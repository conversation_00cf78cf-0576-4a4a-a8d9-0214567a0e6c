<?php
/**
 * CPU Details API
 * Handles operations related to CPU details extraction
 */

// Ensure no output before headers
ob_start();

// Enable error reporting for debugging
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Define debug mode (set to false in production)
define('DEBUG_MODE', false);

// Define secure access constant
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// Include required files
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/../inc/database.php';

// Validate CSRF token for non-GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET' && defined('ENABLE_CSRF_VALIDATION') && ENABLE_CSRF_VALIDATION) {
    // Get CSRF token from various sources
    $csrfToken = null;

    // Check headers
    $headers = getallheaders();
    if (isset($headers['X-CSRF-Token'])) {
        $csrfToken = $headers['X-CSRF-Token'];
    } else if (isset($headers['X-Csrf-Token'])) {
        $csrfToken = $headers['X-Csrf-Token'];
    } else if (isset($headers['x-csrf-token'])) {
        $csrfToken = $headers['x-csrf-token'];
    }

    // Check POST data
    if ($csrfToken === null && isset($_POST['csrf_token'])) {
        $csrfToken = $_POST['csrf_token'];
    }

    // Check GET parameters
    if ($csrfToken === null && isset($_GET['csrf_token'])) {
        $csrfToken = $_GET['csrf_token'];
    }

    // Validate token
    if (!isset($_SESSION['csrf_token']) || $csrfToken !== $_SESSION['csrf_token']) {
        // Clean any output buffer
        ob_clean();

        // Return error response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Invalid CSRF token',
            'code' => 'INVALID_CSRF'
        ]);
        exit;
    }
}

// Create CPU details service
try {
    $cpuDetailsService = new App\Services\CpuDetailsService();
} catch (\Exception $e) {
    // Clean any output buffer
    ob_clean();

    // Log the error
    error_log("Error creating CpuDetailsService: " . $e->getMessage());

    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Error initializing CPU details service: ' . $e->getMessage(),
        'debug_output' => DEBUG_MODE ? $e->getTraceAsString() : null
    ]);
    exit;
}

// Handle API requests
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            // Get CPU details
            $result = $cpuDetailsService->getCpuDetails();
            // Clean any output buffer
            ob_clean();
            header('Content-Type: application/json');
            echo json_encode($result);
            break;

        case 'POST':
            // Check if this is an update request
            if (isset($_POST['action']) && $_POST['action'] === 'update') {
                // Validate required parameters
                if (!isset($_POST['id'])) {
                    ob_clean();
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => 'Missing CPU ID'
                    ]);
                    break;
                }

                // Get parameters
                $id = $_POST['id']; // Don't cast to int, as original_id might be a string
                $brandTag = isset($_POST['brand_tag']) ? trim($_POST['brand_tag']) : '';
                $seriesTag = isset($_POST['series_tag']) ? trim($_POST['series_tag']) : '';
                $coreModelTag = isset($_POST['core_model_tag']) ? trim($_POST['core_model_tag']) : '';

                // Update CPU details
                $result = $cpuDetailsService->updateCpuDetails($id, $brandTag, $seriesTag, $coreModelTag);
            } else {
                // Legacy support for generate request (now disabled)
                $result = $cpuDetailsService->generateCpuDetails();
            }

            // Clean any output buffer
            ob_clean();
            header('Content-Type: application/json');
            echo json_encode($result);
            break;

        default:
            // Method not allowed
            // Clean any output buffer
            ob_clean();
            header('HTTP/1.1 405 Method Not Allowed');
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
            break;
    }
} catch (\Exception $e) {
    // Clean any output buffer
    ob_clean();

    // Log the error
    error_log("Error in CPU details API: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred: ' . $e->getMessage(),
        'debug_output' => DEBUG_MODE ? $e->getTraceAsString() : null
    ]);
}
