<?php

// Define secure access constant to protect included files
define('SECURE_ACCESS', true);

// Include configuration and common files
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../inc/database.php';
require_once __DIR__ . '/../inc/auth.php';
require_once __DIR__ . '/../inc/session.php';

// Include the autoloader
require_once '../vendor/autoload.php';

use App\Services\AmazonSearchTermService;

// Set content type to JSON
header('Content-Type: application/json');

// Check authentication
if (!isAuthenticated()) {
    echo json_encode(['success' => false, 'message' => 'Authentication required.']);
    exit;
}

// Verify CSRF token for POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        echo json_encode(['success' => false, 'message' => 'CSRF token validation failed.']);
        exit;
    }
}

$action = $_GET['action'] ?? ($_POST['action'] ?? null);
$response = ['success' => false, 'message' => 'Invalid action.'];

try {
    $pdo = getDatabase();
    $amazonSearchTermService = new AmazonSearchTermService($pdo);

    switch ($action) {
        case 'get_all':
            $terms = $amazonSearchTermService->getAllSearchTerms();
            $response = ['success' => true, 'data' => $terms];
            break;

        case 'generate':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $result = $amazonSearchTermService->generateAndStoreSearchTerms();
                $response = $result; // The service method already returns success, message, and count
            } else {
                $response = ['success' => false, 'message' => 'Invalid request method for generate action.'];
            }
            break;

        case 'update_product_count':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // Validate required parameters
                if (!isset($_POST['id']) || !isset($_POST['product_count'])) {
                    $response = ['success' => false, 'message' => 'Missing required parameters: id and product_count.'];
                    break;
                }

                $id = (int)$_POST['id'];
                $productCount = (int)$_POST['product_count'];

                // Validate values
                if ($id <= 0) {
                    $response = ['success' => false, 'message' => 'Invalid ID provided.'];
                    break;
                }

                if ($productCount < 0) {
                    $response = ['success' => false, 'message' => 'Product count cannot be negative.'];
                    break;
                }

                $result = $amazonSearchTermService->updateProductCount($id, $productCount);
                $response = $result;
            } else {
                $response = ['success' => false, 'message' => 'Invalid request method for update_product_count action.'];
            }
            break;

        case 'add_manual':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // Validate required parameters
                if (!isset($_POST['generated_search_term']) || empty(trim($_POST['generated_search_term']))) {
                    $response = ['success' => false, 'message' => 'Search term cannot be empty.'];
                    break;
                }

                $brandTag = isset($_POST['brand_tag']) ? trim($_POST['brand_tag']) : null;
                $keywords = isset($_POST['keywords']) ? trim($_POST['keywords']) : null;
                $generatedSearchTerm = trim($_POST['generated_search_term']);

                $result = $amazonSearchTermService->addManualSearchTerm($brandTag, $keywords, $generatedSearchTerm);
                $response = $result;
            } else {
                $response = ['success' => false, 'message' => 'Invalid request method for add_manual action.'];
            }
            break;

        case 'update_manual':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // Validate required parameters
                if (!isset($_POST['id']) || !isset($_POST['generated_search_term']) || empty(trim($_POST['generated_search_term']))) {
                    $response = ['success' => false, 'message' => 'ID and search term are required.'];
                    break;
                }

                $id = (int)$_POST['id'];
                $brandTag = isset($_POST['brand_tag']) ? trim($_POST['brand_tag']) : null;
                $keywords = isset($_POST['keywords']) ? trim($_POST['keywords']) : null;
                $generatedSearchTerm = trim($_POST['generated_search_term']);

                // Validate values
                if ($id <= 0) {
                    $response = ['success' => false, 'message' => 'Invalid ID provided.'];
                    break;
                }

                $result = $amazonSearchTermService->updateManualSearchTerm($id, $brandTag, $keywords, $generatedSearchTerm);
                $response = $result;
            } else {
                $response = ['success' => false, 'message' => 'Invalid request method for update_manual action.'];
            }
            break;

        case 'toggle_active':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // Validate required parameters
                if (!isset($_POST['id'])) {
                    $response = ['success' => false, 'message' => 'ID is required.'];
                    break;
                }

                $id = (int)$_POST['id'];

                // Validate values
                if ($id <= 0) {
                    $response = ['success' => false, 'message' => 'Invalid ID provided.'];
                    break;
                }

                $result = $amazonSearchTermService->toggleSearchTermActive($id);
                $response = $result;
            } else {
                $response = ['success' => false, 'message' => 'Invalid request method for toggle_active action.'];
            }
            break;

        case 'get_by_id':
            if (isset($_GET['id'])) {
                $id = (int)$_GET['id'];

                // Validate values
                if ($id <= 0) {
                    $response = ['success' => false, 'message' => 'Invalid ID provided.'];
                    break;
                }

                $result = $amazonSearchTermService->getSearchTermById($id);
                $response = $result;
            } else {
                $response = ['success' => false, 'message' => 'ID is required.'];
            }
            break;

        case 'delete':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // Validate required parameters
                if (!isset($_POST['id'])) {
                    $response = ['success' => false, 'message' => 'ID is required.'];
                    break;
                }

                $id = (int)$_POST['id'];

                // Validate values
                if ($id <= 0) {
                    $response = ['success' => false, 'message' => 'Invalid ID provided.'];
                    break;
                }

                $result = $amazonSearchTermService->deleteSearchTerm($id);
                $response = $result;
            } else {
                $response = ['success' => false, 'message' => 'Invalid request method for delete action.'];
            }
            break;

        default:
            $response = ['success' => false, 'message' => 'Unknown action specified.'];
            break;
    }
} catch (\Exception $e) {
    error_log("API Error (amazon-search-terms.php): " . $e->getMessage());
    $response = ['success' => false, 'message' => 'An unexpected error occurred: ' . $e->getMessage()];
}

echo json_encode($response);
