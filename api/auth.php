<?php
/**
 * API Authentication Helper
 * Simple authentication check for API endpoints
 */

// Define secure access constant to protect included files
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// Include configuration
require_once __DIR__ . '/../config/config.php';

// Include session handling
require_once __DIR__ . '/../inc/session.php';

/**
 * Check if the request is authenticated
 *
 * @return bool True if authenticated, false otherwise
 */
function isApiAuthenticated() {
    // Simple check for logged_in session variable
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

/**
 * Send unauthorized response and exit
 */
function sendUnauthorizedResponse() {
    // Clean any output buffer
    if (ob_get_level()) ob_clean();

    // Set content type to JSON
    header('Content-Type: application/json');

    // Send unauthorized response
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access',
        'code' => 'SESSION_EXPIRED'
    ]);

    // Exit to prevent further execution
    exit;
}

/**
 * Update last activity time
 */
function updateLastActivity() {
    $_SESSION['last_activity'] = time();
}

// Check if this file is being included in an API endpoint
if (basename($_SERVER['SCRIPT_NAME']) !== 'auth.php') {
    // Check if user is authenticated
    if (!isApiAuthenticated()) {
        sendUnauthorizedResponse();
    }

    // Update last activity time
    updateLastActivity();
}
