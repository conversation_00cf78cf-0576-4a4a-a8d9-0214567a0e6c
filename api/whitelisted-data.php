<?php
/**
 * Whitelisted Data API
 *
 * Handles requests for generating and retrieving the whitelisted CPU data
 */

// Define secure access constant to protect included files
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// Include authentication, configuration and database connection
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/../inc/database.php';

// Validate CSRF token for non-GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET' && defined('ENABLE_CSRF_VALIDATION') && ENABLE_CSRF_VALIDATION) {
    // Get CSRF token from various sources
    $csrfToken = null;
    
    // Check headers
    $headers = getallheaders();
    if (isset($headers['X-CSRF-Token'])) {
        $csrfToken = $headers['X-CSRF-Token'];
    } else if (isset($headers['X-Csrf-Token'])) {
        $csrfToken = $headers['X-Csrf-Token'];
    } else if (isset($headers['x-csrf-token'])) {
        $csrfToken = $headers['x-csrf-token'];
    }
    
    // Check POST data
    if ($csrfToken === null && isset($_POST['csrf_token'])) {
        $csrfToken = $_POST['csrf_token'];
    }
    
    // Check GET parameters
    if ($csrfToken === null && isset($_GET['csrf_token'])) {
        $csrfToken = $_GET['csrf_token'];
    }
    
    // Validate token
    if (!isset($_SESSION['csrf_token']) || $csrfToken !== $_SESSION['csrf_token']) {
        // Return error response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Invalid CSRF token',
            'code' => 'INVALID_CSRF'
        ]);
        exit;
    }
}

// Set content type to JSON
header('Content-Type: application/json');

// Initialize database connection
$db = getDatabase();

// Create the whitelisted data service
$finalDataService = new App\Services\FinalDataService($db);

// Process request based on method and action
try {
    // GET requests
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = isset($_GET['action']) ? $_GET['action'] : 'get';

        switch ($action) {
            case 'get':
                // Get the whitelisted data
                $result = $finalDataService->getFinalData();
                echo json_encode($result);
                break;

            case 'generate':
                // Generate and save the whitelisted data
                $result = $finalDataService->generateAndSaveFinalData();
                echo json_encode($result);
                break;

            case 'import':
                // Import CPU data from JSON file
                $jsonFile = isset($_GET['file']) ? $_GET['file'] : 'cpu_models.json';
                $result = $finalDataService->importCpuDataFromJson($jsonFile);
                echo json_encode($result);
                break;

            default:
                throw new Exception('Invalid action');
        }
    } else {
        throw new Exception('Invalid request method');
    }
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
