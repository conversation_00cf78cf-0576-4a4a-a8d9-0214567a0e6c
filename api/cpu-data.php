<?php
/**
 * CPU Data API
 * Handles AJAX requests for CPU data operations
 */

// Prevent any output before headers
if (ob_get_level()) ob_end_clean();
ob_start();

// Prevent PHP errors from being output
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Define secure access constant to protect included files
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// Include the autoloader
require_once '../vendor/autoload.php';

// Use the required namespaces
use App\Services\CpuDataService;
use App\Utils\DatabaseUtils;

// Initialize response array
$response = [
    'success' => false,
    'message' => 'Unknown error occurred'
];

try {
    // Include configuration and required files
    require_once '../config/config.php';
    require_once '../inc/session.php';
    require_once '../inc/auth.php';

    // Check if user is logged in
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        $response = [
            'success' => false,
            'message' => 'Unauthorized access',
            'code' => 'SESSION_EXPIRED'
        ];
    } else {
        // Use CSRF validation setting from config
        // This can be controlled via the .env file
        if (ENABLE_CSRF_VALIDATION && isset($_SESSION['csrf_token'])) {
            $headers = getallheaders();

            // Try to get raw request body for JSON requests
            $rawBody = file_get_contents('php://input');
            $jsonBody = json_decode($rawBody, true);

            // Check for CSRF token in headers, cookies, or POST data
            $csrfToken = '';

            // 1. Check headers (X-CSRF-Token)
            if (isset($headers['X-CSRF-Token'])) {
                $csrfToken = $headers['X-CSRF-Token'];
            }
            // 2. Check case-insensitive headers (some servers normalize header names)
            else if (isset($headers['X-Csrf-Token'])) {
                $csrfToken = $headers['X-Csrf-Token'];
            }
            // 3. Check for x-csrf-token (lowercase)
            else if (isset($headers['x-csrf-token'])) {
                $csrfToken = $headers['x-csrf-token'];
            }
            // 4. Check POST data
            else if (isset($_POST['csrf_token'])) {
                $csrfToken = $_POST['csrf_token'];
            }
            // 5. Check GET data (not recommended but as fallback)
            else if (isset($_GET['csrf_token'])) {
                $csrfToken = $_GET['csrf_token'];
            }
            // 6. Check JSON body
            else if ($jsonBody !== null && isset($jsonBody['csrf_token'])) {
                $csrfToken = $jsonBody['csrf_token'];
            }

            // Check if token is empty
            if (empty($csrfToken)) {
                $response = [
                    'success' => false,
                    'message' => 'CSRF token missing'
                ];
                // Skip further processing
                throw new Exception('CSRF token missing');
            }

            // Check if token matches
            // The received token might contain commas if multiple headers were set
            // Split the token by commas and check if any part matches the session token
            $tokenParts = explode(',', $csrfToken);
            $tokenMatched = false;

            foreach ($tokenParts as $tokenPart) {
                $trimmedToken = trim($tokenPart);
                if ($trimmedToken === $_SESSION['csrf_token']) {
                    $tokenMatched = true;
                    break;
                }
            }

            if (!$tokenMatched) {
                $response = [
                    'success' => false,
                    'message' => 'Invalid CSRF token'
                ];
                // Skip further processing
                throw new Exception('CSRF validation failed');
            }
        }

        // Get request method and action
        $method = $_SERVER['REQUEST_METHOD'];
        $action = isset($_GET['action']) ? $_GET['action'] : '';

        // Get database connection
        $db = DatabaseUtils::getConnection();
        
        // Create CPU data service instance with database connection
        $cpuDataService = new CpuDataService($db);

        // Process request based on method and action
        switch ($method) {
            case 'GET':
                switch ($action) {
                    case 'count':
                        $count = $cpuDataService->getCpuModelsCount();
                        $response = [
                            'success' => true,
                            'count' => $count
                        ];
                        break;

                    case 'list':
                        $data = $cpuDataService->getCpuModels();

                        // Extract CPU models from the data structure
                        $cpuModels = [];
                        if (isset($data['data']) && is_array($data['data'])) {
                            $cpuModels = $data['data'];
                        } else if (is_array($data)) {
                            $cpuModels = $data;
                        }

                        // Create a simplified version with only the requested fields
                        $simplifiedModels = [];
                        foreach ($cpuModels as $model) {
                            // Trim the socket name to remove any leading/trailing spaces
                            $socketName = isset($model['socket']) ? trim($model['socket']) : 'Unknown';

                            $simplifiedModel = [
                                'id' => $model['id'] ?? '',
                                'name' => $model['name'] ?? '',
                                'cpumark' => $model['cpumark'] ?? 'NA',
                                'tdp' => $model['tdp'] ?? 'NA',
                                'socket' => $socketName,
                                'cat' => $model['cat'] ?? '',
                                'cpuCount' => $model['cpuCount'] ?? 1,
                                'cores' => $model['cores'] ?? '',
                                'secondaryCores' => $model['secondaryCores'] ?? '0'
                            ];
                            $simplifiedModels[] = $simplifiedModel;
                        }

                        $response = [
                            'success' => true,
                            'data' => $simplifiedModels
                        ];
                        break;

                    default:
                        $response = [
                            'success' => false,
                            'message' => 'Invalid action'
                        ];
                        break;
                }
                break;

            case 'POST':
                switch ($action) {
                    case 'update':
                        // Get JSON data from request body or POST
                        $jsonContent = '';

                        // Check for POST data with cpu-data-json field (from form)
                        if (isset($_POST['cpu-data-json'])) {
                            $jsonContent = $_POST['cpu-data-json'];
                        }
                        // Check for POST data with cpu_data field (legacy)
                        else if (isset($_POST['cpu_data'])) {
                            $jsonContent = $_POST['cpu_data'];
                        }
                        // Get raw input (from fetch API)
                        else {
                            $jsonContent = file_get_contents('php://input');
                        }

                        // Log the received data for debugging
                        error_log('CPU Data Update - Received data: ' . substr($jsonContent, 0, 100) . '...');

                        if (empty($jsonContent)) {
                            $response = [
                                'success' => false,
                                'message' => 'No CPU data provided'
                            ];
                            break;
                        }

                        $response = $cpuDataService->updateCpuModelsFromJson($jsonContent);
                        break;

                    default:
                        $response = [
                            'success' => false,
                            'message' => 'Invalid action'
                        ];
                        break;
                }
                break;

            default:
                $response = [
                    'success' => false,
                    'message' => 'Method not allowed'
                ];
                break;
        }
    }
} catch (Exception $e) {
    // Handle any exceptions
    $response = [
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ];
}

// End output buffering and discard any unexpected output
$output = ob_get_clean();

// Set content type to JSON
header('Content-Type: application/json');

// If there was unexpected output, include it in the response for debugging
if (!empty($output) && trim($output) !== '') {
    $response['debug_output'] = $output;
}

// Send the JSON response
echo json_encode($response);
exit;
