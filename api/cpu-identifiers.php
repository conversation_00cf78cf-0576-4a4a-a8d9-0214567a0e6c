<?php
/**
 * CPU Identifiers API
 * Handles operations related to CPU identifiers
 */

// Ensure no output before headers
ob_start();

// Enable error reporting for debugging
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Define debug mode (set to false in production)
define('DEBUG_MODE', false);

// Define secure access constant
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// Include required files
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/../inc/database.php';

// Validate CSRF token for non-GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET' && defined('ENABLE_CSRF_VALIDATION') && ENABLE_CSRF_VALIDATION) {
    // Get CSRF token from various sources
    $csrfToken = null;

    // Check headers
    $headers = getallheaders();
    if (isset($headers['X-CSRF-Token'])) {
        $csrfToken = $headers['X-CSRF-Token'];
    } else if (isset($headers['X-Csrf-Token'])) {
        $csrfToken = $headers['X-Csrf-Token'];
    } else if (isset($headers['x-csrf-token'])) {
        $csrfToken = $headers['x-csrf-token'];
    }

    // Check POST data
    if ($csrfToken === null && isset($_POST['csrf_token'])) {
        $csrfToken = $_POST['csrf_token'];
    }

    // Check GET parameters
    if ($csrfToken === null && isset($_GET['csrf_token'])) {
        $csrfToken = $_GET['csrf_token'];
    }

    // Validate token
    if (!isset($_SESSION['csrf_token']) || $csrfToken !== $_SESSION['csrf_token']) {
        // Clean any output buffer
        ob_clean();

        // Return error response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Invalid CSRF token',
            'code' => 'INVALID_CSRF'
        ]);
        exit;
    }
}

// Create CPU identifier service
try {
    $cpuIdentifierService = new App\Services\CpuIdentifierService();
} catch (\Exception $e) {
    // Clean any output buffer
    ob_clean();

    // Log the error
    error_log("Error creating CpuIdentifierService: " . $e->getMessage());

    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Error initializing CPU identifier service: ' . $e->getMessage(),
        'debug_output' => DEBUG_MODE ? $e->getTraceAsString() : null
    ]);
    exit;
}

// Handle API requests
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            // Check if this is a generate request
            if (isset($_GET['action']) && $_GET['action'] === 'generate') {
                // Check if we should generate for all CPUs
                if (isset($_GET['all']) && $_GET['all'] === 'true') {
                    $result = $cpuIdentifierService->generateAllIdentifiers();
                } else {
                    // Validate required parameters
                    if (!isset($_GET['id'])) {
                        ob_clean();
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false,
                            'message' => 'Missing CPU ID'
                        ]);
                        break;
                    }

                    // Get parameters
                    $id = $_GET['id'];

                    // Generate identifiers
                    $result = $cpuIdentifierService->generateIdentifiers($id);
                }
            } else {
                // Get identifiers for a specific CPU
                if (!isset($_GET['id'])) {
                    ob_clean();
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => 'Missing CPU ID'
                    ]);
                    break;
                }

                // Get parameters
                $id = $_GET['id'];

                // Get identifiers
                $result = $cpuIdentifierService->getIdentifiers($id);
            }

            // Clean any output buffer
            ob_clean();
            header('Content-Type: application/json');
            echo json_encode($result);
            break;

        default:
            // Method not allowed
            // Clean any output buffer
            ob_clean();
            header('HTTP/1.1 405 Method Not Allowed');
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
            break;
    }
} catch (\Exception $e) {
    // Clean any output buffer
    ob_clean();

    // Log the error
    error_log("Error in CPU identifiers API: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred: ' . $e->getMessage(),
        'debug_output' => DEBUG_MODE ? $e->getTraceAsString() : null
    ]);
}
