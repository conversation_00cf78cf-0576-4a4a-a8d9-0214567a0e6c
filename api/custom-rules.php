<?php
/**
 * Custom Rules API
 *
 * Handles CRUD operations for custom CPU data rules
 */

// Define SECURE_ACCESS to allow including protected files
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// Include required files
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/../inc/database.php';

// Initialize database connection
$db = getDatabase();

// Initialize custom rule service
$customRuleService = new App\Services\CustomRuleService($db);

// Set content type to JSON
header('Content-Type: application/json');

// Check if request method is allowed
$allowedMethods = ['GET', 'POST', 'PUT', 'DELETE'];
if (!in_array($_SERVER['REQUEST_METHOD'], $allowedMethods)) {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

// Validate CSRF token
$csrfToken = null;

// Check for token in headers
if (isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
    $csrfToken = $_SERVER['HTTP_X_CSRF_TOKEN'];
}
// Check for token in GET parameters
else if (isset($_GET['csrf_token'])) {
    $csrfToken = $_GET['csrf_token'];
}
// Check for token in POST parameters
else if (isset($_POST['csrf_token'])) {
    $csrfToken = $_POST['csrf_token'];
}

// Get request body for PUT and DELETE methods
if ($_SERVER['REQUEST_METHOD'] === 'PUT' || $_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $requestBody = file_get_contents('php://input');
    $requestData = json_decode($requestBody, true);

    if (isset($requestData['csrf_token'])) {
        $csrfToken = $requestData['csrf_token'];
    }
}

// Validate CSRF token if enabled in config
if (defined('ENABLE_CSRF_VALIDATION') && ENABLE_CSRF_VALIDATION) {
    if (!isset($_SESSION['csrf_token']) || $csrfToken !== $_SESSION['csrf_token']) {
        // Log the error
        error_log('CSRF Validation Failed: Token in request: ' . $csrfToken . ', Token in session: ' . ($_SESSION['csrf_token'] ?? 'not set'));

        http_response_code(403);
        echo json_encode([
            'success' => false,
            'message' => 'Invalid CSRF token',
            'debug' => [
                'request_token' => $csrfToken,
                'session_token' => $_SESSION['csrf_token'] ?? 'not set',
                'session_id' => session_id(),
                'session_status' => session_status()
            ]
        ]);
        exit;
    }
}

// Process request based on method and action
try {
    // Log the request method and data
    error_log('Custom Rules API - Request Method: ' . $_SERVER['REQUEST_METHOD']);

    // GET requests
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = isset($_GET['action']) ? $_GET['action'] : 'list';

        switch ($action) {
            case 'list':
                $rules = $customRuleService->getAllRules();
                echo json_encode([
                    'success' => true,
                    'data' => $rules
                ]);
                break;

            case 'get':
                if (!isset($_GET['id'])) {
                    throw new Exception('Rule ID is required');
                }

                $rule = $customRuleService->getRuleById($_GET['id']);

                if (!$rule) {
                    throw new Exception('Rule not found');
                }

                echo json_encode([
                    'success' => true,
                    'data' => $rule
                ]);
                break;

            default:
                throw new Exception('Invalid action');
        }
    }
    // POST requests (create)
    else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Check if this is actually a PUT request using POST method
        if (isset($_POST['_method']) && $_POST['_method'] === 'PUT') {
            error_log('Custom Rules API - Handling PUT request via POST method');

            // This is actually a PUT request (update)
            if (!isset($_POST['id'])) {
                error_log('Custom Rules API - Missing ID in PUT-via-POST request');
                throw new Exception('Rule ID is required');
            }

            // Handle special case for is_active which might be a string 'true' or 'false'
            $isActive = null;
            if (isset($_POST['is_active'])) {
                if ($_POST['is_active'] === 'true' || $_POST['is_active'] === '1' || $_POST['is_active'] === 1) {
                    $isActive = 1;
                } else if ($_POST['is_active'] === 'false' || $_POST['is_active'] === '0' || $_POST['is_active'] === 0) {
                    $isActive = 0;
                } else {
                    $isActive = (int)$_POST['is_active'];
                }
            }

            $data = [
                'name_pattern' => $_POST['name_pattern'] ?? '',
                'field_to_change' => $_POST['field_to_change'] ?? '',
                'new_value' => $_POST['new_value'] ?? '',
                'is_active' => $isActive,
                'match_type' => $_POST['match_type'] ?? null,
                'cpu_count' => isset($_POST['cpu_count']) ? (int)$_POST['cpu_count'] : null,
                'original_id' => $_POST['original_id'] ?? null,
                'action_type' => $_POST['action_type'] ?? null
            ];

            error_log('Custom Rules API - PUT-via-POST Data: ' . json_encode($data));

            $result = $customRuleService->updateRule($_POST['id'], $data);
            error_log('Custom Rules API - updateRule result (PUT-via-POST): ' . json_encode($result));
            echo json_encode($result);
            return;
        }

        $action = isset($_POST['action']) ? $_POST['action'] : 'create';

        switch ($action) {
            case 'create':
                // Handle special case for is_active which might be a string 'true' or 'false'
                $isActive = 1; // Default to active for new rules
                if (isset($_POST['is_active'])) {
                    if ($_POST['is_active'] === 'true' || $_POST['is_active'] === '1' || $_POST['is_active'] === 1) {
                        $isActive = 1;
                    } else if ($_POST['is_active'] === 'false' || $_POST['is_active'] === '0' || $_POST['is_active'] === 0) {
                        $isActive = 0;
                    } else {
                        $isActive = (int)$_POST['is_active'];
                    }
                }

                $data = [
                    'name_pattern' => $_POST['name_pattern'] ?? '',
                    'field_to_change' => $_POST['field_to_change'] ?? '',
                    'new_value' => $_POST['new_value'] ?? '',
                    'is_active' => $isActive,
                    'match_type' => $_POST['match_type'] ?? 'name',
                    'cpu_count' => isset($_POST['cpu_count']) ? (int)$_POST['cpu_count'] : null,
                    'original_id' => $_POST['original_id'] ?? null,
                    'action_type' => $_POST['action_type'] ?? 'update'
                ];

                $result = $customRuleService->createRule($data);
                echo json_encode($result);
                break;

            case 'update':
                // Check required fields
                if (!isset($_POST['id'])) {
                    throw new Exception('Rule ID is required');
                }

                // Get the rule ID
                $id = (int)$_POST['id'];

                // Handle special case for is_active which might be a string 'true' or 'false'
                $isActive = 0;
                if (isset($_POST['is_active'])) {
                    if ($_POST['is_active'] === 'true' || $_POST['is_active'] === '1' || $_POST['is_active'] === 1) {
                        $isActive = 1;
                    } else if ($_POST['is_active'] === 'false' || $_POST['is_active'] === '0' || $_POST['is_active'] === 0) {
                        $isActive = 0;
                    } else {
                        $isActive = (int)$_POST['is_active'];
                    }
                }

                // Prepare variables with proper types
                $data = [
                    'name_pattern' => $_POST['name_pattern'] ?? '',
                    'field_to_change' => $_POST['field_to_change'] ?? '',
                    'new_value' => $_POST['new_value'] ?? '',
                    'is_active' => $isActive,
                    'match_type' => $_POST['match_type'] ?? 'name',
                    'cpu_count' => isset($_POST['cpu_count']) && $_POST['cpu_count'] !== '' ? (int)$_POST['cpu_count'] : null,
                    'original_id' => $_POST['original_id'] ?? null,
                    'action_type' => $_POST['action_type'] ?? 'update'
                ];

                $result = $customRuleService->updateRule($id, $data);
                echo json_encode($result);
                break;

            case 'toggle':
                if (!isset($_POST['id'])) {
                    throw new Exception('Rule ID is required');
                }

                // Handle special case for is_active which might be a string 'true' or 'false'
                $isActive = false; // Default to inactive
                if (isset($_POST['is_active'])) {
                    if ($_POST['is_active'] === 'true' || $_POST['is_active'] === '1' || $_POST['is_active'] === 1) {
                        $isActive = true;
                    } else if ($_POST['is_active'] === 'false' || $_POST['is_active'] === '0' || $_POST['is_active'] === 0) {
                        $isActive = false;
                    } else {
                        $isActive = (bool)$_POST['is_active'];
                    }
                }

                error_log('Custom Rules API - Toggle action, is_active: ' . ($isActive ? 'true' : 'false'));
                $result = $customRuleService->toggleRuleActive($_POST['id'], $isActive);
                echo json_encode($result);
                break;

            default:
                throw new Exception('Invalid action');
        }
    }
    // PUT requests (update)
    else if ($_SERVER['REQUEST_METHOD'] === 'PUT' || ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['_method']) && $_POST['_method'] === 'PUT')) {
        // Try multiple ways to get the request body
        $requestBody = file_get_contents('php://input');
        error_log('Custom Rules API - PUT Request Body: ' . $requestBody);

        // Check if the request body is empty, which might happen on some servers
        if (empty($requestBody)) {
            error_log('Custom Rules API - Empty request body, trying to use $_POST');
            // Try to use $_POST as a fallback
            $requestData = $_POST;
        } else {
            // Parse the JSON request body
            $requestData = json_decode($requestBody, true);
        }

        // Check if we have valid data
        if ($requestData === null) {
            error_log('Custom Rules API - Failed to parse request data');
            throw new Exception('Invalid request data');
        }

        error_log('Custom Rules API - PUT Request Data: ' . json_encode($requestData));

        if (!isset($requestData['id'])) {
            error_log('Custom Rules API - Missing ID in PUT request');
            throw new Exception('Rule ID is required');
        }

        // Handle special case for is_active which might be a string 'true' or 'false'
        $isActive = null;
        if (isset($requestData['is_active'])) {
            if ($requestData['is_active'] === true || $requestData['is_active'] === 'true' || $requestData['is_active'] === '1' || $requestData['is_active'] === 1) {
                $isActive = 1;
            } else if ($requestData['is_active'] === false || $requestData['is_active'] === 'false' || $requestData['is_active'] === '0' || $requestData['is_active'] === 0) {
                $isActive = 0;
            } else {
                $isActive = (int)$requestData['is_active'];
            }
        }

        $data = [
            'name_pattern' => $requestData['name_pattern'] ?? '',
            'field_to_change' => $requestData['field_to_change'] ?? '',
            'new_value' => $requestData['new_value'] ?? '',
            'is_active' => $isActive,
            'match_type' => $requestData['match_type'] ?? null,
            'cpu_count' => isset($requestData['cpu_count']) ? (int)$requestData['cpu_count'] : null,
            'original_id' => $requestData['original_id'] ?? null,
            'action_type' => $requestData['action_type'] ?? null
        ];

        error_log('Custom Rules API - Data for updateRule: ' . json_encode($data));

        $result = $customRuleService->updateRule($requestData['id'], $data);
        error_log('Custom Rules API - updateRule result: ' . json_encode($result));
        echo json_encode($result);
    }
    // DELETE requests
    else if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        $requestBody = file_get_contents('php://input');
        $requestData = json_decode($requestBody, true);

        if (!isset($requestData['id'])) {
            throw new Exception('Rule ID is required');
        }

        $result = $customRuleService->deleteRule($requestData['id']);
        echo json_encode($result);
    }
} catch (\Exception $e) {
    // Log the error with detailed information
    error_log('Custom Rules API Error: ' . $e->getMessage());
    error_log('Custom Rules API Error File: ' . $e->getFile() . ' Line: ' . $e->getLine());
    error_log('Custom Rules API Error Trace: ' . $e->getTraceAsString());

    // Log request information
    error_log('Custom Rules API Error Request Method: ' . $_SERVER['REQUEST_METHOD']);
    if ($_SERVER['REQUEST_METHOD'] === 'PUT' || $_SERVER['REQUEST_METHOD'] === 'DELETE') {
        $requestBody = file_get_contents('php://input');
        error_log('Custom Rules API Error Request Body: ' . $requestBody);
    } else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        error_log('Custom Rules API Error POST Data: ' . json_encode($_POST));
    } else if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        error_log('Custom Rules API Error GET Data: ' . json_encode($_GET));
    }

    // Return detailed error information for debugging
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
}
