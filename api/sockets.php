<?php
/**
 * Socket Management API
 * Handles AJAX requests for socket operations
 */

// Prevent any output before headers
if (ob_get_level()) ob_end_clean();
ob_start();

// Prevent PHP errors from being output
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Define secure access constant to protect included files
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// Include the required classes
require_once '../vendor/autoload.php';

// Use the namespaces
use App\Services\SocketService;

// Initialize response array
$response = [
    'success' => false,
    'message' => 'Unknown error occurred'
];

try {
    // Include configuration and required files
    require_once '../config/config.php';
    require_once '../inc/session.php';
    require_once '../inc/auth.php';

    // Check if user is logged in
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        $response = [
            'success' => false,
            'message' => 'Unauthorized access',
            'code' => 'SESSION_EXPIRED'
        ];
    } else {
        // Use CSRF validation setting from config
        // This can be controlled via the .env file
        if (ENABLE_CSRF_VALIDATION && isset($_SESSION['csrf_token'])) {
            $headers = getallheaders();

            // Try to get raw request body for JSON requests
            $rawBody = file_get_contents('php://input');
            $jsonBody = json_decode($rawBody, true);

            // Check for CSRF token in headers, cookies, or POST data
            $csrfToken = '';

            // 1. Check headers (X-CSRF-Token)
            if (isset($headers['X-CSRF-Token'])) {
                $csrfToken = $headers['X-CSRF-Token'];
            }
            // 2. Check case-insensitive headers (some servers normalize header names)
            else if (isset($headers['X-Csrf-Token'])) {
                $csrfToken = $headers['X-Csrf-Token'];
            }
            // 3. Check for x-csrf-token (lowercase)
            else if (isset($headers['x-csrf-token'])) {
                $csrfToken = $headers['x-csrf-token'];
            }
            // 4. Check POST data
            else if (isset($_POST['csrf_token'])) {
                $csrfToken = $_POST['csrf_token'];
            }
            // 5. Check GET data (not recommended but as fallback)
            else if (isset($_GET['csrf_token'])) {
                $csrfToken = $_GET['csrf_token'];
            }
            // 6. Check JSON body
            else if ($jsonBody !== null && isset($jsonBody['csrf_token'])) {
                $csrfToken = $jsonBody['csrf_token'];
            }

            // Check if token is empty
            if (empty($csrfToken)) {
                $response = [
                    'success' => false,
                    'message' => 'CSRF token missing'
                ];
                // Skip further processing
                throw new Exception('CSRF token missing');
            }

            // Check if token matches
            // The received token might contain commas if multiple headers were set
            // Split the token by commas and check if any part matches the session token
            $tokenParts = explode(',', $csrfToken);
            $tokenMatched = false;

            foreach ($tokenParts as $tokenPart) {
                $trimmedToken = trim($tokenPart);
                if ($trimmedToken === $_SESSION['csrf_token']) {
                    $tokenMatched = true;
                    break;
                }
            }

            if (!$tokenMatched) {
                $response = [
                    'success' => false,
                    'message' => 'Invalid CSRF token'
                ];
                // Skip further processing
                throw new Exception('CSRF validation failed');
            }
        }

        // Create socket service instance
        $socketService = new SocketService();

        // Get request method and action
        $method = $_SERVER['REQUEST_METHOD'];
        $action = isset($_GET['action']) ? $_GET['action'] : '';

        // Process request based on method and action
        switch ($method) {
            case 'GET':
                $response = handleGetRequest($action, $socketService);
                break;

            case 'POST':
                $response = handlePostRequest($action, $socketService);
                break;

            case 'PUT':
                $response = handlePutRequest($action, $socketService);
                break;

            case 'DELETE':
                $response = handleDeleteRequest($action, $socketService);
                break;

            default:
                $response = [
                    'success' => false,
                    'message' => 'Method not allowed'
                ];
                break;
        }
    }
} catch (Exception $e) {
    // Handle any exceptions
    $response = [
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ];
}

// End output buffering and discard any unexpected output
$output = ob_get_clean();

// Set content type to JSON
header('Content-Type: application/json');

// If there was unexpected output, include it in the response for debugging
if (!empty($output) && trim($output) !== '') {
    $response['debug_output'] = $output;
}

// Send the JSON response
echo json_encode($response);
exit;

/**
 * Handle GET requests
 *
 * @param string $action Action to perform
 * @param App\Services\SocketService $socketService Socket service instance
 * @return array Response data
 */
function handleGetRequest($action, $socketService) {
    switch ($action) {
        case 'list':
            // Get all sockets
            return $socketService->getAllSockets();

        case 'get':
            // Get a single socket by ID
            $socketId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

            if ($socketId <= 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid socket ID'
                ];
            }

            return $socketService->getSocketById($socketId);

        default:
            return [
                'success' => false,
                'message' => 'Invalid action'
            ];
    }
}

/**
 * Handle POST requests
 *
 * @param string $action Action to perform
 * @param App\Services\SocketService $socketService Socket service instance
 * @return array Response data
 */
function handlePostRequest($action, $socketService) {
    // Get JSON data from request body
    $jsonData = file_get_contents('php://input');
    $data = json_decode($jsonData, true);

    if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
        return [
            'success' => false,
            'message' => 'Invalid JSON data: ' . json_last_error_msg()
        ];
    }

    switch ($action) {
        case 'add':
            // Add a new socket
            return $socketService->addSocket($data);

        case 'add_variant':
            // Add a new socket variant
            $socketId = isset($data['socket_id']) ? (int)$data['socket_id'] : 0;
            $variantName = isset($data['variant_name']) ? trim($data['variant_name']) : '';

            if ($socketId <= 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid socket ID'
                ];
            }

            if (empty($variantName)) {
                return [
                    'success' => false,
                    'message' => 'Variant name is required'
                ];
            }

            return $socketService->addSocketVariant($socketId, $variantName);

        default:
            return [
                'success' => false,
                'message' => 'Invalid action'
            ];
    }
}

/**
 * Handle PUT requests
 *
 * @param string $action Action to perform
 * @param App\Services\SocketService $socketService Socket service instance
 * @return array Response data
 */
function handlePutRequest($action, $socketService) {
    // Get JSON data from request body
    $jsonData = file_get_contents('php://input');
    $data = json_decode($jsonData, true);

    if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
        return [
            'success' => false,
            'message' => 'Invalid JSON data: ' . json_last_error_msg()
        ];
    }

    switch ($action) {
        case 'update':
            // Update an existing socket
            $socketId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

            if ($socketId <= 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid socket ID'
                ];
            }

            return $socketService->updateSocket($socketId, $data);

        case 'update_variant':
            // Update a socket variant
            $variantId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
            $variantName = isset($data['variant_name']) ? trim($data['variant_name']) : '';

            if ($variantId <= 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid variant ID'
                ];
            }

            if (empty($variantName)) {
                return [
                    'success' => false,
                    'message' => 'Variant name is required'
                ];
            }

            return $socketService->updateSocketVariant($variantId, $variantName);

        default:
            return [
                'success' => false,
                'message' => 'Invalid action'
            ];
    }
}

/**
 * Handle DELETE requests
 *
 * @param string $action Action to perform
 * @param App\Services\SocketService $socketService Socket service instance
 * @return array Response data
 */
function handleDeleteRequest($action, $socketService) {
    switch ($action) {
        case 'delete':
            // Delete a socket
            $socketId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

            if ($socketId <= 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid socket ID'
                ];
            }

            return $socketService->deleteSocket($socketId);

        case 'delete_variant':
            // Delete a socket variant
            $variantId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

            if ($variantId <= 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid variant ID'
                ];
            }

            return $socketService->deleteSocketVariant($variantId);

        default:
            return [
                'success' => false,
                'message' => 'Invalid action'
            ];
    }
}
