<?php
/**
 * This script compresses the CPU data by removing unnecessary fields
 * and transforming the category values for better storage efficiency.
 *
 * 1. Takes /input/cpus.json and compresses it to /data/cpus.json
 * 2. Keeps essential fields like name, cpumark, socket, category, cores, etc.
 * 3. Doesn't keep if socket === 'Unknown' or cat === 'Unknown'
 * 4. Processes Performance (P) and Efficiency (E) cores for hybrid CPUs
 * 5. Creates a core_display field in the format "XP+YE" for hybrid CPUs
 * 6. Keeps the JSON size as small as possible
 */

// Define paths with more flexibility
$base_dir = __DIR__ . '/..';
$input_file = $base_dir . '/input/cpus.json';
$output_dir = $base_dir . '/data';

// Check if the file exists at the expected path
if (!file_exists($input_file)) {
    echo "Error: File not found at: $input_file<br>";
    exit(1);
}

// Read the JSON file
echo "Reading from: $input_file<br>";
$json_data = file_get_contents($input_file);
$cpu_data_raw = json_decode($json_data, true);

// Check if the data is in a nested structure
$cpu_data = isset($cpu_data_raw['data']) ? $cpu_data_raw['data'] : $cpu_data_raw;

// Read the sockets JSON file
$socket_data = json_decode(file_get_contents($base_dir . '/../data/sockets.json'), true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "Error parsing JSON: " . json_last_error_msg() . "<br>";
    exit(1);
}

echo "Found " . count($cpu_data) . " total entries in the JSON file<br>";

// Function to transform socket values
function transform_socket($socket) {
    global $socket_data;

    // Direct lookup in variants map
    if (isset($socket_data['variants_map'][$socket])) {
        return [
            'name' => $socket_data['variants_map'][$socket]['name'],
            'slug' => $socket_data['variants_map'][$socket]['slug'],
            'whitelist' => true
        ];
    } else {
        return [
            'whitelist' => false
        ];
    }
}

// Function to transform category values
function transform_category($category) {
    if (empty($category)) {
        return '';
    }

    // Split by comma if multiple categories
    $categories = explode(', ', $category);
    $transformed = [];

    foreach ($categories as $cat) {
        switch (trim($cat)) {
            case 'Mobile/Embedded':
                $transformed[] = 'mobile';
                break;
            case 'Desktop':
                $transformed[] = 'desktop';
                break;
            case 'Laptop':
                $transformed[] = 'laptop';
                break;
            case 'Server':
                $transformed[] = 'server';
                break;
            default:
                // Keep original if not matching any of the above
                $transformed[] = trim($cat);
        }
    }

    // Join back with commas
    return implode(',', $transformed);
}

$compressed_data = [];

foreach ($cpu_data as $cpu) {
    // Check if required keys exist
    if (!isset($cpu['socket']) || !isset($cpu['cat'])) {
        continue;
    }

    // Manual data fixes
    if ($cpu['name'] === 'Intel Celeron G3900T @ 2.60GHz') {
        $cpu['socket'] = 'LGA1151';
    }

    // Fix for AMD Opteron 6284 SE - it's a single CPU with 16 cores, not a dual-CPU configuration
    if ($cpu['id'] === '2702' && $cpu['name'] === 'AMD Opteron 6284 SE') {
        $cpu['cpuCount'] = 1;
    }

    // Skip rows with unknown or empty values
    if ($cpu['socket'] === 'Unknown' || $cpu['socket'] === '' ||$cpu['cat'] === 'Unknown' || $cpu['cat'] === '') {
        continue;
    }

    // Transform the socket
    $transformed_socket = transform_socket($cpu['socket']);

    // Transform the category
    $transformed_category = transform_category($cpu['cat']);

    // Calculate total cores (P cores + E cores)
    $p_cores = isset($cpu['cores']) ? intval($cpu['cores']) : 0;
    $e_cores = isset($cpu['secondaryCores']) ? intval($cpu['secondaryCores']) : 0;
    $total_cores = $p_cores + $e_cores;

    // Create a core display string in the format "XP+YE" if both P and E cores are present
    $core_display = $p_cores;
    if ($e_cores > 0) {
        $core_display = $p_cores . 'P+' . $e_cores . 'E';
    }

    $compressed_data[] = [
        'id'            => $cpu['id'] ?? '',
        'name'          => $cpu['name'] ?? '',
        'mark'          => $cpu['cpumark'] ? intval(str_replace(',', '', $cpu['cpumark'])) : null,
        'speed'         => $cpu['speed'] ? intval($cpu['speed']) : null,
        'cpu_count'     => $cpu['cpuCount'] ? intval($cpu['cpuCount']) : null,
        'cores'         => $total_cores,
        'p_cores'       => $p_cores,
        'e_cores'       => $e_cores,
        'core_display'  => $core_display,
        'tdp'           => $cpu['tdp'] ? intval($cpu['tdp']) : null,
        'socket'        => $transformed_socket && isset($transformed_socket['name']) && $transformed_socket['name'] ? $transformed_socket['name'] : $cpu['socket'],
        'socket_slug'   => $transformed_socket && isset($transformed_socket['slug']) && $transformed_socket['slug'] ? $transformed_socket['slug'] : '',
        'category'      => $transformed_category ?? '',
        'whitelist'     => $transformed_socket && isset($transformed_socket['whitelist']) && $transformed_socket['whitelist'] ? $transformed_socket['whitelist'] : false
    ];
}

$json_data = json_encode($compressed_data, JSON_PRETTY_PRINT);
$output_file = $output_dir . '/cpus.json';
file_put_contents($output_file, $json_data);

echo "Compression complete. Processed " . count($compressed_data) . " CPU entries.<br>";
echo "Output saved to: $output_file<br>";
