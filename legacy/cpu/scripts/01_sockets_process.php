<?php
/**
 * This script generates the sockets.json file based on the whitelist array.
 * Whitelisted sockets are grouped by type and manufacturer.
 * 
 * Sockets were taken from https://www.cpubenchmark.net/socketType.html
 */

$output_file = __DIR__ . '/../../data/sockets.json';

$whitelist = [
    // Intel Desktop
    ["name" => "LGA 1851", "slug" => "lga1851", "year" => 2024, "variants" => ["FCLGA1851", "FCLGA 1851"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "LGA 1700", "slug" => "lga1700", "year" => 2021, "variants" => ["LGA1700", "LGA 1700", "BGA1700", "BGA 1700", "LGA1700/BGA1700", "FCLGA1700", "FCLGA 1700"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "LGA 1200", "slug" => "lga1200", "year" => 2020, "variants" => ["LGA1200", "LGA 1200", "FCLGA1200", "FCLGA 1200"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "LGA 1151-v2", "slug" => "lga1151v2", "year" => 2017, "variants" => ["FCLGA1151-2", "FCLGA 1151-2"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "LGA 1151", "slug" => "lga1151", "year" => 2015, "variants" => ["LGA1151", "LGA 1151", "FCLGA1151", "FCLGA 1151"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "LGA 1150", "slug" => "lga1150", "year" => 2013, "variants" => ["LGA1150", "LGA 1150", "FCLGA1150", "FCLGA 1150"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "LGA 1155", "slug" => "lga1155", "year" => 2011, "variants" => ["LGA1155", "LGA 1155", "FCLGA1155", "FCLGA 1155", "LGA1155, FCLGA11"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "LGA 1156", "slug" => "lga1156", "year" => 2009, "variants" => ["LGA1156", "LGA 1156", "FCLGA1156", "FCLGA 1156"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "LGA 771", "slug" => "lga771", "year" => 2006, "variants" => ["LGA771", "LGA 771"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "LGA 775", "slug" => "lga775", "year" => 2004, "variants" => ["LGA775", "LGA 775", "PLGA775", "PLGA 775", "LGA775, PLGA775"], "manufacturer" => "Intel", "type" => "desktop"],
    ["name" => "PGA 478", "slug" => "pga478", "year" => 2001, "variants" => ["PGA478", "PGA 478", "PPGA478", "PPGA 478", "µFCPGA-478", "PPGA478, Socket ", "PBGA479, PPGA478", "BGA479,PGA478"], "manufacturer" => "Intel", "type" => "desktop"],

    // AMD Desktop
    ["name" => "FP8", "slug" => "fp8", "year" => 2024, "variants" => ["FP8"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "sTR5", "slug" => "str5", "year" => 2023, "variants" => ["sTR5", "TR4"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "AM5", "slug" => "am5", "year" => 2022, "variants" => ["AM5", "AM5 (LGA 1718)"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "sWRX8", "slug" => "swrx8", "year" => 2020, "variants" => ["sWRX8"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "sTRX4", "slug" => "strx4", "year" => 2019, "variants" => ["sTRX4"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "sTR4", "slug" => "str4", "year" => 2017, "variants" => ["sTR4", "TR4"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "AM4", "slug" => "am4", "year" => 2017, "variants" => ["AM4"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "FM2/FM2+", "slug" => "fm2", "year" => 2014, "variants" => ["FM2", "FM2+"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "FM1", "slug" => "fm1", "year" => 2011, "variants" => ["FM1"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "AM3/AM3+", "slug" => "am3", "year" => 2011, "variants" => ["AM3", "AM3+", "AM2+/AM3", "AM2+,AM3", "AM2+\/AM3"], "manufacturer" => "AMD", "type" => "desktop"],
    ["name" => "AM2/AM2+", "slug" => "am2", "year" => 2007, "variants" => ["AM2", "AM2+", "AM2,AM2+", "AM2\/AM2+"], "manufacturer" => "AMD", "type" => "desktop"],

    // Intel Server
    ["name" => "LGA 4677", "slug" => "lga4677", "year" => 2023, "variants" => ["LGA4677", "LGA 4677", "FCLGA4677", "FCLGA 4677"], "manufacturer" => "Intel", "type" => "server"],
    ["name" => "LGA 4189", "slug" => "lga4189", "year" => 2020, "variants" => ["LGA4189", "LGA 4189", "FCLGA4189", "FCLGA 4189"], "manufacturer" => "Intel", "type" => "server"],
    ["name" => "LGA 2066", "slug" => "lga2066", "year" => 2017, "variants" => ["LGA2066", "LGA 2066", "FCLGA2066", "FCLGA 2066"], "manufacturer" => "Intel", "type" => "server"],
    ["name" => "LGA 3647", "slug" => "lga3647", "year" => 2016, "variants" => ["LGA3647", "LGA 3647", "FCLGA3647", "FCLGA 3647"], "manufacturer" => "Intel", "type" => "server"],
    ["name" => "LGA 2011-v3", "slug" => "lga2011v3", "year" => 2014, "variants" => ["LGA2011-v3", "LGA 2011-v3", "FCLGA2011-v3", "FCLGA 2011-v3", "FCLGA2011-3", "FCLGA 2011-3"], "manufacturer" => "Intel", "type" => "server"],
    ["name" => "LGA 1356", "slug" => "lga1356", "year" => 2012, "variants" => ["LGA1356", "LGA 1356", "FCLGA1356", "FCLGA 1356"], "manufacturer" => "Intel", "type" => "server"],
    ["name" => "LGA 2011", "slug" => "lga2011", "year" => 2011, "variants" => ["LGA2011", "LGA 2011", "LGA 2011-1", "FCLGA2011", "FCLGA 2011"], "manufacturer" => "Intel", "type" => "server"],
    ["name" => "LGA 1366", "slug" => "lga1366", "year" => 2008, "variants" => ["LGA1366", "LGA 1366"], "manufacturer" => "Intel", "type" => "server"],

    // AMD Server
    ["name" => "SP5", "slug" => "sp5", "year" => 2022, "variants" => ["SP5"], "manufacturer" => "AMD", "type" => "server"],
    ["name" => "SP3", "slug" => "sp3", "year" => 2017, "variants" => ["SP3"], "manufacturer" => "AMD", "type" => "server"],
    ["name" => "G34", "slug" => "g34", "year" => 2011, "variants" => ["G34", "Socket G34"], "manufacturer" => "AMD", "type" => "server"]
];

// Generate sockets.json (normal, grouped and variants map)
$sockets_json = [];
$sockets_json['grouped'] = [
    'desktop' => [],
    'server' => []
];
$sockets_json['variants_map'] = [];

foreach ($whitelist as $socket) {
    // $sockets_json['named'][$socket['name']] = $socket;
    $sockets_json['grouped'][$socket['type']][$socket['manufacturer']][] = $socket;
    foreach ($socket['variants'] as $variant) {
        $sockets_json['variants_map'][$variant] = [
            'name' => $socket['name'],
            'slug' => $socket['slug']
        ];
    }
}

$json_data = json_encode($sockets_json, JSON_PRETTY_PRINT);
file_put_contents($output_file, $json_data);

echo "Socket processing complete. Processed " . count($whitelist) . " sockets.<br>";
echo "Output saved to: $output_file<br>";
