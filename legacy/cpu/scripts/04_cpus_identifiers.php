<?php

/**
 * Minimally normalizes a CPU name or identifier string.
 */
function normalizeCpuIdentifierMinimal(string $identifier): string {
    $normalized = strtolower($identifier);
    $normalized = preg_replace('/@ [0-9.]+ghz/', '', $normalized); // Remove @ frequency
    $normalized = preg_replace('/ \([^\)]+\)/', '', $normalized); // Remove content in parentheses
    $normalized = preg_replace('/\bwith radeon graphics\b/', '', $normalized); // Remove "with radeon graphics"

    // List of words/patterns to remove
    $removePatterns = [
        '/\bghz\b/',
        '/\bmhz\b/',
        '/\bapu\b/',
        '/\bprocessor\b/',
        '/\bdesktop\b/',
        '/\bserver\b/',
        '/\bmobile\b/',
        '/\blaptop\b/',
        '/\bquad-core\b/',
        '/\bsix-core\b/',
        '/\beight-core\b/',
        '/\bdual-core\b/',
        '/\bdual core\b/',
        '/\bquad core\b/',
        '/\bsix core\b/',
        '/\beight core\b/',
        '/\bboxed\b/',
        '/\bblack edition\b/',
        '/\bedition\b/'
    ];
    $normalized = preg_replace($removePatterns, '', $normalized);

    // Standardize spacing and trim
    $normalized = trim(preg_replace('/\s+/', ' ', $normalized));
    return $normalized;
}

/**
 * Extracts candidate identifiers (simple and compound) from a CPU definition.
 * Version 10: Enhanced to better utilize brand_tag, series_tag, and core_model_tag fields.
 *
 * @param array $cpu Expected to have 'brand_tag', 'series_tag' (optional), 'core_model_tag'.
 * @return array ['simple' => [...], 'compound_and' => [[...],[...]]]
 */
function extractCandidatesAdvanced(array $cpu): array {
    $simpleCandidatesSet = [];
    $compoundCandidates = [];
    $potentialIdentifiers = []; // Still used for basic simple rules

    // Read tags - use null coalescing operator for safety
    $brand = isset($cpu['brand_tag']) ? strtolower(trim($cpu['brand_tag'])) : null;
    $series = isset($cpu['series_tag']) ? strtolower(trim($cpu['series_tag'])) : null;
    $coreModel = isset($cpu['core_model_tag']) ? strtolower(trim($cpu['core_model_tag'])) : null;
    $coreModelSpaced = null;
    $coreModelNoPrefix = null;

    // Basic validation
    if (empty($brand) || empty($coreModel)) {
        // Fallback: only use normalized name and identifiers if tags are missing/invalid
        if (isset($cpu['name'])) $potentialIdentifiers[] = $cpu['name'];
        if (isset($cpu['identifiers']) && is_array($cpu['identifiers'])) {
            $potentialIdentifiers = array_merge($potentialIdentifiers, $cpu['identifiers']);
        }
        foreach ($potentialIdentifiers as $identifier) {
            $minimalNormalized = normalizeCpuIdentifierMinimal($identifier);
            if (!empty($minimalNormalized)) $simpleCandidatesSet[$minimalNormalized] = true;
        }
        return ['simple' => array_keys($simpleCandidatesSet), 'compound_and' => []];
    }

    // --- Generate Simple Rules ---

    // 1. From Name and Identifiers (Normalized)
    if (isset($cpu['name'])) $potentialIdentifiers[] = $cpu['name'];
    if (isset($cpu['identifiers']) && is_array($cpu['identifiers'])) {
        $potentialIdentifiers = array_merge($potentialIdentifiers, $cpu['identifiers']);
    }
    foreach ($potentialIdentifiers as $identifier) {
        $minimalNormalized = normalizeCpuIdentifierMinimal($identifier);
        if (empty($minimalNormalized)) continue;
        $simpleCandidatesSet[$minimalNormalized] = true; // Add original structure (cleaned)

        // Generate and add space-separated version if hyphens exist
        if (strpos($minimalNormalized, '-') !== false) {
            $spacedVersion = trim(preg_replace('/\s+/', ' ', str_replace('-', ' ', $minimalNormalized)));
            if ($spacedVersion !== $minimalNormalized) {
                $simpleCandidatesSet[$spacedVersion] = true;
            }
        }
    }

    // 2. Process core model variations
    // Handle models with prefixes (like X5660, G3440)
    if (preg_match('/^([a-z])\d+/i', $coreModel, $matches)) {
        // Store the model without the prefix (e.g., 5660 from X5660)
        $coreModelNoPrefix = substr($coreModel, 1);
        // Ensure it's stored as a string, not an integer
        $simpleCandidatesSet[(string)$coreModelNoPrefix] = true;
    }

    // Handle models with hyphens (like i7-4790K)
    if (strpos($coreModel, '-') !== false) {
        $coreModelSpaced = trim(preg_replace('/\s+/', ' ', str_replace('-', ' ', $coreModel)));
        if ($coreModelSpaced !== $coreModel) {
            $simpleCandidatesSet[$coreModelSpaced] = true;
        } else {
            $coreModelSpaced = null; // Reset if not different
        }

        // For Intel Core models, also add the numeric part alone (e.g., 4790K from i7-4790K)
        if (strpos($cpu['name'], 'Intel Core') !== false && preg_match('/-([\d]+[a-z]*)$/i', $coreModel, $matches)) {
            $numericPart = $matches[1];
            // Ensure it's stored as a string, not an integer
            $simpleCandidatesSet[(string)$numericPart] = true;
        }
    }

    // 3. Combine components (handle cases where series might be missing)
    $brandSeries = $brand;
    if (!empty($series)) {
        $brandSeries .= ' ' . $series;
        $simpleCandidatesSet[trim("$series $coreModel")] = true; // Series + CoreModel

        // For AMD Ryzen, add series number + model (e.g., "5 5600x")
        if (strpos($series, 'ryzen') !== false && preg_match('/ryzen\s+(\d+)/i', $series, $matches)) {
            $seriesNum = $matches[1];
            // Ensure it's stored as a string, not an integer
            $simpleCandidatesSet[trim((string)$seriesNum . " $coreModel")] = true;
        }

        // For AMD A-series with suffixes (like A8-7600B)
        if (strpos($brand, 'amd') !== false &&
            strpos($series, 'a') === 0 &&
            preg_match('/a(\d+)/i', $series, $matches)) {

            // Check if the core model has a suffix (like 7600B)
            if (preg_match('/(\d+)([a-z]+)$/i', $coreModel, $modelMatches)) {
                $baseModel = $modelMatches[1]; // e.g., 7600
                $suffix = $modelMatches[2];    // e.g., B

                // Add variations with and without suffix
                $simpleCandidatesSet["a{$matches[1]}-{$baseModel}{$suffix}"] = true; // e.g., a8-7600b
                $simpleCandidatesSet["a{$matches[1]} {$baseModel}{$suffix}"] = true;  // e.g., a8 7600b

                // For PRO variants
                if (strpos(strtolower($cpu['name']), 'pro') !== false) {
                    $simpleCandidatesSet["a{$matches[1]} pro-{$baseModel}{$suffix}"] = true; // e.g., a8 pro-7600b
                    $simpleCandidatesSet["a{$matches[1]} pro {$baseModel}{$suffix}"] = true;  // e.g., a8 pro 7600b
                    $simpleCandidatesSet["a{$matches[1]}-pro-{$baseModel}{$suffix}"] = true; // e.g., a8-pro-7600b
                }
            }
        }
    }
    $simpleCandidatesSet[trim("$brandSeries $coreModel")] = true; // Brand + Series + CoreModel

    // 4. CoreModel itself
    $simpleCandidatesSet[$coreModel] = true;

    // --- Generate Compound Rules ---
    $seenCompound = [];

    // [Brand, CoreModel]
    $rule1 = [$brand, $coreModel];
    $key1 = $brand . '|' . $coreModel;
    if (!isset($seenCompound[$key1])) {
        $compoundCandidates[] = $rule1;
        $seenCompound[$key1] = true;
    }

    // Add variations with spaced model
    if ($coreModelSpaced) {
        $rule1s = [$brand, $coreModelSpaced];
        $key1s = $brand . '|' . $coreModelSpaced;
        if (!isset($seenCompound[$key1s])) {
            $compoundCandidates[] = $rule1s;
            $seenCompound[$key1s] = true;
        }
    }

    // Add variations with model without prefix
    if ($coreModelNoPrefix) {
        $rule1np = [$brand, (string)$coreModelNoPrefix];
        $key1np = $brand . '|' . $coreModelNoPrefix;
        if (!isset($seenCompound[$key1np])) {
            $compoundCandidates[] = $rule1np;
            $seenCompound[$key1np] = true;
        }
    }

    // [Brand Series, CoreModel] - only if series exists
    if ($series) {
        $seriesContext = trim("$brand $series");
        $rule2 = [$seriesContext, $coreModel];
        $key2 = $seriesContext . '|' . $coreModel;
        if (!isset($seenCompound[$key2])) {
            $compoundCandidates[] = $rule2;
            $seenCompound[$key2] = true;
        }

        // Add variations with spaced model
        if ($coreModelSpaced) {
            $rule2s = [$seriesContext, $coreModelSpaced];
            $key2s = $seriesContext . '|' . $coreModelSpaced;
            if (!isset($seenCompound[$key2s])) {
                $compoundCandidates[] = $rule2s;
                $seenCompound[$key2s] = true;
            }
        }

        // Add variations with model without prefix
        if ($coreModelNoPrefix) {
            $rule2np = [$seriesContext, (string)$coreModelNoPrefix];
            $key2np = $seriesContext . '|' . $coreModelNoPrefix;
            if (!isset($seenCompound[$key2np])) {
                $compoundCandidates[] = $rule2np;
                $seenCompound[$key2np] = true;
            }
        }

        // For AMD Ryzen, add [Ryzen Series Number, CoreModel]
        if (strpos($series, 'ryzen') !== false && preg_match('/ryzen\s+(\d+)/i', $series, $matches)) {
            $ryzenSeries = 'ryzen ' . $matches[1];
            $rule3 = [$ryzenSeries, $coreModel];
            $key3 = $ryzenSeries . '|' . $coreModel;
            if (!isset($seenCompound[$key3])) {
                $compoundCandidates[] = $rule3;
                $seenCompound[$key3] = true;
            }
        }
    }

    $finalSimple = array_keys($simpleCandidatesSet);

    // Clean up simple rules: remove rules that are just the brand, series, or frequency
    $finalSimple = array_filter($finalSimple, function ($rule) use ($brand, $series, $brandSeries) {
        $lowerRule = strtolower($rule);

        // Filter out brand and series
        if ($brand && $lowerRule === $brand) return false;
        if ($series && $lowerRule === $series) return false;
        if ($brandSeries && $lowerRule === $brandSeries) return false;

        // Filter out frequency-only rules
        if (preg_match('/^\d+(\.\d+)?\s*ghz$/', $lowerRule)) return false;
        if (preg_match('/^\d+(\.\d+)?\s*mhz$/', $lowerRule)) return false;

        // Filter out very short rules (less than 4 characters) unless they contain a model pattern
        if (strlen($lowerRule) < 4) {
            // Keep short rules that have a model pattern (letter+number or number+letter)
            if (!preg_match('/[a-z]\d+/i', $lowerRule) && !preg_match('/\d+[a-z]/i', $lowerRule)) {
                return false;
            }
        }

        return true;
    });

    // Ensure all simple rules are strings
    $finalSimple = array_map(function($rule) {
        return (string)$rule;
    }, array_values($finalSimple));

    return ['simple' => $finalSimple, 'compound_and' => $compoundCandidates];
}

// --- Main Function (generateCpuMatchingRulesAdvanced) ---
// --- No changes needed here, it uses the updated extractCandidatesAdvanced ---
/**
 * Generates unique matching rules (simple and compound) for a list of CPUs.
 *
 * @param string|array $cpuData JSON string or decoded PHP array of CPUs.
 * @param bool $returnJson If true, returns JSON string; otherwise, returns PHP array.
 * @return string|array The CPU data with 'matching_rules' added, or false on JSON error/invalid input.
 */
function generateCpuMatchingRulesAdvanced($cpuData, bool $returnJson = false) {
    // ... (Function body is identical to the previous correct version) ...
    if (is_string($cpuData)) {
        $cpus = json_decode($cpuData, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON decode error: " . json_last_error_msg());
            return false;
        }
    } elseif (is_array($cpuData)) {
        $cpus = $cpuData;
    } else {
        error_log("Invalid input type. Expected JSON string or array.");
        return false;
    }
    if (!is_array($cpus)) {
        error_log("Decoded JSON is not an array.");
        return false;
    }

    $globalSimpleCandidatesMap = [];
    $globalCompoundCandidatesMap = [];

    // --- Pass 1: Collect all candidates ---
    foreach ($cpus as $cpu) {
        if (!isset($cpu['id'])) continue;
        $cpuId = $cpu['id'];
        $candidates = extractCandidatesAdvanced($cpu); // Calls the LATEST version

        foreach ($candidates['simple'] as $candidate) {
            if (empty(trim($candidate))) continue;
            $candidate = trim($candidate); // Ensure no leading/trailing spaces from processing
            if (!isset($globalSimpleCandidatesMap[$candidate])) {
                $globalSimpleCandidatesMap[$candidate] = [];
            }
            if (!in_array($cpuId, $globalSimpleCandidatesMap[$candidate])) {
                $globalSimpleCandidatesMap[$candidate][] = $cpuId;
            }
        }

        foreach ($candidates['compound_and'] as $compoundCandidate) {
            if (count($compoundCandidate) !== 2 || empty(trim($compoundCandidate[0])) || empty(trim($compoundCandidate[1]))) continue;
            $trimmedRule = array_map('trim', $compoundCandidate); // Trim parts
            $compoundKey = json_encode($trimmedRule);
            if (!isset($globalCompoundCandidatesMap[$compoundKey])) {
                $globalCompoundCandidatesMap[$compoundKey] = [];
            }
            if (!in_array($cpuId, $globalCompoundCandidatesMap[$compoundKey])) {
                $globalCompoundCandidatesMap[$compoundKey][] = $cpuId;
            }
        }
    }

    // --- Pass 2: Identify unique rules ---
    $uniqueRulesMap = []; // cpuId => ['simple' => [], 'compound_and' => []]

    foreach ($globalSimpleCandidatesMap as $candidate => $cpuIds) {
        if (count($cpuIds) === 1) {
            $cpuId = $cpuIds[0];
            if (!isset($uniqueRulesMap[$cpuId])) {
                $uniqueRulesMap[$cpuId] = ['simple' => [], 'compound_and' => []];
            }
            // Add only if it's not just the brand name itself
            if (!in_array(strtolower($candidate), ['amd', 'intel', 'montage'])) {
                $uniqueRulesMap[$cpuId]['simple'][] = $candidate;
            }
        }
    }

    foreach ($globalCompoundCandidatesMap as $compoundKey => $cpuIds) {
        if (count($cpuIds) === 1) {
            $cpuId = $cpuIds[0];
            $rule = json_decode($compoundKey, true);
            if ($rule === null || count($rule) !== 2) continue;

            if (!isset($uniqueRulesMap[$cpuId])) {
                $uniqueRulesMap[$cpuId] = ['simple' => [], 'compound_and' => []];
            }
            $uniqueRulesMap[$cpuId]['compound_and'][] = $rule;
        }
    }

    // --- Pass 3: Add unique rules to the original CPU data ---
    foreach ($cpus as &$cpu) { // Use reference
        if (!isset($cpu['id'])) {
            $cpu['matching_rules'] = ['simple' => [], 'compound_and' => []];
            continue;
        };
        $cpuId = $cpu['id'];
        $rules = $uniqueRulesMap[$cpuId] ?? ['simple' => [], 'compound_and' => []];

        // Store original rules before filtering
        $originalSimpleRules = $rules['simple'];
        $originalCompoundRules = $rules['compound_and'];

        // Remove duplicates from simple rules before sorting
        $rules['simple'] = array_values(array_unique($rules['simple']));

        // Sort simple rules by length, descending, prioritizing digits
        usort($rules['simple'], function ($a, $b) {
            $aHasDigit = preg_match('/\d/', $a);
            $bHasDigit = preg_match('/\d/', $b);
            if ($aHasDigit !== $bHasDigit) {
                return $bHasDigit <=> $aHasDigit;
            }
            return strlen($b) <=> strlen($a);
        });

        // Ensure compound rules are unique and filter out risky ones
        $tempCompound = [];
        $seenCompoundKeys = [];
        foreach ($rules['compound_and'] as $cRule) {
            $cKey = json_encode($cRule);

            // Skip risky compound rules (brand + short number)
            if (count($cRule) === 2) {
                $firstPart = strtolower(trim($cRule[0]));
                $secondPart = strtolower(trim($cRule[1]));

                // Check if first part is just a brand
                $isBrand = in_array($firstPart, ['intel', 'amd', 'montage']);

                // Check if second part is just a short number or a short alphanumeric string
                $isShortNumber = is_numeric($secondPart) ||
                                (preg_match('/^\d+$/', $secondPart) && strlen($secondPart) <= 4) ||
                                (preg_match('/^\d+[a-z]$/i', $secondPart) && strlen($secondPart) <= 5);

                // Skip brand + short number combinations
                if ($isBrand && $isShortNumber) {
                    continue;
                }
            }

            if (!isset($seenCompoundKeys[$cKey])) {
                $tempCompound[] = $cRule;
                $seenCompoundKeys[$cKey] = true;
            }
        }
        $rules['compound_and'] = $tempCompound;

        // Optional: Sort compound rules (e.g., by combined length or primary element)
        usort($rules['compound_and'], function ($a, $b) {
            // Sort by length of the core model part (second element) descending
            return strlen($b[1]) <=> strlen($a[1]);
        });

        // Debug output for CPUs with no rules
        if ($cpu['name'] === 'AMD E-350' || $cpu['name'] === 'AMD Ryzen 7 6800HS') {
            echo "DEBUG {$cpu['name']} - Original simple rules count: " . count($originalSimpleRules) . "<br>";
            echo "DEBUG {$cpu['name']} - Original compound rules count: " . count($originalCompoundRules) . "<br>";
            if (!empty($originalSimpleRules)) {
                echo "DEBUG {$cpu['name']} - Sample original simple rules: " . implode(", ", array_slice($originalSimpleRules, 0, 5)) . "<br>";
            }
        }

        // Check if we have any rules after filtering
        if (empty($rules['simple']) && empty($rules['compound_and'])) {
            // No rules after filtering, use a less strict approach

            // For simple rules, prioritize model numbers and specific identifiers
            $fallbackSimpleRules = [];
            foreach ($originalSimpleRules as $rule) {
                $lowerRule = strtolower($rule);

                // Skip very generic rules
                if (in_array($lowerRule, ['intel', 'amd', 'montage'])) {
                    continue;
                }

                // Skip pure frequency rules but allow model+frequency
                if (preg_match('/^\d+(\.\d+)?\s*ghz$/', $lowerRule) ||
                    preg_match('/^\d+(\.\d+)?\s*mhz$/', $lowerRule)) {
                    continue;
                }

                // Include rules with model numbers or specific identifiers
                if (preg_match('/[a-z]\d+/i', $lowerRule) || // Pattern like E350, i7, etc.
                    preg_match('/\d+[a-z]/i', $lowerRule) || // Pattern like 5600X, etc.
                    strlen($lowerRule) >= 6) {                // Longer rules are more specific
                    $fallbackSimpleRules[] = $rule;
                }
            }

            // If we still have no simple rules, include some of the original rules
            if (empty($fallbackSimpleRules) && !empty($originalSimpleRules)) {
                // Sort original rules by length (longer is better)
                usort($originalSimpleRules, function($a, $b) {
                    return strlen($b) <=> strlen($a);
                });

                // Take the top 3 longest rules
                $fallbackSimpleRules = array_slice($originalSimpleRules, 0, 3);
            }

            // For compound rules, include those with specific model identifiers
            $fallbackCompoundRules = [];
            foreach ($originalCompoundRules as $cRule) {
                if (count($cRule) !== 2) continue;

                $firstPart = strtolower(trim($cRule[0]));
                $secondPart = strtolower(trim($cRule[1]));

                // Skip if both parts are too generic
                if (in_array($firstPart, ['intel', 'amd', 'montage']) &&
                    (strlen($secondPart) <= 3 || preg_match('/^\d+(\.\d+)?\s*ghz$/', $secondPart))) {
                    continue;
                }

                $fallbackCompoundRules[] = $cRule;
            }

            // If we still have no rules, generate some from the CPU name
            if (empty($fallbackSimpleRules) && empty($fallbackCompoundRules) && isset($cpu['name'])) {
                $cpuName = $cpu['name'];
                $normalizedName = strtolower(trim($cpuName));

                // Extract model numbers and specific identifiers from the name
                if (preg_match('/e-?(\d+)/i', $cpuName, $matches)) {
                    // E-series CPUs (like E-350)
                    $fallbackSimpleRules[] = 'e' . $matches[1];
                    $fallbackSimpleRules[] = 'e-' . $matches[1];
                    $fallbackCompoundRules[] = ['amd', 'e' . $matches[1]];
                    $fallbackCompoundRules[] = ['amd', 'e-' . $matches[1]];
                }
                else if (preg_match('/ryzen\s+(\d+)\s+(\d+)/i', $cpuName, $matches)) {
                    // Ryzen CPUs (like Ryzen 7 6800HS)
                    $fallbackSimpleRules[] = 'ryzen ' . $matches[1] . ' ' . $matches[2];
                    $fallbackSimpleRules[] = $matches[2];
                    $fallbackCompoundRules[] = ['ryzen ' . $matches[1], $matches[2]];
                    $fallbackCompoundRules[] = ['amd', 'ryzen ' . $matches[1] . ' ' . $matches[2]];
                }
                else if (preg_match('/fx-?(\d+)/i', $cpuName, $matches)) {
                    // FX-series CPUs (like FX-7600P)
                    $fallbackSimpleRules[] = 'fx' . $matches[1];
                    $fallbackSimpleRules[] = 'fx-' . $matches[1];
                    $fallbackCompoundRules[] = ['amd', 'fx' . $matches[1]];
                    $fallbackCompoundRules[] = ['amd', 'fx-' . $matches[1]];
                }
                else if (preg_match('/phenom\s+(\d+)/i', $cpuName, $matches)) {
                    // Phenom CPUs (like Phenom 9500)
                    $fallbackSimpleRules[] = 'phenom ' . $matches[1];
                    $fallbackSimpleRules[] = $matches[1];
                    $fallbackCompoundRules[] = ['amd', 'phenom ' . $matches[1]];
                    $fallbackCompoundRules[] = ['phenom', $matches[1]];
                }
                else if (preg_match('/athlon.*\s+(\d+)\+/i', $cpuName, $matches)) {
                    // Athlon CPUs with plus (like Athlon 3200+)
                    $fallbackSimpleRules[] = 'athlon ' . $matches[1] . '+';
                    $fallbackSimpleRules[] = $matches[1] . '+';
                    $fallbackCompoundRules[] = ['amd', 'athlon ' . $matches[1] . '+'];
                    $fallbackCompoundRules[] = ['athlon', $matches[1] . '+'];
                }
                else if (preg_match('/sempron\s+(\d+)\+/i', $cpuName, $matches)) {
                    // Sempron CPUs with plus (like Sempron 3200+)
                    $fallbackSimpleRules[] = 'sempron ' . $matches[1] . '+';
                    $fallbackSimpleRules[] = $matches[1] . '+';
                    $fallbackCompoundRules[] = ['amd', 'sempron ' . $matches[1] . '+'];
                    $fallbackCompoundRules[] = ['sempron', $matches[1] . '+'];
                }
                else if (preg_match('/turion.*\s+rm-(\d+)/i', $cpuName, $matches)) {
                    // Turion CPUs (like Turion X2 Dual Core Mobile RM-70)
                    $fallbackSimpleRules[] = 'rm-' . $matches[1];
                    $fallbackSimpleRules[] = 'rm' . $matches[1];
                    $fallbackCompoundRules[] = ['amd', 'rm-' . $matches[1]];
                    $fallbackCompoundRules[] = ['turion', 'rm-' . $matches[1]];
                }
                else if (preg_match('/xeon.*\s+(\d+\.\d+)ghz/i', $cpuName, $matches)) {
                    // Xeon CPUs with frequency (like Xeon 3.20GHz)
                    $fallbackSimpleRules[] = 'xeon ' . $matches[1] . 'ghz';
                    $fallbackCompoundRules[] = ['intel', 'xeon ' . $matches[1] . 'ghz'];
                    $fallbackCompoundRules[] = ['xeon', $matches[1] . 'ghz'];
                }
                else if (preg_match('/xeon\s+gold\s+(\d+)/i', $cpuName, $matches)) {
                    // Xeon Gold CPUs (like Xeon Gold 5215)
                    $fallbackSimpleRules[] = 'xeon gold ' . $matches[1];
                    $fallbackSimpleRules[] = 'gold ' . $matches[1];
                    $fallbackCompoundRules[] = ['intel', 'xeon gold ' . $matches[1]];
                    $fallbackCompoundRules[] = ['xeon gold', $matches[1]];
                }
                else if (preg_match('/xeon\s+platinum\s+(\d+)/i', $cpuName, $matches)) {
                    // Xeon Platinum CPUs (like Xeon Platinum 8260M)
                    $fallbackSimpleRules[] = 'xeon platinum ' . $matches[1];
                    $fallbackSimpleRules[] = 'platinum ' . $matches[1];
                    $fallbackCompoundRules[] = ['intel', 'xeon platinum ' . $matches[1]];
                    $fallbackCompoundRules[] = ['xeon platinum', $matches[1]];
                }
                else if (preg_match('/pentium\s+4\s+(\d+\.\d+)ghz/i', $cpuName, $matches)) {
                    // Pentium 4 CPUs with frequency (like Pentium 4 3.20GHz)
                    $fallbackSimpleRules[] = 'pentium 4 ' . $matches[1] . 'ghz';
                    $fallbackCompoundRules[] = ['intel', 'pentium 4 ' . $matches[1] . 'ghz'];
                    $fallbackCompoundRules[] = ['pentium 4', $matches[1] . 'ghz'];
                }
                else if (preg_match('/celeron\s+(\d+\.\d+)ghz/i', $cpuName, $matches)) {
                    // Celeron CPUs with frequency (like Celeron 1.80GHz)
                    $fallbackSimpleRules[] = 'celeron ' . $matches[1] . 'ghz';
                    $fallbackCompoundRules[] = ['intel', 'celeron ' . $matches[1] . 'ghz'];
                    $fallbackCompoundRules[] = ['celeron', $matches[1] . 'ghz'];
                }
                else if (preg_match('/a(\d+)[\s-]+(\d+)([a-z]+)/i', $cpuName, $matches)) {
                    // AMD A-series CPUs with suffix (like A8-7600B or A8 PRO-7600B)
                    $aSeries = $matches[1];      // e.g., 8
                    $baseModel = $matches[2];    // e.g., 7600
                    $suffix = $matches[3];       // e.g., B

                    // Add variations with and without suffix
                    $fallbackSimpleRules[] = 'a' . $aSeries . '-' . $baseModel . $suffix;
                    $fallbackSimpleRules[] = 'a' . $aSeries . ' ' . $baseModel . $suffix;
                    $fallbackSimpleRules[] = $baseModel . $suffix;

                    $fallbackCompoundRules[] = ['amd', 'a' . $aSeries . '-' . $baseModel . $suffix];
                    $fallbackCompoundRules[] = ['a' . $aSeries, $baseModel . $suffix];

                    // For PRO variants
                    if (stripos($cpuName, 'pro') !== false) {
                        $fallbackSimpleRules[] = 'a' . $aSeries . ' pro-' . $baseModel . $suffix;
                        $fallbackSimpleRules[] = 'a' . $aSeries . ' pro ' . $baseModel . $suffix;
                        $fallbackSimpleRules[] = 'a' . $aSeries . '-pro-' . $baseModel . $suffix;

                        $fallbackCompoundRules[] = ['amd', 'a' . $aSeries . ' pro-' . $baseModel . $suffix];
                        $fallbackCompoundRules[] = ['a' . $aSeries . ' pro', $baseModel . $suffix];
                    }
                }

                // If we still have no rules, use the full name as a last resort
                if (empty($fallbackSimpleRules)) {
                    $fallbackSimpleRules[] = $normalizedName;
                }
            }

            // Use the fallback rules
            $rules['simple'] = $fallbackSimpleRules;
            $rules['compound_and'] = $fallbackCompoundRules;
        }

        $cpu['matching_rules'] = $rules;
    }
    unset($cpu); // Unset the reference

    // --- Return the modified data ---
    if ($returnJson) {
        // Remove the temporary tags before encoding if desired
        foreach ($cpus as &$cpu) {
            unset($cpu['brand_tag'], $cpu['series_tag'], $cpu['core_model_tag']);

            // Ensure all simple rules are strings in the JSON output
            if (isset($cpu['matching_rules']) && isset($cpu['matching_rules']['simple'])) {
                foreach ($cpu['matching_rules']['simple'] as $key => $rule) {
                    $cpu['matching_rules']['simple'][$key] = (string)$rule;
                }
            }

            // Ensure all compound rules have string values
            if (isset($cpu['matching_rules']) && isset($cpu['matching_rules']['compound_and'])) {
                foreach ($cpu['matching_rules']['compound_and'] as $i => $rule) {
                    foreach ($rule as $j => $part) {
                        $cpu['matching_rules']['compound_and'][$i][$j] = (string)$part;
                    }
                }
            }
        }
        unset($cpu);
        return json_encode($cpus, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    } else {
        return $cpus; // Return array with tags still present
    }
}

// --- Main Script ---
// Use the tagged CPU data file
$jsonString = file_get_contents(__DIR__ . '/../data/cpus_details.json');

// Get the result as a PHP array
$cpusWithRulesArray = generateCpuMatchingRulesAdvanced($jsonString, false);

if ($cpusWithRulesArray === false) {
    echo "Error processing CPU data.<br>";
} else {
    // Save the result to a new file
    $outputJson = generateCpuMatchingRulesAdvanced($jsonString, true);
    file_put_contents(__DIR__ . '/../data/cpus_identifiers.json', $outputJson);

    echo "Successfully processed " . count($cpusWithRulesArray) . " CPUs.<br>";
    echo "Output saved to cpu/data/cpus_identifiers.json<br>";
}
