<?php

/**
 * Function to extract brand, series, and core model tags from CPU name
 *
 * @param string $name CPU name
 * @return array Array containing brand_tag, series_tag, and core_model_tag
 */
function extract_tags($name) {
    // Default values
    $brand_tag = "";
    $series_tag = "";
    $core_model_tag = "";

    // Extract brand
    if (strpos($name, "AMD") !== false) {
        $brand_tag = "AMD";
    } elseif (strpos($name, "Intel") !== false) {
        $brand_tag = "Intel";
    } elseif (strpos($name, "Montage Jintide") !== false) {
        $brand_tag = "Montage";
        $series_tag = "Jintide";
        // Extract core model
        if (preg_match('/C(\d{4}[A-Za-z]*)/', $name, $matches)) {
            $core_model_tag = $matches[1];
        }
        return array($brand_tag, $series_tag, $core_model_tag);
    } else {
        // Check for AMD processor families
        $amd_families = array("Athlon", "Ryzen", "Phenom", "Opteron", "Sempron", "Turion", "FX", "EPYC");
        foreach ($amd_families as $family) {
            if (strpos($name, $family) !== false) {
                $brand_tag = "AMD";
                break;
            }
        }

        // Check for Intel processor families
        $intel_families = array("Celeron", "Pentium", "Xeon", "Core 2", "Core i", "Core m");
        foreach ($intel_families as $family) {
            if (strpos($name, $family) !== false) {
                $brand_tag = "Intel";
                break;
            }
        }
    }

    // Extract series and core model
    if ($brand_tag == "AMD") {
        // AMD patterns
        if (strpos($name, "Ryzen Threadripper") !== false) {
            // Check for PRO designation
            if (strpos($name, "PRO") !== false) {
                $series_tag = "Ryzen Threadripper PRO";
            } else {
                $series_tag = "Ryzen Threadripper";
            }
            // Extract core model
            if (preg_match('/(\d{4}[A-Za-z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Ryzen AI") !== false) {
            // Extract the Ryzen AI series
            if (strpos($name, "HX") !== false) {
                if (strpos($name, "PRO") !== false) {
                    $series_tag = "Ryzen AI HX PRO";
                } else {
                    $series_tag = "Ryzen AI HX";
                }
            } elseif (strpos($name, "PRO") !== false) {
                $series_tag = "Ryzen AI PRO";
            } else {
                $series_tag = "Ryzen AI";
            }

            // Extract core model
            if (preg_match('/(\d{3})/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Ryzen") !== false) {
            if (preg_match('/Ryzen\s+(\d+(?:\s+[A-Z]+)?)/', $name, $matches)) {
                $series_tag = "Ryzen " . $matches[1];
            }

            // Check for X3D models
            if (strpos($name, "X3D") !== false) {
                if (preg_match('/(\d{4}X3D)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                } else if (preg_match('/(\d{4}X)/', $name, $matches) && strpos($name, "X3D") !== false) {
                    $core_model_tag = $matches[1] . "3D";
                }
            }
            // Extract core model for other Ryzen processors
            else if (preg_match('/(\d{4}[A-Za-z]*(?:\s*XT)?)/', $name, $matches)) {
                $core_model_tag = trim($matches[1]);
            }
        } elseif (strpos($name, "Athlon") !== false) {
            if (strpos($name, "64") !== false) {
                $series_tag = "Athlon 64";
                // Extract core model for Athlon 64
                if (preg_match('/(\d{4}[A-Za-z]*\+?|\d{3,4}\+?)/', $name, $matches)) {
                        // Clean up the plus sign to avoid issues with appended characters
                    $core_model_tag = $matches[1];
                    // Make sure there are no additional characters after the plus sign
                    if (strpos($core_model_tag, '+') !== false) {
                        $parts = explode('+', $core_model_tag);
                        $core_model_tag = $parts[0] . '+';
                    }
                }
                // Special case for "AMD Athlon 64 FX-62 Dual Core"
                elseif (strpos($name, "FX-") !== false) {
                    if (preg_match('/FX-(\d{2})/', $name, $matches)) {
                        $core_model_tag = "FX-" . $matches[1];
                    }
                }
            } elseif (strpos($name, "II") !== false) {
                if (preg_match('/Athlon\s+II\s+(X\d)/', $name, $matches)) {
                    $series_tag = "Athlon II " . $matches[1];
                } else {
                    $series_tag = "Athlon II";
                }

                // Extract core model
                if (preg_match('/(\d{3,4}[a-zA-Z]*)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                }
                // Special case for "AMD Athlon II X2 B22"
                elseif (strpos($name, "B") !== false) {
                    if (preg_match('/B(\d{2})/', $name, $matches)) {
                        $core_model_tag = "B" . $matches[1];
                    }
                }
            } else {
                $series_tag = "Athlon";
                // Special case for Athlon GE series (200GE, 300GE, etc.)
                if (preg_match('/Athlon\s+(\d{3}GE)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                }
                // Extract core model for other Athlon processors
                else if (preg_match('/(\d{3,4}[a-zA-Z]*)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                }
            }
        }
        // Handle all AMD A-series processors
        elseif (preg_match('/A\d+(?:-|\s+PRO)/', $name)) {
            // Extract the A-series number (A4, A6, A8, A10, A12)
            if (preg_match('/A(\d+)(?:-|\s+PRO)/', $name, $matches)) {
                $series_num = $matches[1];
                if (strpos($name, "PRO") !== false) {
                    $series_tag = "A" . $series_num . " PRO";
                } else {
                    $series_tag = "A" . $series_num;
                }

                // Extract core model - include both uppercase and lowercase letters
                if (preg_match('/(\d{4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                } else {
                    if (preg_match('/-(\d{4}[A-Za-z]*)/', $name, $matches)) {
                        $core_model_tag = $matches[1];
                    }
                }
            }
        } elseif (strpos($name, "E2-") !== false) {
            $series_tag = "E2";
            // Extract core model
            if (preg_match('/E2-(\d{4}[A-Za-z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "FirePro") !== false) {
            $series_tag = "FirePro";
            // Extract core model
            if (preg_match('/FirePro\s+([A-Z]\d{3})/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Sempron") !== false) {
            $series_tag = "Sempron";
            // Extract core model
            if (preg_match('/Sempron\s+(\d{3,4}\+?)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Phenom") !== false) {
            // Check for Phenom II
            if (strpos($name, "II") !== false) {
                $series_tag = "Phenom II";
                // Special case for "AMD Phenom II X2 B55"
                if (strpos($name, "B") !== false) {
                    if (preg_match('/B(\d{2})/', $name, $matches)) {
                        $core_model_tag = "B" . $matches[1];
                    }
                } else {
                    // Extract core model
                    if (preg_match('/(\d{4}[A-Za-z]*|\d{3,4}[A-Za-z]*)/', $name, $matches)) {
                        $core_model_tag = $matches[1];
                    }
                }
            } else {
                $series_tag = "Phenom";

                // Extract core model
                if (preg_match('/(\d{4}[A-Za-z]*|\d{3,4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                }
            }
        } elseif (strpos($name, "FX") !== false) {
            $series_tag = "FX";
            // Extract core model
            if (preg_match('/FX[-\s](\d{4})/', $name, $matches)) {
                $core_model_tag = $matches[1];
            } else {
                // Try alternative pattern for FX
                if (preg_match('/FX[-\s](\d{3,4})/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                }
            }
        } elseif (strpos($name, "EPYC") !== false) {
            $series_tag = "EPYC";
            // Extract core model for EPYC
            if (preg_match('/(\d{4}[A-Za-z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            } else {
                // Special case for "AMD EPYC 7F32" type models
                if (preg_match('/EPYC\s+(\d[A-Z]\d{2})/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                } else {
                    // Try another pattern for EPYC
                    if (preg_match('/EPYC\s+(\d{2}F\d)/', $name, $matches)) {
                        $core_model_tag = $matches[1];
                    }
                }
            }
        } elseif (strpos($name, "Opteron") !== false) {
            $series_tag = "Opteron";
            // Extract core model
            if (preg_match('/(\d{4}[A-Za-z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        }
        // Add more AMD patterns as needed
    } elseif ($brand_tag == "Intel") {
        // Intel patterns
        if (strpos($name, "Core2 Extreme") !== false || strpos($name, "Core 2 Extreme") !== false) {
            $series_tag = "Core2 Extreme";
            // Extract core model
            if (preg_match('/(X\d{4}|Q\d{4})/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Core2 Duo") !== false || strpos($name, "Core 2 Duo") !== false) {
            $series_tag = "Core2 Duo";
            // Extract core model
            if (preg_match('/E(\d{4})/', $name, $matches)) {
                $core_model_tag = "E" . $matches[1];
            }
        } elseif (strpos($name, "Core2 Quad") !== false || strpos($name, "Core 2 Quad") !== false) {
            $series_tag = "Core2 Quad";
            // Extract core model
            if (preg_match('/Q(\d{4})/', $name, $matches)) {
                $core_model_tag = "Q" . $matches[1];
            }
        } elseif (strpos($name, "Core Ultra") !== false) {
            $series_tag = "Core Ultra";
            // Extract core model
            if (preg_match('/Core Ultra\s+\d+\s+(\d{3}[A-Z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Core Duo") !== false) {
            $series_tag = "Core Duo";
            // Extract core model
            if (preg_match('/T(\d{4})/', $name, $matches)) {
                $core_model_tag = "T" . $matches[1];
            }
        } elseif (strpos($name, "Core Solo") !== false) {
            $series_tag = "Core Solo";
            // Extract core model
            if (preg_match('/T(\d{4})/', $name, $matches)) {
                $core_model_tag = "T" . $matches[1];
            }
        } elseif (strpos($name, "Core i3") !== false) {
            $series_tag = "Core i3";
            // Extract core model
            if (preg_match('/i3-(\d{4,5}[A-Za-z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Core i5") !== false) {
            $series_tag = "Core i5";
            // Extract core model
            if (preg_match('/i5-(\d{4,5}[A-Za-z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Core i7") !== false) {
            $series_tag = "Core i7";
            // Extract core model
            if (preg_match('/i7-(\d{3,5}[A-Za-z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            } else {
                // For older Core i7 models like "Intel Core i7-860 @ 2.80GHz"
                if (preg_match('/Core i7-(\d{3,4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                }
            }
        } elseif (strpos($name, "Core i9") !== false) {
            $series_tag = "Core i9";
            // Extract core model
            if (preg_match('/i9-(\d{4,5}[A-Za-z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Celeron") !== false) {
            $series_tag = "Celeron";
            // Check for Celeron G-series
            if (strpos($name, "Celeron G") !== false) {
                if (preg_match('/Celeron G(\d{3,4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = "G" . $matches[1];
                }
            }
            // If not G-series or pattern didn't match, use generic patterns
            if ($core_model_tag == "") {
                if (preg_match('/(\d{4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                } else {
                    if (preg_match('/T(\d{4})/', $name, $matches)) {
                        $core_model_tag = $matches[1];
                    } else {
                        // Special case for "Intel Celeron 2.80GHz"
                        if (preg_match('/Celeron\s+([\d.]+GHz)/', $name, $matches)) {
                            $core_model_tag = $matches[1];
                        }
                    }
                }
            }
        } elseif (strpos($name, "Pentium 4") !== false) {
            $series_tag = "Pentium 4";
            // Special case for "Intel Pentium 4 3.00GHz"
            if (preg_match('/Pentium 4\s+([\d.]+GHz)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        } elseif (strpos($name, "Pentium") !== false) {
            $series_tag = "Pentium";
            // Check for Pentium Gold G-series
            if (strpos($name, "Pentium Gold G") !== false) {
                if (preg_match('/Pentium Gold G(\d{3,4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = "G" . $matches[1];
                }
            }
            // Check for Pentium G-series
            elseif (strpos($name, "Pentium G") !== false) {
                if (preg_match('/Pentium G(\d{3,4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = "G" . $matches[1];
                }
            }
            // Check for Pentium T-series
            elseif (strpos($name, "Pentium T") !== false) {
                if (preg_match('/Pentium T(\d{3,4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = "T" . $matches[1];
                }
            }
            // Check for Pentium E-series
            elseif (strpos($name, "Pentium E") !== false) {
                if (preg_match('/Pentium E(\d{3,4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = "E" . $matches[1];
                }
            }
            // If no specific series or pattern didn't match, use generic pattern
            if ($core_model_tag == "") {
                if (preg_match('/(\d{4}[A-Za-z]*)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                }
            }
        } elseif (strpos($name, "Xeon") !== false) {
            // Set default series tag
            $series_tag = "Xeon";

            // Check for Xeon sub-series
            if (strpos($name, "Xeon Gold") !== false) {
                $series_tag = "Xeon Gold";
            } elseif (strpos($name, "Xeon Silver") !== false) {
                $series_tag = "Xeon Silver";
            } elseif (strpos($name, "Xeon Bronze") !== false) {
                $series_tag = "Xeon Bronze";
            } elseif (strpos($name, "Xeon Platinum") !== false) {
                $series_tag = "Xeon Platinum";
            }

            // Special case for "Intel Xeon E E-2414" type models
            if (strpos($name, "Xeon E E-") !== false) {
                if (preg_match('/Xeon\s+E\s+E-(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = "E-" . $matches[1];
                }
            }
            // Handle different Xeon naming patterns
            elseif (strpos($name, "Xeon X") !== false) {
                if (preg_match('/Xeon\s+X(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = "X" . $matches[1];
                }
            } elseif (strpos($name, "Xeon E3") !== false) {
                if (preg_match('/Xeon\s+E3-(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = "E3-" . $matches[1];
                }
            } elseif (strpos($name, "Xeon E5") !== false) {
                if (preg_match('/Xeon\s+E5-(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = "E5-" . $matches[1];
                } else if (preg_match('/Xeon\s+E(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = "E" . $matches[1];
                }
            } elseif (strpos($name, "Xeon E7") !== false) {
                if (preg_match('/Xeon\s+E7-(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = "E7-" . $matches[1];
                }
            } elseif (strpos($name, "Xeon W") !== false) {
                if (preg_match('/Xeon\s+W-(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = "W-" . $matches[1];
                }
            } elseif (strpos($name, "Xeon D") !== false) {
                if (preg_match('/Xeon\s+D-(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = "D-" . $matches[1];
                }
            } elseif (strpos($name, "Xeon E-") !== false) {
                if (preg_match('/Xeon\s+E-(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = "E-" . $matches[1];
                }
            } elseif (strpos($name, "Xeon E ") !== false && strpos($name, "Xeon E E-") === false) {
                if (preg_match('/Xeon\s+E\s+(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                }
            } else {
                // Generic Xeon pattern for models without a letter prefix
                if (preg_match('/Xeon\s+(\d{4}[A-Za-z]*v?\d?)/', $name, $matches)) {
                    $core_model_tag = $matches[1];
                }
            }
        }
        // Generic Intel pattern for models like "Intel 300"
        elseif (preg_match('/^Intel\s+\d{3}[A-Za-z]*$/', $name)) {
            $series_tag = "Intel";
            if (preg_match('/Intel\s+(\d{3}[A-Za-z]*)/', $name, $matches)) {
                $core_model_tag = $matches[1];
            }
        }
        // Add more Intel patterns as needed
    }

    // If we couldn't extract the core model, try a generic approach
    if ($core_model_tag == "") {
        // Look for patterns like numbers followed by letters or plus signs
        if (preg_match('/(\d{3,5}[A-Za-z]*\+?)/', $name, $matches)) {
            $core_model_tag = $matches[1];
        }
    }

    // If core_model_tag is still empty, use a fallback
    if ($core_model_tag == "") {
        // For CPUs with GHz in the name, use the GHz value as the model
        if (preg_match('/([\d.]+GHz)/', $name, $matches)) {
            $core_model_tag = $matches[1];
        }
        // For CPUs with model numbers like B55, use that
        elseif (strpos($name, "B") !== false) {
            if (preg_match('/B(\d{2})/', $name, $matches)) {
                $core_model_tag = "B" . $matches[1];
            }
        }
        // Last resort: use the last part of the name
        else {
            $parts = explode(" ", $name);
            if (!empty($parts)) {
                $core_model_tag = end($parts);
            }
        }
    }

    return array($brand_tag, $series_tag, $core_model_tag);
}

/**
 * Function to check if the combination of brand_tag, series_tag, and core_model_tag is unique
 *
 * @param array $cpus Array of CPU objects
 * @return array Array of CPUs with adjusted tags to ensure uniqueness
 */
function ensure_unique_tags($cpus) {
    $tag_combinations = [];
    $duplicates = [];

    // First pass: identify duplicates
    foreach ($cpus as $index => $cpu) {
        $key = $cpu['brand_tag'] . '|' . $cpu['series_tag'] . '|' . $cpu['core_model_tag'];

        if (!isset($tag_combinations[$key])) {
            $tag_combinations[$key] = [
                'indices' => [$index],
                'names' => [$cpu['name']],
                'cpu_counts' => [$cpu['cpu_count']],
                'ids' => [$cpu['id']]
            ];
        } else {
            $tag_combinations[$key]['indices'][] = $index;
            $tag_combinations[$key]['names'][] = $cpu['name'];
            $tag_combinations[$key]['cpu_counts'][] = $cpu['cpu_count'];
            $tag_combinations[$key]['ids'][] = $cpu['id'];

            // Only consider it a duplicate if the CPU count is the same
            $is_duplicate = false;
            foreach ($tag_combinations[$key]['cpu_counts'] as $count) {
                if ($count == $cpu['cpu_count']) {
                    $is_duplicate = true;
                    break;
                }
            }

            if ($is_duplicate) {
                $duplicates[$key] = $tag_combinations[$key];
            }
        }
    }

    // Second pass: adjust tags for duplicates
    foreach ($duplicates as $key => $info) {
        $parts = explode('|', $key);
        // We only need the model part for adjustments
        $model = $parts[2];

        // For each duplicate, look for differences in the name
        foreach ($info['indices'] as $i => $index) {
            $cpu_name = $info['names'][$i];
            $cpu_id = $info['ids'][$i];

            // Special case handling for known problematic CPUs
            if ($cpu_id == '650' || $cpu_id == '698' || $cpu_id == '700') {
                // Intel Celeron 420 vs Celeron D 420 vs Celeron M 420
                if (strpos($cpu_name, 'Celeron D') !== false) {
                    $cpus[$index]['series_tag'] = 'Celeron D';
                } elseif (strpos($cpu_name, 'Celeron M') !== false) {
                    $cpus[$index]['series_tag'] = 'Celeron M';
                }
            } elseif ($cpu_id == '1921' || $cpu_id == '2452') {
                // Intel Core i5-4670K vs Core i5-4670K CPT
                if (strpos($cpu_name, 'CPT') !== false) {
                    $cpus[$index]['core_model_tag'] = $model . ' CPT';
                }
            } elseif ($cpu_id == '4963' || $cpu_id == '1141') {
                // Intel Pentium Dual T2390 vs Pentium T2390
                if (strpos($cpu_name, 'Dual') !== false) {
                    $cpus[$index]['series_tag'] = 'Pentium Dual';
                }
            } elseif ($cpu_id == '1194' || $cpu_id == '2044') {
                // Intel Xeon E3110 vs Xeon L3110
                if (strpos($cpu_name, 'Xeon E') !== false) {
                    $cpus[$index]['core_model_tag'] = 'E' . $model;
                } elseif (strpos($cpu_name, 'Xeon L') !== false) {
                    $cpus[$index]['core_model_tag'] = 'L' . $model;
                }
            } elseif ($cpu_id == '81' || $cpu_id == '1551') {
                // AMD Athlon 64 X2 Dual Core 4600+ vs AMD Athlon64 X2 Dual Core 4600+
                if (strpos($cpu_name, 'Athlon 64') !== false) {
                    $cpus[$index]['brand_tag'] = 'AMD';
                    $cpus[$index]['series_tag'] = 'Athlon 64 X2';
                } elseif (strpos($cpu_name, 'Athlon64') !== false) {
                    $cpus[$index]['brand_tag'] = 'AMD';
                    $cpus[$index]['series_tag'] = 'Athlon64 X2';
                }
            }
            // Look for version information (v2, v3, etc.)
            elseif (preg_match('/\s+[vV](\d+)\s+/', $cpu_name, $matches)) {
                $version = $matches[1];
                $cpus[$index]['core_model_tag'] = $model . ' v' . $version;
            }
            // Look for frequency information
            elseif (preg_match('/\s+@\s+([\d.]+GHz)/', $cpu_name, $matches)) {
                $freq = $matches[1];
                $cpus[$index]['core_model_tag'] = $model . ' ' . $freq;
            }
            // Look for other distinguishing features
            elseif (strpos($cpu_name, 'PRO') !== false && strpos($cpus[$index]['series_tag'], 'PRO') === false) {
                $cpus[$index]['series_tag'] .= ' PRO';
            }
            elseif (strpos($cpu_name, 'Gold') !== false && strpos($cpus[$index]['series_tag'], 'Gold') === false) {
                $cpus[$index]['series_tag'] .= ' Gold';
            }
            elseif (strpos($cpu_name, 'X2') !== false && strpos($cpus[$index]['series_tag'], 'X2') === false) {
                $cpus[$index]['series_tag'] .= ' X2';
            }
            elseif (strpos($cpu_name, 'X3D') !== false) {
                $cpus[$index]['core_model_tag'] .= 'X3D';
            }
            elseif (strpos($cpu_name, 'HE') !== false) {
                $cpus[$index]['core_model_tag'] .= ' HE';
            }
            elseif (strpos($cpu_name, 'SE') !== false) {
                $cpus[$index]['core_model_tag'] .= ' SE';
            }
            // If no distinguishing features found, append the index
            else {
                $cpus[$index]['core_model_tag'] = $model . '-' . $i;
            }
        }
    }

    return $cpus;
}

// Read the entire JSON file
$json_data = file_get_contents(__DIR__ . '/../data/cpus.json');
$cpus = json_decode($json_data, true);

// Process each CPU
foreach ($cpus as &$cpu) {
    list($brand_tag, $series_tag, $core_model_tag) = extract_tags($cpu['name']);

    // Special case fixes for specific CPUs
    if ($cpu['name'] == "AMD Athlon 200GE") {
        $core_model_tag = "200GE";
    } else if ($cpu['name'] == "AMD Athlon 300GE") {
        $core_model_tag = "300GE";
    } else if ($cpu['name'] == "AMD Athlon 64 4000+") {
        $core_model_tag = "4000+";
    } else if ($cpu['name'] == "AMD Ryzen 5 5600X3D") {
        $core_model_tag = "5600X3D";
    } else if ($cpu['name'] == "AMD A4-9120e") {
        $core_model_tag = "9120e";
    }

    $cpu['brand_tag'] = $brand_tag;
    $cpu['series_tag'] = $series_tag;
    $cpu['core_model_tag'] = $core_model_tag;
}

/**
 * Function to print statistics about tag combinations
 *
 * @param array $cpus Array of CPU objects
 * @param bool $verbose Whether to print detailed information about duplicates
 */
function print_tag_stats($cpus, $verbose = false) {
    $tag_combinations = [];
    $duplicates = [];
    $duplicate_count = 0;
    $different_name_duplicates = [];

    // Identify duplicates
    foreach ($cpus as $index => $cpu) {
        $key = $cpu['brand_tag'] . '|' . $cpu['series_tag'] . '|' . $cpu['core_model_tag'];

        if (!isset($tag_combinations[$key])) {
            $tag_combinations[$key] = [
                'indices' => [$index],
                'names' => [$cpu['name']],
                'cpu_counts' => [$cpu['cpu_count']],
                'ids' => [$cpu['id']]
            ];
        } else {
            $tag_combinations[$key]['indices'][] = $index;
            $tag_combinations[$key]['names'][] = $cpu['name'];
            $tag_combinations[$key]['cpu_counts'][] = $cpu['cpu_count'];
            $tag_combinations[$key]['ids'][] = $cpu['id'];

            // Only consider it a duplicate if the CPU count is the same
            $is_duplicate = false;
            foreach ($tag_combinations[$key]['cpu_counts'] as $i => $count) {
                if ($count == $cpu['cpu_count']) {
                    $is_duplicate = true;

                    // Check if the names are different
                    if ($tag_combinations[$key]['names'][$i] != $cpu['name']) {
                        $different_name_duplicates[$key] = $tag_combinations[$key];
                    }

                    break;
                }
            }

            if ($is_duplicate) {
                $duplicates[$key] = $tag_combinations[$key];
                $duplicate_count++;
            }
        }
    }

    echo "Total CPUs: " . count($cpus) . "<br>";
    echo "Unique tag combinations: " . count($tag_combinations) . "<br>";
    echo "Duplicate tag combinations (same CPU count): " . count($duplicates) . "<br>";
    echo "Number of CPUs with duplicate tags: " . $duplicate_count . "<br>";
    echo "Duplicates with different names: " . count($different_name_duplicates) . "<br>";

    // Print detailed information about duplicates with different names
    if ($verbose && count($different_name_duplicates) > 0) {
        echo "<br>Duplicates with different names:<br>";
        foreach ($different_name_duplicates as $key => $info) {
            $parts = explode('|', $key);
            echo "<br>Brand: {$parts[0]}, Series: {$parts[1]}, Model: {$parts[2]}<br>";

            // Group by CPU count
            $by_cpu_count = [];
            foreach ($info['indices'] as $i => $index) {
                $count = $info['cpu_counts'][$i];
                if (!isset($by_cpu_count[$count])) {
                    $by_cpu_count[$count] = [];
                }
                $by_cpu_count[$count][] = [
                    'id' => $info['ids'][$i],
                    'name' => $info['names'][$i]
                ];
            }

            // Print each group
            foreach ($by_cpu_count as $count => $cpus) {
                if (count($cpus) > 1) {
                    echo "  CPU Count: {$count}<br>";
                    foreach ($cpus as $cpu) {
                        echo "    ID: {$cpu['id']}, Name: {$cpu['name']}<br>";
                    }
                }
            }
        }
    }
}

// Print statistics before ensuring uniqueness
echo "Before ensuring uniqueness:<br>";
print_tag_stats($cpus, true);

// Ensure uniqueness of tag combinations
$cpus = ensure_unique_tags($cpus);

// Apply special case fixes after ensuring uniqueness
foreach ($cpus as &$cpu) {
    // Remove "-[0-9]" suffix from all core_model_tags
    if (preg_match('/-[0-9]$/', $cpu['core_model_tag'])) {
        $cpu['core_model_tag'] = preg_replace('/-[0-9]$/', '', $cpu['core_model_tag']);
    }

    // Fix Intel Pentium 4 processors with frequency in the model tag
    if (strpos($cpu['name'], "Intel Pentium 4") !== false && strpos($cpu['core_model_tag'], "-") !== false) {
        $cpu['core_model_tag'] = str_replace("-0", "", $cpu['core_model_tag']);
        $cpu['core_model_tag'] = str_replace("-1", "", $cpu['core_model_tag']);
    }

    // Fix Intel Xeon processors with frequency in the model tag
    if (strpos($cpu['name'], "Intel Xeon") !== false) {
        // Pattern for models like "1234" or "1234A" followed by frequency
        if (preg_match('/^(\d{4}[A-Z]*) [\d.]+GHz$/', $cpu['core_model_tag'], $matches)) {
            $cpu['core_model_tag'] = $matches[1];
        }
        // Pattern for models like "X5482" or "E5-2690" followed by frequency
        else if (preg_match('/^([A-Z]\d{4}|[A-Z]\d-\d{4}[A-Z]*) [\d.]+GHz$/', $cpu['core_model_tag'], $matches)) {
            $cpu['core_model_tag'] = $matches[1];
        }
    }

    // Fix Intel Xeon Bronze processors with frequency in the model tag
    if (strpos($cpu['name'], "Intel Xeon Bronze") !== false && preg_match('/^(\d{4}) [\d.]+GHz$/', $cpu['core_model_tag'], $matches)) {
        $cpu['core_model_tag'] = $matches[1];
    }
}

// Print statistics after ensuring uniqueness
echo "<br>After ensuring uniqueness:<br>";
print_tag_stats($cpus, true);

// Write the processed data to the new file
file_put_contents(__DIR__ . '/../data/cpus_details.json', json_encode($cpus, JSON_PRETTY_PRINT));

echo "<br>Processing complete. Added tags to " . count($cpus) . " CPUs.<br>";
