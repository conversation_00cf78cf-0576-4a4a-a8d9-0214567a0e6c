<?php
/**
 * Generate an optimized CPU data file for faster matching
 * This script pre-processes the CPU data and creates indexes for efficient lookups
 */

// Read the CPU data file
$cpu_json_path = __DIR__ . '/../data/cpus_identifiers.json';

if (!file_exists($cpu_json_path)) {
    die("Error: CPU data file not found at {$cpu_json_path}<br>");
}

// Read and decode JSON file
$cpu_json_data = file_get_contents($cpu_json_path);
$cpu_data = json_decode($cpu_json_data, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    die("Error decoding CPU JSON: " . json_last_error_msg() . "<br>");
}

if (!is_array($cpu_data)) {
    die("Error: CPU data is not an array<br>");
}

echo "Loaded " . count($cpu_data) . " CPU database entries<br>";
echo "Generating optimized matching data...<br>";

// Helper functions
function normalizeText($text) {
    $text = strtolower($text);
    
    // Standardize version notation (v2, v3, etc.)
    $text = preg_replace('/\bv(\d+)\b/i', 'v$1', $text); // Ensure 'v' is lowercase
    $text = preg_replace('/\s+v(\d+)\b/i', 'v$1', $text); // Remove space before v
    
    // Standardize Intel Core i-series notation
    $text = preg_replace('/\bi(\d)\s*-\s*(\d+)([a-z]*)\b/i', 'i$1-$2$3', $text);
    
    $text = preg_replace('/[^a-z0-9\s\-v]/', '', $text); // Remove special characters except spaces, hyphens, and 'v'
    $text = preg_replace('/\s+/', ' ', $text); // Normalize whitespace
    $text = trim($text);
    return $text;
}

function extractModelNumber($name) {
    $normalized = normalizeText($name);
    
    // Try to extract Intel Core i-series model numbers (e.g., i5-6600T)
    if (preg_match('/\bi\d-(\d+)([a-z]*)\b/', $normalized, $matches)) {
        $baseNumber = $matches[1];
        $suffix = isset($matches[2]) ? $matches[2] : '';
        
        return [
            'full' => $baseNumber . $suffix,
            'base' => $baseNumber
        ];
    }
    
    // Try to extract Xeon model numbers (e.g., e3-1280, e5-1650)
    if (preg_match('/\be\d-(\d+)\b/', $normalized, $matches)) {
        return [
            'full' => $matches[1],
            'base' => $matches[1]
        ];
    }
    
    // Try to extract AMD Ryzen model numbers (e.g., 5600X)
    if (preg_match('/\bryzen\s+\d+\s+(\d+)([a-z0-9]*)\b/', $normalized, $matches)) {
        $baseNumber = $matches[1];
        $suffix = isset($matches[2]) ? $matches[2] : '';
        
        return [
            'full' => $baseNumber . $suffix,
            'base' => $baseNumber
        ];
    }
    
    return null; // No model number found
}

function extractVersionInfo($name) {
    $normalized = normalizeText($name);
    if (preg_match('/\bv(\d+)\b/', $normalized, $matches)) {
        return $matches[1]; // Return the version number
    }
    return null; // No version found
}

// Create optimized data structure
$optimized_data = [
    'cpus' => $cpu_data, // Keep the original CPU data
    'indexes' => [
        'simple_rules' => [],
        'compound_rules' => [],
        'model_numbers' => [],
        'brands' => [],
        'versions' => []
    ]
];

// Build indexes
foreach ($cpu_data as $index => $cpu) {
    // Skip CPUs without matching rules
    if (!isset($cpu['matching_rules'])) {
        continue;
    }
    
    // Extract version information
    $version = extractVersionInfo($cpu['name']);
    if ($version !== null) {
        if (!isset($optimized_data['indexes']['versions'][$version])) {
            $optimized_data['indexes']['versions'][$version] = [];
        }
        $optimized_data['indexes']['versions'][$version][] = $index;
    }
    
    // Extract brand information
    if (isset($cpu['brand_tag'])) {
        $brand = strtolower(trim($cpu['brand_tag']));
        if (!isset($optimized_data['indexes']['brands'][$brand])) {
            $optimized_data['indexes']['brands'][$brand] = [];
        }
        $optimized_data['indexes']['brands'][$brand][] = $index;
    }
    
    // Index simple rules
    if (isset($cpu['matching_rules']['simple'])) {
        foreach ($cpu['matching_rules']['simple'] as $rule) {
            $normalizedRule = normalizeText($rule);
            if (!isset($optimized_data['indexes']['simple_rules'][$normalizedRule])) {
                $optimized_data['indexes']['simple_rules'][$normalizedRule] = [];
            }
            $optimized_data['indexes']['simple_rules'][$normalizedRule][] = $index;
        }
    }
    
    // Index compound rules
    if (isset($cpu['matching_rules']['compound_and'])) {
        foreach ($cpu['matching_rules']['compound_and'] as $rule) {
            if (count($rule) !== 2) continue;
            
            // Index the full compound rule
            $key = implode('|', array_map('normalizeText', $rule));
            if (!isset($optimized_data['indexes']['compound_rules'][$key])) {
                $optimized_data['indexes']['compound_rules'][$key] = [];
            }
            $optimized_data['indexes']['compound_rules'][$key][] = $index;
            
            // Also index by the first part (usually brand or series)
            $firstPart = normalizeText($rule[0]);
            if (!isset($optimized_data['indexes']['compound_rules'][$firstPart])) {
                $optimized_data['indexes']['compound_rules'][$firstPart] = [];
            }
            $optimized_data['indexes']['compound_rules'][$firstPart][] = $index;
            
            // Also index by the second part (usually model number)
            $secondPart = normalizeText($rule[1]);
            if (!isset($optimized_data['indexes']['compound_rules'][$secondPart])) {
                $optimized_data['indexes']['compound_rules'][$secondPart] = [];
            }
            $optimized_data['indexes']['compound_rules'][$secondPart][] = $index;
        }
    }
    
    // Index by model number
    $modelNumber = extractModelNumber($cpu['name']);
    if ($modelNumber !== null) {
        if (isset($modelNumber['base'])) {
            $baseModel = $modelNumber['base'];
            if (!isset($optimized_data['indexes']['model_numbers'][$baseModel])) {
                $optimized_data['indexes']['model_numbers'][$baseModel] = [];
            }
            $optimized_data['indexes']['model_numbers'][$baseModel][] = $index;
        }
        
        if (isset($modelNumber['full'])) {
            $fullModel = $modelNumber['full'];
            if (!isset($optimized_data['indexes']['model_numbers'][$fullModel])) {
                $optimized_data['indexes']['model_numbers'][$fullModel] = [];
            }
            $optimized_data['indexes']['model_numbers'][$fullModel][] = $index;
        }
    }
}

// Add metadata
$optimized_data['metadata'] = [
    'generated_at' => date('Y-m-d H:i:s'),
    'source_file' => $cpu_json_path,
    'cpu_count' => count($cpu_data),
    'index_counts' => [
        'simple_rules' => count($optimized_data['indexes']['simple_rules']),
        'compound_rules' => count($optimized_data['indexes']['compound_rules']),
        'model_numbers' => count($optimized_data['indexes']['model_numbers']),
        'brands' => count($optimized_data['indexes']['brands']),
        'versions' => count($optimized_data['indexes']['versions'])
    ]
];

// Save the optimized data to a new file
$output_path = __DIR__ . '/../data/cpus_optimized_for_matching.json';
file_put_contents($output_path, json_encode($optimized_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));

echo "Optimized matching data generated successfully!<br>";
echo "Saved to: {$output_path}<br>";
echo "Index statistics:<br>";
echo "  - Simple rules: " . $optimized_data['metadata']['index_counts']['simple_rules'] . "<br>";
echo "  - Compound rules: " . $optimized_data['metadata']['index_counts']['compound_rules'] . "<br>";
echo "  - Model numbers: " . $optimized_data['metadata']['index_counts']['model_numbers'] . "<br>";
echo "  - Brands: " . $optimized_data['metadata']['index_counts']['brands'] . "<br>";
echo "  - Versions: " . $optimized_data['metadata']['index_counts']['versions'] . "<br>";
