<?php
$json_data = file_get_contents('cpu/data/cpus_identifiers.json');
$cpus = json_decode($json_data, true);

$empty_brand = array_filter($cpus, function($cpu) { return $cpu['brand_tag'] == ''; });
$empty_series = array_filter($cpus, function($cpu) { return $cpu['series_tag'] == ''; });
$empty_core = array_filter($cpus, function($cpu) { return $cpu['core_model_tag'] == ''; });

echo 'CPUs with empty brand_tag: ' . count($empty_brand) . PHP_EOL;
echo 'CPUs with empty series_tag: ' . count($empty_series) . PHP_EOL;
echo 'CPUs with empty core_model_tag: ' . count($empty_core) . PHP_EOL;
