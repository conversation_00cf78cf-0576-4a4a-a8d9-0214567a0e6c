<?php
$json_data = file_get_contents('cpu/data/cpus_identifiers.json');
$cpus = json_decode($json_data, true);

$empty_simple = array_filter($cpus, function($cpu) { return empty($cpu['matching_rules']['simple']); });
$empty_compound = array_filter($cpus, function($cpu) { return empty($cpu['matching_rules']['compound_and']); });

// echo 'CPUs with empty simple rules: ' . count($empty_simple) . PHP_EOL;
// echo 'CPUs with empty compound rules: ' . count($empty_compound) . PHP_EOL;

// List CPUs with empty simple rules
// echo "\nCPUs with empty simple rules:\n";
// foreach ($empty_simple as $cpu) {
//     echo $cpu['name'] . PHP_EOL;
// }

// // List CPUs with empty compound rules
// echo "\nCPUs with empty compound rules:\n";
// foreach ($empty_compound as $cpu) {
//     echo $cpu['name'] . PHP_EOL;
// }

// List CPUs with both empty simple and compound rules
echo "\nCPUs with both empty simple and compound rules:\n";
foreach (array_intersect_key($empty_simple, $empty_compound) as $cpu) {
    echo $cpu['name'] . PHP_EOL;
}
