<?php
/**
 * This script extracts the CPU names from the cpus.json file and outputs a comma-separated string.
 */

$input_file = __DIR__ . '/../data/cpus.json';

if (!file_exists($input_file)) {
    echo "Error: File not found at: $input_file\n";
    exit(1);
}

$json_data = file_get_contents($input_file);
$cpu_data = json_decode($json_data, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "Error parsing JSON: " . json_last_error_msg() . "\n";
    exit(1);
}

$cpu_names = array_column($cpu_data, 'name');
$cpu_names_string = implode(', ', $cpu_names);

echo $cpu_names_string;
