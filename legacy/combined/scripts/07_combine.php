<?php
/*
** Take the CPU details and merge it with the matched CPU data into a single JSON file
*/

// Read the matched data
$matched_json_data = file_get_contents(__DIR__ . '/../../amazon/data/amazon.json');
$matched_data = json_decode($matched_json_data, true);

// Read the matched CPU data
$matched_cpu_json_data = file_get_contents(__DIR__ . '/../data/matched_cpus.json');
$matched_cpu_data = json_decode($matched_cpu_json_data, true);

// Read the CPU database
$cpu_json_data = file_get_contents(__DIR__ . '/../../cpu/data/cpus.json');
$cpu_data = json_decode($cpu_json_data, true);

// Create an index of CPUs by ID for faster lookup
$cpu_index = [];
foreach ($cpu_data as $cpu) {
    $cpu_index[$cpu['id']] = $cpu;
}

// Read the sockets data
$socket_data = file_get_contents(__DIR__ . '/../../data/sockets.json');
$socket_data = json_decode($socket_data, true);

// Function to detect brand from text
function detectBrand($text) {
    $text = strtolower($text);

    // Check for explicit brand mentions
    if (stripos($text, 'intel') !== false) {
        return 'Intel';
    }
    if (stripos($text, 'amd') !== false) {
        return 'AMD';
    }

    // Check for Intel processor families
    $intel_families = ['core i', 'i3', 'i5', 'i7', 'i9', 'celeron', 'pentium', 'xeon'];
    foreach ($intel_families as $family) {
        if (stripos($text, $family) !== false) {
            return 'Intel';
        }
    }

    // Check for AMD processor families
    $amd_families = ['ryzen', 'threadripper', 'athlon', 'phenom', 'opteron', 'sempron', 'turion', 'fx', 'epyc'];
    foreach ($amd_families as $family) {
        if (stripos($text, $family) !== false) {
            return 'AMD';
        }
    }

    return null; // Brand couldn't be determined
}

// Function to get socket manufacturer
function getSocketManufacturer($socket_slug, $socket_data) {
    foreach ($socket_data['grouped']['desktop'] as $manufacturer => $sockets) {
        foreach ($sockets as $socket) {
            if ($socket['slug'] === $socket_slug) {
                return $manufacturer;
            }
        }
    }

    foreach ($socket_data['grouped']['server'] as $manufacturer => $sockets) {
        foreach ($sockets as $socket) {
            if ($socket['slug'] === $socket_slug) {
                return $manufacturer;
            }
        }
    }

    return null;
}

$combined_data = [];

foreach ($matched_cpu_data as $matched) {

    // Skip if CPU ID is not set
    if (!isset($matched['cpu_id']) || $matched['cpu_id'] === null) {
        continue;
    }

    // Find the corresponding CPU data
    $cpuId = $matched['cpu_id'];

    // Check if there are multiple versions of this CPU with different CPU counts
    $cpuVersions = [];
    foreach ($cpu_data as $cpuItem) {
        if ($cpuItem['id'] === $cpuId) {
            $cpuVersions[] = $cpuItem;
        }
    }

    // If there's only one version, use it
    if (count($cpuVersions) === 1) {
        $cpu = $cpuVersions[0];
    }
    // If there are multiple versions, determine which one to use based on the product name
    else if (count($cpuVersions) > 1) {
        // Detect the number of CPUs in the configuration
        $detectedCpuCount = 1; // Default to 1 CPU

        // Check for explicit CPU count indicators
        if (preg_match('/\b(\d+)\s*x\b/i', $matched['amazon_name'], $matches)) {
            // Pattern like "2x", "4x", etc.
            $detectedCpuCount = intval($matches[1]);
        } else if (preg_match('/\b([1-8])[-\s](?:processor|cpu)s?\b/i', $matched['amazon_name'], $matches)) {
            // Pattern like "2-processor", "2 CPU", etc.
            // Limit to 1-8 to avoid matching model numbers like E5-2630
            $detectedCpuCount = intval($matches[1]);
        } else if (preg_match('/\b(dual|two|triple|three|quad|four|octa|eight)[-\s]+(?:processor|cpu)s?\b/i', $matched['amazon_name'], $matches)) {
            // Text-based count like "dual cpu", "quad cpu", "dual-processor", etc.
            $countMap = [
                'dual' => 2, 'two' => 2,
                'triple' => 3, 'three' => 3,
                'quad' => 4, 'four' => 4,
                'octa' => 8, 'eight' => 8
            ];
            $textCount = strtolower($matches[1]);
            if (isset($countMap[$textCount])) {
                $detectedCpuCount = $countMap[$textCount];
            }
        } else if (preg_match('/\bpair\b|\bmatching\s+pair\b/i', $matched['amazon_name'])) {
            // "Pair" indicates 2 CPUs
            $detectedCpuCount = 2;
        }

        // Special case for AMD Opteron 6328
        // When the listing mentions "Eight-Core" but the CPU only has 4 cores per CPU,
        // it's likely a dual-CPU configuration (2 x 4 cores = 8 cores total)
        if ($cpuId === '1982' && stripos($matched['amazon_name'], 'opteron 6328') !== false &&
            (stripos($matched['amazon_name'], 'eight-core') !== false ||
             stripos($matched['amazon_name'], 'eight core') !== false ||
             stripos($matched['amazon_name'], '8-core') !== false ||
             stripos($matched['amazon_name'], '8 core') !== false)) {
            $detectedCpuCount = 2;
        }

        // Special case for AMD Opteron 6284 SE
        // This is a single-CPU processor with 16 cores, but the database has it marked as a dual-CPU configuration
        // We need to override the CPU count to 1 when the listing mentions "sixteen-core processor"
        if ($cpuId === '2702') {
            // The AMD Opteron 6284 SE is a single CPU with 16 cores
            // The database incorrectly has it marked as a dual-CPU configuration
            $detectedCpuCount = 1;
            echo "DEBUG: Setting CPU count to 1 for AMD Opteron 6284 SE in listing: {$matched['amazon_name']}\n";
        }

        // Special case for Intel Xeon X5670
        // When the listing mentions "Matching Pair" or similar, it's likely a dual-CPU configuration
        // But we want to show the core count per CPU (6 cores), not the total (12 cores)
        if ($cpuId === '1307' &&
            (stripos($matched['amazon_name'], 'matching pair') !== false ||
             stripos($matched['amazon_name'], 'pair of') !== false)) {
            // Force CPU count to 1 to avoid doubling the core count
            $detectedCpuCount = 1;
            echo "DEBUG: Setting CPU count to 1 for Intel Xeon X5670 in listing (despite being a pair): {$matched['amazon_name']}\n";
        }

        // Special case for AMD Opteron 6238
        // This is a 12-core processor, but might be incorrectly listed as 6-core
        if ($cpuId === '1982' && stripos($matched['amazon_name'], 'opteron 6238') !== false) {
            // Force CPU count to 1 to ensure correct core count
            $detectedCpuCount = 1;
            echo "DEBUG: Setting CPU count to 1 for AMD Opteron 6238 in listing: {$matched['amazon_name']}\n";
        }

        // Special case for AMD Opteron 6376
        // This is a 16-core processor, but might be incorrectly listed as 8-core
        if (stripos($matched['amazon_name'], 'opteron 6376') !== false) {
            // Override the core count to 16
            $cpu['cores'] = 16;
            echo "DEBUG: Setting core count to 16 for AMD Opteron 6376 in listing: {$matched['amazon_name']}\n";
        }

        // Special case for AMD Opteron 6328
        // This is an 8-core processor, but might be incorrectly listed as 4-core
        if (stripos($matched['amazon_name'], 'opteron 6328') !== false) {
            // Override the core count to 8
            $cpu['cores'] = 8;
            echo "DEBUG: Setting core count to 8 for AMD Opteron 6328 in listing: {$matched['amazon_name']}\n";
        }

        // Special case for AMD FX-9370
        // This is an 8-core processor, but might be incorrectly listed as 4-core
        if (stripos($matched['amazon_name'], 'fx-9370') !== false ||
            (stripos($matched['amazon_name'], 'fx') !== false && stripos($matched['amazon_name'], '9370') !== false)) {
            // Override the core count to 8
            $cpu['cores'] = 8;
            echo "DEBUG: Setting core count to 8 for AMD FX-9370 in listing: {$matched['amazon_name']}\n";
        }

        // Special case for AMD FX-4350
        // This is a 4-core processor, but might be incorrectly listed as 2-core
        if (stripos($matched['amazon_name'], 'fx-4350') !== false ||
            (stripos($matched['amazon_name'], 'fx') !== false && stripos($matched['amazon_name'], '4350') !== false)) {
            // Override the core count to 4
            $cpu['cores'] = 4;
            echo "DEBUG: Setting core count to 4 for AMD FX-4350 in listing: {$matched['amazon_name']}\n";
        }

        // Debug output for CPUs with multiple versions
        echo "DEBUG: Found multiple versions for CPU ID {$cpuId} in listing: {$matched['amazon_name']}\n";
        echo "DEBUG: Detected CPU count: {$detectedCpuCount}\n";

        // Sort versions by CPU count
        usort($cpuVersions, function($a, $b) {
            $aCount = isset($a['cpu_count']) ? $a['cpu_count'] : 1;
            $bCount = isset($b['cpu_count']) ? $b['cpu_count'] : 1;
            return $aCount <=> $bCount; // Sort by CPU count (ascending)
        });

        // Find the best matching CPU version based on the detected CPU count
        $bestMatch = null;
        $bestMatchDiff = PHP_INT_MAX;

        foreach ($cpuVersions as $version) {
            $versionCpuCount = isset($version['cpu_count']) ? $version['cpu_count'] : 1;
            $diff = abs($versionCpuCount - $detectedCpuCount);

            // If we find an exact match, use it immediately
            if ($diff === 0) {
                $bestMatch = $version;
                break;
            }

            // Otherwise, keep track of the closest match
            if ($diff < $bestMatchDiff) {
                $bestMatch = $version;
                $bestMatchDiff = $diff;
            }
        }

        // If we found a match, use it
        if ($bestMatch !== null) {
            $cpu = $bestMatch;
            $cpuCount = isset($cpu['cpu_count']) ? $cpu['cpu_count'] : 1;
            echo "DEBUG: Selected CPU version with count: {$cpuCount}, mark: {$cpu['mark']} (detected count: {$detectedCpuCount})\n";
        }
        // If no match was found (shouldn't happen), fall back to the lowest CPU count
        else {
            $cpu = $cpuVersions[0]; // First version (lowest CPU count)
            $cpuCount = isset($cpu['cpu_count']) ? $cpu['cpu_count'] : 1;
            echo "DEBUG: Fallback to CPU version with count: {$cpuCount}, mark: {$cpu['mark']}\n";
        }
    }
    // No matching CPU found
    else {
        $cpu = null;
    }

    // Skip if CPU data is not found
    if (!$cpu) {
        continue;
    }

    // Check if the CPU is whitelisted
    if (isset($cpu['whitelist']) && $cpu['whitelist'] === true) {
        // CPU is explicitly whitelisted, proceed
    } else {
        // Find out if socket is in the whitelist
        $socket = $cpu['socket'];
        $socket_whitelist = isset($socket_data['variants_map'][$socket]) ? true : false;

        // Skip if socket is not in the whitelist and CPU is not explicitly whitelisted
        if (!$socket_whitelist) {
            continue;
        }
    }

    // Brand validation - ensure socket manufacturer matches CPU brand
    $amazonBrand = detectBrand($matched['amazon_name']);
    $cpuBrand = detectBrand($cpu['name']);
    $socketManufacturer = getSocketManufacturer($cpu['socket_slug'], $socket_data);

    // Skip if Amazon listing brand doesn't match CPU brand
    if ($amazonBrand && $cpuBrand && $amazonBrand !== $cpuBrand) {
        continue;
    }

    // Skip if CPU brand doesn't match socket manufacturer
    if ($cpuBrand && $socketManufacturer && $cpuBrand !== $socketManufacturer) {
        continue;
    }

    // Calculate the value (price is in format like $1,889.32)
    $price = '';
    if (!empty($matched['amazon_price'])) {
        // Remove $ sign and commas before converting to float
        $price = floatval(str_replace(['$', ','], ['', ''], $matched['amazon_price']));
    }
    $mark = $cpu['mark'] ? floatval($cpu['mark']) : '';
    $value = ($mark && $price) ? $mark / $price : '';

    // Determine the condition based on keywords in the product name
    $condition = 'new'; // Default condition

    // Convert name to lowercase for case-insensitive matching
    $lowerName = strtolower($matched['amazon_name']);

    // Check for keywords indicating a used condition
    $usedKeywords = ['renewed', 'refurbished', 'used', 'pre-owned', 'preowned', 'open box'];

    foreach ($usedKeywords as $keyword) {
        if (strpos($lowerName, $keyword) !== false) {
            $condition = 'used';
            break; // Exit the loop once we find a match
        }
    }

    // Combine the data
    // Use core_display if available (for hybrid CPUs with P+E cores)
    $coreDisplay = isset($cpu['core_display']) ? $cpu['core_display'] : $cpu['cores'];

    $combined_data[] = [
        'name'        => $matched['amazon_name'],
        'cpu'         => $cpu['name'],
        'value'       => $value,
        'price'       => $price,
        'mark'        => $cpu['mark'],
        'cores'       => $cpu['cores'], // This is already the total cores (not multiplied by CPU count)
        'core_display'=> $coreDisplay,  // Add core display field for P+E cores
        'cpu_count'   => isset($cpu['cpu_count']) ? $cpu['cpu_count'] : 1, // Include CPU count, default to 1
        'socket'      => $cpu['socket'],
        'socket_slug' => $cpu['socket_slug'],
        'type'        => $cpu['category'],
        'tdp'         => $cpu['tdp'],
        'condition'   => $condition, // Amazon - when API is available
        'warranty'    => '-',        // Amazon - when API is available
        'link'        => isset($matched['link']) ? $matched['link'] : ''
    ];
}

$json_data = json_encode($combined_data, JSON_PRETTY_PRINT);

file_put_contents(__DIR__ . '/../../data/cpu_data.json', $json_data);

echo "Combination complete. Processed " . count($combined_data) . " Amazon entries.<br>";

