<?php
// <PERSON>ript to extract all unique CPU types from cpu_data.json

// Read the CPU data
$cpu_data_path = __DIR__ . '/../data/cpu_data.json';
if (!file_exists($cpu_data_path)) {
    die("Error: CPU data file not found at {$cpu_data_path}\n");
}

$cpu_data = json_decode(file_get_contents($cpu_data_path), true);
if (json_last_error() !== JSON_ERROR_NONE) {
    die("Error decoding CPU data JSON: " . json_last_error_msg() . "\n");
}

// Extract all types
$types = [];
foreach ($cpu_data as $cpu) {
    if (isset($cpu['type']) && !empty($cpu['type'])) {
        $types[] = $cpu['type'];
    }
}

// Get unique types and sort them
$unique_types = array_unique($types);
sort($unique_types);

// Output as comma-separated list
echo implode(', ', $unique_types) . "\n";
