<?php
/**
 * This script takes the Amazon sample data in CSV format and converts it to JSON
 * 
 * 1. Takes /../data/amazon.csv and loops through each row
 * 2. Keep only name, link, price, price_1, price_2, price_decimal, reviews, reviews_count 
 * 2. Converts each row to an associative array
 * 3. Adds the row to the JSON data
 * 4. Saves the JSON data to /../data/amazon.json
 */

$csv_file = __DIR__ . '/../input/amazon.csv';
$json_file = __DIR__ . '/../data/amazon.json';

if (!file_exists($csv_file)) {
    echo "Error: File not found at: $csv_file\n";
    exit(1);
}

function clean_amazon_link($link) {
    $pos = strpos($link, "ref=");
    if ($pos !== false) {
        return substr($link, 0, $pos);
    }
    return $link;
}

// Read file contents and parse CSV with explicit parameters
$csv_data = array_map(function($line) {
    return str_getcsv($line, ",", '"', "\\");
}, file($csv_file));

$json_data = [];

foreach ($csv_data as $row) {
    // Skipt the first row (header)
    if ($row[0] === 'ASIN') {
        continue;
    }
    
    // Skip if link contains "/sspa/"
    if (strpos($row[0], "/sspa/") !== false) {
        continue;
    }

    // Clean the link by removing everything after "ref="
    $cleaned_link = clean_amazon_link($row[0]);

    $json_data[] = [
        'name' => $row[1],
        'link' => $cleaned_link,
        // 'tag' => $row[2],
        'price' => $row[6],
        'price_symbol' => $row[7],
        'price_1' => $row[8],
        'price_decimal' => $row[9],
        'price_2' => $row[10],
        // 'reviews_count' => $row[4]
    ];
}

$json_data = json_encode($json_data, JSON_PRETTY_PRINT);
file_put_contents($json_file, $json_data);

echo "Conversion complete. Output saved to: $json_file\n";
