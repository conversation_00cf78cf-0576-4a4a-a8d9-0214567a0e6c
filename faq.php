<?php
// Define ABSPATH to allow inclusion of header.php
define('ABSPATH', __DIR__);
require_once 'header.php';
?>

<div id="container">
    <div id="content">
        <h1>Frequently Asked Questions</h1>

        <h2>Where do these prices come from?</h2>
        <!-- <p>All pricing data is collected through <a href="https://webservices.amazon.com/paapi5/documentation/">Amazon's Product Advertising API</a>, which provides us with current marketplace information.</p> -->
        <p>All pricing data is collected directly from Amazon website.</p>

        <h2>How frequently is this site updated?</h2>
        <p>Our system automatically updates all pricing information every few hours.</p>

        <h2>Why doesn't this product appear on your site?</h2>
        <p>Our system currently matches approximately 60% of all CPU listings from Amazon. There are several reasons for this match rate:</p>
        <ul>
            <li><strong>Product focus:</strong> We only list desktop and server CPUs (not mobile or laptop processors) as these are typically replaceable components. We also exclude CPUs for very old sockets that are no longer commonly used.</li>
            <li><strong>Complex product naming:</strong> Amazon listings often contain inconsistent naming conventions, marketing terms, bundle information, and other text that makes exact matching difficult.</li>
            <li><strong>Quality control:</strong> We prioritize accuracy over quantity. Our matching algorithms are designed to avoid false positives, which means we'd rather exclude a product than incorrectly categorize it.</li>
            <li><strong>Filtering:</strong> We remove duplicates, fake products, spam, and out-of-stock listings, which accounts for approximately 1-2% of all listings.</li>
        </ul>
        <p>Our matching system uses a combination of exact matching, substring matching, and compound rule matching to identify CPUs. We continuously improve our algorithms to increase the match rate while maintaining accuracy.</p>
        <!-- <p>We are also limited by the functionality of the Amazon Product Advertising API, which restricts the number of items we can retrieve per query and the number of queries per day. This leads to the exclusion of many products with a low sales ranking.</p> -->
        <p>If there is a specific CPU that you believe should be listed but isn't appearing on our site, please email its <NAME_EMAIL></p>

        <h2>Do you need a graphic designer?</h2>
        <p>No. This site is designed to maximize information density, accessibility, and performance. More whitespace, colors, and icons won't help.</p>

        <h2>What about shipping?</h2>
        <p>The prices listed here don't include shipping and taxes, as we don't know where you live. The majority of these listings include free shipping, especially if you have <a href="https://www.amazon.com/tryprimefree">Amazon Prime</a> but make sure to check shipping costs before placing an order.</p>

        <h2>Who are you?</h2>
        <p><a href="/">cpu-prices.com</a> is developed and maintained by <a href="https://www.janpencik.com/">Jan Pencik</a>.</p>

        <h2 id="affiliate">Is this an ad?</h2>
        <p>This site participates in <a href="https://affiliate-program.amazon.com/">Amazon's Associate Program</a>. When you purchase products through our links, we earn a commission at no additional cost to you.</p>

        <h2>Need more information?</h2>
        <p>For any questions not covered here, please contact <NAME_EMAIL>.</p>
    </div>
</div>

<?php
// Footer already has ABSPATH check
require_once 'footer.php';
?>