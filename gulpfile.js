const gulp = require('gulp');
const cleanCSS = require('gulp-clean-css');
const uglify = require('gulp-uglify');
const del = require('del');
const sass = require('gulp-sass')(require('sass'));
const autoprefixer = require('gulp-autoprefixer');

// Clean dist directory
function clean() {
    return del(['dist/**/*']);
}

// Copy PHP files
function php() {
    return gulp.src('*.php')
        .pipe(gulp.dest('dist'));
}

// Copy data
function data() {
    return gulp.src('data/**/*', { base: '.' })
        .pipe(gulp.dest('dist'));
}

// Compile SCSS to CSS
function scss() {
    return gulp.src('src/scss/main.scss')
        .pipe(sass().on('error', sass.logError))
        .pipe(autoprefixer({
            cascade: false,
            grid: 'autoplace'
        }))
        .pipe(cleanCSS())
        .pipe(gulp.dest('assets/css'))
        .pipe(gulp.dest('dist/assets/css'));
}

// Minify JavaScript files
function js() {
    return gulp.src('src/js/app.js')
        .pipe(uglify())
        .pipe(gulp.dest('assets/js'))
        .pipe(gulp.dest('dist/assets/js'));
}

// Copy favicon
function favicon() {
    return gulp.src('src/favicon/**/*', { base: 'src' })
        .pipe(gulp.dest('assets'))
        .pipe(gulp.dest('dist/assets'));
}

// Copy robots.txt
function robots() {
    return gulp.src('robots.txt')
        .pipe(gulp.dest('dist'));
}

// Watch for changes
function watch() {
    gulp.watch('src/scss/**/*.scss', scss);
    gulp.watch('src/js/**/*.js', js);
}

// Define complex tasks
const build = gulp.series(
    clean,
    gulp.parallel(data, php, favicon, robots),
    gulp.parallel(scss, js)
);

// Export tasks
exports.scss = scss;
exports.js = js;
exports.watch = watch;
exports.build = build;
exports.default = build;
