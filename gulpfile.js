const gulp = require('gulp');
const del = require('del');

// Clean dist directory
function clean() {
    return del(['dist/**/*']);
}

// Copy PHP files
function php() {
    return gulp.src('*.php')
        .pipe(gulp.dest('dist'));
}

// Copy app files
function app() {
    return gulp.src('app/**/*', { base: '.' })
        .pipe(gulp.dest('dist'));
}

// Copy inc
function inc() {
    return gulp.src('inc/**/*', { base: '.' })
        .pipe(gulp.dest('dist'));
}

// Copy api
function api() {
    return gulp.src('api/**/*', { base: '.' })
        .pipe(gulp.dest('dist'));
}

// Copy assets
function assets() {
    return gulp.src('assets/**/*', { base: '.' })
        .pipe(gulp.dest('dist'));
}

// Copy config
function config() {
    return gulp.src('config/**/*', { base: '.' })
        .pipe(gulp.dest('dist'));
}

// Copy robots.txt
function robots() {
    return gulp.src('robots.txt')
        .pipe(gulp.dest('dist'));
}

// Define complex tasks
const build = gulp.series(
    clean,
    gulp.parallel(php, app, inc, api, assets, config, robots),
);

// Export tasks
exports.build = build;
exports.default = build;
