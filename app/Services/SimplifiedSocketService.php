<?php
/**
 * Simplified Socket Service
 *
 * A simplified version of the SocketService class
 * for use in the FinalDataService class
 */

namespace App\Services;

use PDO;
use Exception;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Simplified service for socket operations
 */
class SimplifiedSocketService {
    /**
     * @var PDO Database connection
     */
    private $db;

    /**
     * Constructor
     *
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * Get all sockets with their variants
     *
     * @return array Result with success status and data
     */
    public function getAllSockets() {
        try {
            // First, get all sockets
            $sql = "
                SELECT
                    id, name, slug, year, manufacturer, type, created_at, updated_at
                FROM
                    sockets
                ORDER BY
                    year DESC, manufacturer, name
            ";

            $stmt = $this->db->query($sql);
            $sockets = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Then get all variants for each socket
            if (!empty($sockets)) {
                $socketIds = array_column($sockets, 'id');

                // Initialize variants array for each socket
                foreach ($sockets as &$socket) {
                    $socket['variants'] = [];
                }
                unset($socket); // Unset reference to avoid issues

                // Get all variants in a single query
                $variantsSql = "
                    SELECT
                        socket_id, id, variant_name
                    FROM
                        socket_variants
                    WHERE
                        socket_id IN (" . implode(',', $socketIds) . ")
                    ORDER BY
                        socket_id, id
                ";

                $variantsStmt = $this->db->query($variantsSql);
                $variants = $variantsStmt->fetchAll(PDO::FETCH_ASSOC);

                // Group variants by socket_id
                $variantsBySocket = [];
                foreach ($variants as $variant) {
                    $socketId = $variant['socket_id'];
                    if (!isset($variantsBySocket[$socketId])) {
                        $variantsBySocket[$socketId] = [];
                    }
                    $variantsBySocket[$socketId][] = $variant['variant_name'];
                }

                // Add variants to their respective sockets
                foreach ($sockets as &$socket) {
                    $socketId = $socket['id'];
                    if (isset($variantsBySocket[$socketId])) {
                        $socket['variants'] = $variantsBySocket[$socketId];
                    }
                }
                unset($socket); // Unset reference
            }

            return [
                'success' => true,
                'data' => $sockets
            ];
        } catch (Exception $e) {
            error_log('Error retrieving sockets: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error retrieving sockets: ' . $e->getMessage()
            ];
        }
    }
}
