<?php
/**
 * CPU Data Service
 * Handles operations related to CPU data management
 */

namespace App\Services;

use PDO; // Required for AmazonSearchTermService

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Class for managing CPU data operations
 */
class CpuDataService {
    /**
     * Path to the CPU models JSON file
     * @var string
     */
    private $cpuModelsFile;
    private ?PDO $db; // Add PDO instance for database operations

    /**
     * Constructor
     * @param PDO|null $db Optional database connection
     */
    public function __construct(?PDO $db = null) {
        $this->cpuModelsFile = __DIR__ . '/../../data/cpu_models.json';
        $this->db = $db;
    }

    /**
     * Update CPU models data from JSON string
     *
     * @param string $jsonContent JSON string containing CPU models data
     * @return array Result with success status and message
     */
    public function updateCpuModelsFromJson($jsonContent) {
        // Validate JSON format
        $data = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => 'Invalid JSON format: ' . json_last_error_msg()
            ];
        }

        // Validate structure
        list($isValid, $errorMessage) = $this->validateCpuModelsStructure($data);
        if (!$isValid) {
            return [
                'success' => false,
                'message' => 'Invalid CPU models structure: ' . $errorMessage
            ];
        }

        // Create backup of existing file if it exists
        if (file_exists($this->cpuModelsFile)) {
            $backupFile = $this->cpuModelsFile . '.bak.' . date('YmdHis');
            if (!copy($this->cpuModelsFile, $backupFile)) {
                return [
                    'success' => false,
                    'message' => 'Failed to create backup of existing CPU models file.'
                ];
            }
        }

        // Ensure the data directory exists
        $dataDir = dirname($this->cpuModelsFile);
        if (!is_dir($dataDir)) {
            if (!mkdir($dataDir, 0755, true)) {
                return [
                    'success' => false,
                    'message' => 'Failed to create data directory.'
                ];
            }
        }

        // Check if directory is writable
        if (!is_writable($dataDir)) {
            return [
                'success' => false,
                'message' => 'Data directory is not writable.'
            ];
        }

        // Determine the count of CPU models
        $count = 0;
        if (isset($data['data']) && is_array($data['data'])) {
            $count = count($data['data']);
        } else if (is_array($data)) {
            $count = count($data);
        }

        // Write to file
        $result = file_put_contents($this->cpuModelsFile, $jsonContent);

        if ($result === false) {
            return [
                'success' => false,
                'message' => 'Failed to write CPU models data to file.'
            ];
        }

        // After successfully updating the JSON file, regenerate dependent data
        $this->regenerateDependentData();

        return [
            'success' => true,
            'message' => 'CPU models data updated successfully.',
            'count' => $count
        ];
    }

    /**
     * Trigger regeneration of related data after CPU models update.
     * Currently, this regenerates Amazon search terms.
     */
    private function regenerateDependentData()
    {
        if ($this->db) {
            try {
                // Ensure AmazonSearchTermService is available or autoloaded
                if (!class_exists('App\Services\AmazonSearchTermService')) {
                    // Attempt to load if not autoloaded (adjust path if necessary)
                    $servicePath = __DIR__ . '/AmazonSearchTermService.php';
                    if (file_exists($servicePath)) {
                        require_once $servicePath;
                    } else {
                        error_log("AmazonSearchTermService class not found and file does not exist at: " . $servicePath);
                        return;
                    }
                }
                $amazonSearchTermService = new AmazonSearchTermService($this->db);
                $result = $amazonSearchTermService->generateAndStoreSearchTerms();
                if ($result['success']) {
                    error_log("Successfully regenerated Amazon search terms after CPU data update. Count: " . $result['count']);
                } else {
                    error_log("Failed to regenerate Amazon search terms after CPU data update: " . $result['message']);
                }
            } catch (\Exception $e) {
                error_log("Exception during dependent data regeneration: " . $e->getMessage());
            }
        } else {
            error_log("Database connection not available in CpuDataService, cannot regenerate dependent data.");
        }
    }

    /**
     * Validate CPU models data structure
     *
     * @param array $data CPU models data
     * @return array [bool, string] True if structure is valid and error message if any
     */
    private function validateCpuModelsStructure($data) {
        // Check if data is an array
        if (!is_array($data)) {
            return [false, "Data must be an array"];
        }

        // Check if the data has the expected structure with "data" key
        if (isset($data['data']) && is_array($data['data'])) {
            // This is the correct format with a "data" key containing an array of CPU models
            $cpuModels = $data['data'];
        } else if (count($data) > 0 && isset($data[0]) && is_array($data[0])) {
            // This is a direct array of CPU models without the "data" key
            $cpuModels = $data;
        } else {
            return [false, "Invalid data structure. Expected either {\"data\": [...]} or direct array of CPU models"];
        }

        // Check if there are any CPU models
        if (empty($cpuModels)) {
            return [false, "No CPU models found in the data"];
        }

        // Check each CPU model
        foreach ($cpuModels as $index => $model) {
            // Required fields
            $requiredFields = ['name', 'id'];

            foreach ($requiredFields as $field) {
                if (!isset($model[$field])) {
                    return [false, "Missing required field '{$field}' in CPU model at index {$index}"];
                }
            }

            // Numeric fields validation (if present)
            // Note: We accept string values that could be numeric after removing commas
            // or values that are "NA" or empty
            $numericFields = ['cpumark', 'thread', 'tdp', 'value', 'threadValue', 'powerPerf', 'speed', 'turbo', 'rank', 'samples'];
            foreach ($numericFields as $field) {
                if (isset($model[$field]) && $model[$field] !== 'NA' && $model[$field] !== '') {
                    // Remove commas from values like "2,409"
                    $cleanValue = str_replace(',', '', $model[$field]);
                    // Check if it's numeric after cleaning
                    if (!is_numeric($cleanValue) && !is_numeric($model[$field])) {
                        return [false, "Field '{$field}' must be numeric (with or without commas), 'NA', or empty in CPU model '{$model['name']}'. Got: '{$model[$field]}'"];
                    }
                }
            }
        }

        return [true, ""];
    }

    /**
     * Get CPU models data
     *
     * @return array CPU models data or empty array if file doesn't exist
     */
    public function getCpuModels() {
        if (!file_exists($this->cpuModelsFile)) {
            return [];
        }

        $jsonContent = file_get_contents($this->cpuModelsFile);
        $data = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [];
        }

        return $data;
    }

    /**
     * Get CPU models count
     *
     * @return int Number of CPU models
     */
    public function getCpuModelsCount() {
        $data = $this->getCpuModels();

        // Handle different data formats
        if (isset($data['data']) && is_array($data['data'])) {
            return count($data['data']);
        } else if (is_array($data)) {
            return count($data);
        }

        return 0;
    }
}
