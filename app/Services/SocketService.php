<?php
/**
 * Socket Service
 * Handles business logic for socket operations
 */

namespace App\Services;

use App\Repositories\SocketRepository;
use App\Utils\StringUtils;
use Exception;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Service for socket operations
 */
class SocketService
{
    /**
     * @var SocketRepository
     */
    private $socketRepository;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->socketRepository = new \App\Repositories\SocketRepository();
    }

    /**
     * Get all sockets with their variants
     *
     * @return array Result with success status and data
     */
    public function getAllSockets()
    {
        try {
            $sockets = $this->socketRepository->getAllSockets();

            return [
                'success' => true,
                'data' => $sockets
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get a single socket by ID with its variants
     *
     * @param int $socketId Socket ID
     * @return array Result with success status and data
     */
    public function getSocketById($socketId)
    {
        try {
            $socket = $this->socketRepository->getSocketById($socketId);

            return [
                'success' => true,
                'data' => $socket
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Add a new socket with variants
     *
     * @param array $socketData Socket data
     * @return array Result with success status and message
     */
    public function addSocket($socketData)
    {
        try {
            // Validate and sanitize input
            $socketData = $this->validateSocketData($socketData);

            // Add socket
            $socketId = $this->socketRepository->addSocket($socketData);

            // Regenerate socket data file
            $this->regenerateSocketDataFile();

            return [
                'success' => true,
                'message' => 'Socket added successfully',
                'socketId' => $socketId
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update an existing socket and its variants
     *
     * @param int $socketId Socket ID
     * @param array $socketData Socket data
     * @return array Result with success status and message
     */
    public function updateSocket($socketId, $socketData)
    {
        try {
            // Validate and sanitize input
            $socketData = $this->validateSocketData($socketData);

            // Update socket
            $this->socketRepository->updateSocket($socketId, $socketData);

            // Regenerate socket data file
            $this->regenerateSocketDataFile();

            return [
                'success' => true,
                'message' => 'Socket updated successfully'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a socket and its variants
     *
     * @param int $socketId Socket ID
     * @return array Result with success status and message
     */
    public function deleteSocket($socketId)
    {
        try {
            // Delete socket
            $this->socketRepository->deleteSocket($socketId);

            // Regenerate socket data file
            $this->regenerateSocketDataFile();

            return [
                'success' => true,
                'message' => 'Socket deleted successfully'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Add a new socket variant
     *
     * @param int $socketId Socket ID
     * @param string $variantName Variant name
     * @return array Result with success status and message
     */
    public function addSocketVariant($socketId, $variantName)
    {
        try {
            // Validate variant name
            if (empty($variantName)) {
                throw new Exception('Variant name is required');
            }

            // Add variant
            $variantId = $this->socketRepository->addSocketVariant($socketId, $variantName);

            // Regenerate socket data file
            $this->regenerateSocketDataFile();

            return [
                'success' => true,
                'message' => 'Variant added successfully',
                'variantId' => $variantId
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update a socket variant
     *
     * @param int $variantId Variant ID
     * @param string $variantName New variant name
     * @return array Result with success status and message
     */
    public function updateSocketVariant($variantId, $variantName)
    {
        try {
            // Validate variant name
            if (empty($variantName)) {
                throw new Exception('Variant name is required');
            }

            // Update variant
            $this->socketRepository->updateSocketVariant($variantId, $variantName);

            // Regenerate socket data file
            $this->regenerateSocketDataFile();

            return [
                'success' => true,
                'message' => 'Variant updated successfully'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a socket variant
     *
     * @param int $variantId Variant ID
     * @return array Result with success status and message
     */
    public function deleteSocketVariant($variantId)
    {
        try {
            // Delete variant
            $this->socketRepository->deleteSocketVariant($variantId);

            // Regenerate socket data file
            $this->regenerateSocketDataFile();

            return [
                'success' => true,
                'message' => 'Variant deleted successfully'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate and sanitize socket data
     *
     * @param array $socketData Socket data
     * @return array Validated and sanitized socket data
     * @throws Exception If validation fails
     */
    private function validateSocketData($socketData)
    {
        // Validate required fields
        $requiredFields = ['name', 'year', 'manufacturer', 'type'];
        foreach ($requiredFields as $field) {
            if (empty($socketData[$field])) {
                throw new Exception("Field '{$field}' is required");
            }
        }

        // Validate manufacturer
        if (!in_array($socketData['manufacturer'], ['Intel', 'AMD'])) {
            throw new Exception("Manufacturer must be 'Intel' or 'AMD'");
        }

        // Validate type
        if (!in_array($socketData['type'], ['desktop', 'server'])) {
            throw new Exception("Type must be 'desktop' or 'server'");
        }

        // Validate year
        if (!is_numeric($socketData['year']) || $socketData['year'] < 1990 || $socketData['year'] > 2100) {
            throw new Exception("Year must be a valid year between 1990 and 2100");
        }

        // Generate slug if not provided
        if (empty($socketData['slug'])) {
            $socketData['slug'] = StringUtils::slugify($socketData['name']);
        } else {
            // Validate slug format
            if (!StringUtils::isValidAlphanumeric($socketData['slug'], '-')) {
                throw new Exception("Slug must contain only alphanumeric characters and hyphens");
            }
        }

        // Sanitize variants
        if (isset($socketData['variants']) && is_array($socketData['variants'])) {
            // Filter out empty variants
            $socketData['variants'] = array_filter($socketData['variants'], function($variant) {
                return !empty($variant);
            });
        } else {
            $socketData['variants'] = [];
        }

        return $socketData;
    }

    /**
     * Regenerate the socket data JSON file
     *
     * @return bool True on success, false on failure
     */
    public function regenerateSocketDataFile()
    {
        try {
            // Get all sockets with their variants
            $sockets = $this->socketRepository->getAllSockets();

            // Save to JSON file
            $jsonData = json_encode($sockets, JSON_PRETTY_PRINT);
            $targetFile = __DIR__ . '/../../data/sockets.json';

            // Create directory if it doesn't exist
            $targetDir = dirname($targetFile);
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
            }

            $success = file_put_contents($targetFile, $jsonData) !== false;

            // Regenerate whitelisted data
            if ($success) {
                $this->regenerateWhitelistedData();
            }

            return $success;
        } catch (Exception $e) {
            error_log('Error regenerating socket data file: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Regenerate the whitelisted CPU data
     *
     * @return bool True on success, false on failure
     */
    private function regenerateWhitelistedData()
    {
        try {
            // Create a new instance of FinalDataService
            $finalDataService = new \App\Services\FinalDataService(null);

            // Generate and save the final data
            $result = $finalDataService->generateAndSaveFinalData();

            if ($result['success']) {
                error_log('Whitelisted data regenerated successfully after socket modification');
                return true;
            } else {
                error_log('Error regenerating whitelisted data: ' . $result['message']);
                return false;
            }
        } catch (Exception $e) {
            error_log('Exception regenerating whitelisted data: ' . $e->getMessage());
            return false;
        }
    }
}
