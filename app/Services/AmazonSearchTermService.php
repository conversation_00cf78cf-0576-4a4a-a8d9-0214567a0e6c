<?php

namespace App\Services;

use App\Repositories\AmazonSearchTermRepository;
use App\Repositories\CpuModelRepository;
use App\Repositories\SocketRepository;
use PDO;

class AmazonSearchTermService
{
    private AmazonSearchTermRepository $amazonSearchTermRepository;
    private CpuModelRepository $cpuModelRepository;
    private SocketRepository $socketRepository;

    public function __construct(PDO $db)
    {
        $this->amazonSearchTermRepository = new AmazonSearchTermRepository($db);
        $this->cpuModelRepository = new CpuModelRepository($db);
        $this->socketRepository = new SocketRepository();
    }

    /**
     * Generate brand + socket variant combinations for Amazon search terms.
     * @return array Array of search term data arrays
     */
    private function generateBrandSocketCombinations(): array
    {
        try {
            // Get unique brands from whitelisted CPUs
            $cpuModels = $this->cpuModelRepository->getWhitelisted();
            $brands = [];

            foreach ($cpuModels as $cpu) {
                $brand = trim($cpu['brand_tag'] ?? '');
                if (!empty($brand) && !in_array($brand, $brands)) {
                    $brands[] = $brand;
                }
            }

            // Get all socket variants
            $socketVariants = $this->socketRepository->getAllSocketVariants();

            // Generate combinations
            $combinations = [];

            foreach ($brands as $brand) {
                foreach ($socketVariants as $variant) {
                    // Only create combinations where brand matches socket manufacturer
                    // Intel brand with Intel sockets, AMD brand with AMD sockets
                    if (
                        ($brand === 'Intel' && $variant['manufacturer'] === 'Intel') ||
                        ($brand === 'AMD' && $variant['manufacturer'] === 'AMD')
                    ) {
                        $variantName = $variant['variant_name'];

                        // Check if the variant name contains any of the delimiters
                        if (preg_match('/(,\s*|\s*\/\s*)/', $variantName)) {
                            error_log("Found delimiter in socket variant: " . $variantName);

                            // Split the variant name by the delimiters
                            $parts = preg_split('/(,\s*|\s*\/\s*)/', $variantName);
                            error_log("Split into " . count($parts) . " parts: " . implode(", ", $parts));

                            // Create a search term for each part
                            foreach ($parts as $part) {
                                $part = trim($part);
                                if (!empty($part)) {
                                    $keywords = $part;
                                    $generatedTerm = $brand . ' ' . $keywords;

                                    $combinations[] = [
                                        'brand_tag' => $brand,
                                        'keywords' => $keywords,
                                        'generated_search_term' => $generatedTerm,
                                    ];
                                    error_log("Added search term: " . $generatedTerm);
                                }
                            }

                            // Also add the original full variant name as a search term
                            $combinations[] = [
                                'brand_tag' => $brand,
                                'keywords' => $variantName,
                                'generated_search_term' => $brand . ' ' . $variantName,
                            ];
                            error_log("Added original search term: " . $brand . ' ' . $variantName);
                        } else {
                            // No delimiters, just add the variant name as is
                            $keywords = $variantName;
                            $generatedTerm = $brand . ' ' . $keywords;

                            $combinations[] = [
                                'brand_tag' => $brand,
                                'keywords' => $keywords,
                                'generated_search_term' => $generatedTerm,
                            ];
                        }
                    }
                }
            }

            return $combinations;
        } catch (\Exception $e) {
            error_log("Error generating brand-socket combinations: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Generate and store Amazon search terms.
     * It clears existing auto-generated terms and generates new ones based on whitelisted cpu_models.
     * Manual terms are preserved.
     * @return array ['success' => bool, 'message' => string, 'count' => int]
     */
    public function generateAndStoreSearchTerms(): array
    {
        try {
            // Get existing auto-generated search terms' active status before clearing
            $preservedActiveStatus = $this->amazonSearchTermRepository->getAutoTermsActiveStatus();

            // Clear existing auto-generated search terms only
            $this->amazonSearchTermRepository->clearAutoTerms();

            // Fetch only whitelisted CPU models
            $cpuModels = $this->cpuModelRepository->getWhitelisted();

            if (empty($cpuModels)) {
                return ['success' => true, 'message' => 'No whitelisted CPU models found to generate search terms.', 'count' => 0];
            }

            $searchTermsToInsert = [];

            // Generate CPU-based search terms
            foreach ($cpuModels as $cpu) {
                $brand = trim($cpu['brand_tag'] ?? '');
                $series = trim($cpu['series_tag'] ?? '');
                $coreModel = trim($cpu['core_model_tag'] ?? '');

                if (empty($brand)) {
                    continue; // Skip if brand is not defined
                }

                // Combination 1: Brand + Series
                if (!empty($series)) {
                    $keywords = $series;
                    $generatedTerm = $brand . ' ' . $keywords;
                    $searchTermsToInsert[] = [
                        'brand_tag' => $brand,
                        'keywords' => $keywords,
                        'generated_search_term' => $generatedTerm,
                    ];
                }

                // Combination 2: Brand + Core Model
                if (!empty($coreModel)) {
                    $keywords = $coreModel;
                    $generatedTerm = $brand . ' ' . $keywords;
                    // Avoid duplicate if series and core_model are the same
                    if (empty($series) || $series !== $coreModel) {
                         $searchTermsToInsert[] = [
                            'brand_tag' => $brand,
                            'keywords' => $keywords,
                            'generated_search_term' => $generatedTerm,
                        ];
                    }
                }
            }

            // Add brand + socket variant combinations
            $socketCombinations = $this->generateBrandSocketCombinations();
            error_log("Generated " . count($socketCombinations) . " brand + socket variant combinations");
            $searchTermsToInsert = array_merge($searchTermsToInsert, $socketCombinations);

            if (empty($searchTermsToInsert)) {
                return ['success' => true, 'message' => 'No valid search terms generated from whitelisted CPU models.', 'count' => 0];
            }

            // Remove duplicate generated_search_term entries before inserting
            $uniqueSearchTerms = [];
            $seenTerms = [];
            foreach ($searchTermsToInsert as $term) {
                if (!isset($seenTerms[$term['generated_search_term']])) {
                    $uniqueSearchTerms[] = $term;
                    $seenTerms[$term['generated_search_term']] = true;
                }
            }


            $success = $this->amazonSearchTermRepository->bulkInsert($uniqueSearchTerms, $preservedActiveStatus);

            if ($success) {
                return ['success' => true, 'message' => 'Amazon search terms generated successfully from whitelisted CPUs and socket variants.', 'count' => count($uniqueSearchTerms)];
            } else {
                return ['success' => false, 'message' => 'Failed to store generated Amazon search terms.', 'count' => 0];
            }
        } catch (\Exception $e) {
            error_log("Error generating Amazon search terms: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while generating search terms: ' . $e->getMessage(), 'count' => 0];
        }
    }

    /**
     * Get all search terms.
     * @return array
     */
    public function getAllSearchTerms(): array
    {
        return $this->amazonSearchTermRepository->getAll();
    }

    /**
     * Update the product count for a search term.
     * @param int $id The ID of the search term
     * @param int $productCount The number of products found for this search term
     * @return array ['success' => bool, 'message' => string]
     */
    public function updateProductCount(int $id, int $productCount): array
    {
        try {
            $success = $this->amazonSearchTermRepository->updateProductCount($id, $productCount);

            if ($success) {
                return [
                    'success' => true,
                    'message' => "Product count updated successfully for search term ID {$id}."
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Failed to update product count for search term ID {$id}."
                ];
            }
        } catch (\Exception $e) {
            error_log("Error updating product count: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "An error occurred while updating product count: " . $e->getMessage()
            ];
        }
    }

    /**
     * Add a manual search term.
     * @param string|null $brandTag
     * @param string|null $keywords
     * @param string $generatedSearchTerm
     * @return array ['success' => bool, 'message' => string, 'id' => int|null]
     */
    public function addManualSearchTerm(?string $brandTag, ?string $keywords, string $generatedSearchTerm): array
    {
        try {
            // Validate input
            if (empty($generatedSearchTerm)) {
                return [
                    'success' => false,
                    'message' => "Search term cannot be empty.",
                    'id' => null
                ];
            }

            // Check if a search term with the same generated_search_term already exists
            $existingTerms = $this->amazonSearchTermRepository->getAll();
            foreach ($existingTerms as $term) {
                if (strtolower($term['generated_search_term']) === strtolower($generatedSearchTerm)) {
                    return [
                        'success' => false,
                        'message' => "A search term with this value already exists.",
                        'id' => null
                    ];
                }
            }

            // Add the manual search term
            $id = $this->amazonSearchTermRepository->add(
                $brandTag,
                $keywords,
                $generatedSearchTerm,
                0, // product_count
                'manual', // type
                true // active
            );

            if ($id) {
                return [
                    'success' => true,
                    'message' => "Manual search term added successfully.",
                    'id' => $id
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Failed to add manual search term.",
                    'id' => null
                ];
            }
        } catch (\Exception $e) {
            error_log("Error adding manual search term: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "An error occurred while adding manual search term: " . $e->getMessage(),
                'id' => null
            ];
        }
    }

    /**
     * Update a manual search term.
     * @param int $id The ID of the search term
     * @param string|null $brandTag
     * @param string|null $keywords
     * @param string $generatedSearchTerm
     * @return array ['success' => bool, 'message' => string]
     */
    public function updateManualSearchTerm(int $id, ?string $brandTag, ?string $keywords, string $generatedSearchTerm): array
    {
        try {
            // Validate input
            if (empty($generatedSearchTerm)) {
                return [
                    'success' => false,
                    'message' => "Search term cannot be empty."
                ];
            }

            // Get the search term to update
            $searchTerm = $this->amazonSearchTermRepository->getById($id);
            if (!$searchTerm) {
                return [
                    'success' => false,
                    'message' => "Search term not found."
                ];
            }

            // Check if this is a manual search term
            if ($searchTerm['type'] !== 'manual') {
                return [
                    'success' => false,
                    'message' => "Only manual search terms can be updated."
                ];
            }

            // Check if a different search term with the same generated_search_term already exists
            $existingTerms = $this->amazonSearchTermRepository->getAll();
            foreach ($existingTerms as $term) {
                if ($term['id'] !== $id && strtolower($term['generated_search_term']) === strtolower($generatedSearchTerm)) {
                    return [
                        'success' => false,
                        'message' => "A search term with this value already exists."
                    ];
                }
            }

            // Update the search term
            $success = $this->amazonSearchTermRepository->updateSearchTerm(
                $id,
                $brandTag,
                $keywords,
                $generatedSearchTerm
            );

            if ($success) {
                return [
                    'success' => true,
                    'message' => "Search term updated successfully."
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Failed to update search term."
                ];
            }
        } catch (\Exception $e) {
            error_log("Error updating search term: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "An error occurred while updating search term: " . $e->getMessage()
            ];
        }
    }

    /**
     * Toggle the active status of a search term.
     * @param int $id The ID of the search term
     * @return array ['success' => bool, 'message' => string, 'active' => bool|null]
     */
    public function toggleSearchTermActive(int $id): array
    {
        try {
            // Get the search term to toggle
            $searchTerm = $this->amazonSearchTermRepository->getById($id);
            if (!$searchTerm) {
                return [
                    'success' => false,
                    'message' => "Search term not found.",
                    'active' => null
                ];
            }

            // Toggle the active status
            $success = $this->amazonSearchTermRepository->toggleActive($id);

            if ($success) {
                // Get the updated search term to return the new active status
                $updatedTerm = $this->amazonSearchTermRepository->getById($id);
                $newActive = $updatedTerm ? (bool)$updatedTerm['active'] : null;

                return [
                    'success' => true,
                    'message' => "Search term " . ($newActive ? "enabled" : "disabled") . " successfully.",
                    'active' => $newActive
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Failed to toggle search term status.",
                    'active' => null
                ];
            }
        } catch (\Exception $e) {
            error_log("Error toggling search term status: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "An error occurred while toggling search term status: " . $e->getMessage(),
                'active' => null
            ];
        }
    }

    /**
     * Get a search term by ID.
     * @param int $id The ID of the search term
     * @return array ['success' => bool, 'message' => string, 'data' => array|null]
     */
    public function getSearchTermById(int $id): array
    {
        try {
            $searchTerm = $this->amazonSearchTermRepository->getById($id);

            if ($searchTerm) {
                return [
                    'success' => true,
                    'message' => "Search term retrieved successfully.",
                    'data' => $searchTerm
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Search term not found.",
                    'data' => null
                ];
            }
        } catch (\Exception $e) {
            error_log("Error retrieving search term: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "An error occurred while retrieving search term: " . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Delete a search term by ID.
     * @param int $id The ID of the search term to delete
     * @return array ['success' => bool, 'message' => string]
     */
    public function deleteSearchTerm(int $id): array
    {
        try {
            // Get the search term to delete
            $searchTerm = $this->amazonSearchTermRepository->getById($id);
            if (!$searchTerm) {
                return [
                    'success' => false,
                    'message' => "Search term not found."
                ];
            }

            // Delete the search term
            $success = $this->amazonSearchTermRepository->delete($id);

            if ($success) {
                return [
                    'success' => true,
                    'message' => "Search term deleted successfully."
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Failed to delete search term."
                ];
            }
        } catch (\Exception $e) {
            error_log("Error deleting search term: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "An error occurred while deleting search term: " . $e->getMessage()
            ];
        }
    }
}
