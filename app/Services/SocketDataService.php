<?php

declare(strict_types=1);

namespace App\Services;

use Exception;
use PDO; // Import the PDO class
use PDOException; // Import PDOException for specific DB error handling

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Service class responsible for processing socket data and generating
 * the corresponding JSON file for the frontend.
 */
class SocketDataService {
    private PDO $pdo; // Property to hold the database connection

    /**
     * Constructor - Inject the PDO database connection.
     *
     * @param PDO $pdo An active PDO database connection instance.
     */
    public function __construct(PDO $pdo) {
        // Set common PDO attributes if not already set
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $this->pdo = $pdo;
    }

    /**
     * Generates the socket_data.json file in the specified output directory.
     *
     * @param string $outputDir The absolute path to the directory where the JSON should be saved.
     * @return array An array indicating success status, count, and output path.
     * @throws Exception|PDOException If database access or file writing fails.
     */
    public function generateSocketDataFile(string $outputDir): array {
        $outputFilename = 'socket_data.json';
        $fullOutputPath = rtrim($outputDir, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $outputFilename;

        if (!is_dir($outputDir)) {
            if (!mkdir($outputDir, 0775, true)) {
                throw new Exception("Failed to create output directory: {$outputDir}");
            }
        }
        if (!is_writable($outputDir)) {
            throw new Exception("Output directory is not writable: {$outputDir}");
        }

        // Fetch data from the database now
        $rawSocketData = $this->getRawSocketDataFromDb(); // Use the new DB method

        if (empty($rawSocketData)) {
            // Handle case where DB might be empty or query failed silently (though exceptions should cover)
            echo "[WARNING] No socket data retrieved from the database.\n";
        }

        $processedData = $this->processSocketData($rawSocketData);

        $jsonData = json_encode($processedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

        if ($jsonData === false) {
            throw new Exception("Failed to encode socket data to JSON. Error: " . json_last_error_msg());
        }

        $bytesWritten = file_put_contents($fullOutputPath, $jsonData);

        if ($bytesWritten === false) {
            throw new Exception("Failed to write JSON data to file: {$fullOutputPath}");
        }

        return [
            'success' => true,
            'count' => count($rawSocketData),
            'output_path' => $fullOutputPath,
            'bytes_written' => $bytesWritten
        ];
    }

    /**
     * Processes the raw socket data array into the structured format needed for the JSON output.
     * (This method remains largely the same as it expects the specific input array format)
     *
     * @param array $rawData The raw socket data array (now fetched from DB).
     * @return array The structured data array.
     */
    private function processSocketData(array $rawData): array {
        $structuredData = [
            'grouped' => [
                'desktop' => [],
                'server' => []
            ]
        ];

        foreach ($rawData as $socket) {
            // Defensive check if socket structure is as expected
            if (!isset($socket['type'], $socket['manufacturer'])) {
                trigger_error("Skipping socket due to missing keys: " . print_r($socket, true), E_USER_WARNING);
                continue;
            }

            if (!isset($structuredData['grouped'][$socket['type']][$socket['manufacturer']])) {
                $structuredData['grouped'][$socket['type']][$socket['manufacturer']] = [];
            }
            $structuredData['grouped'][$socket['type']][$socket['manufacturer']][] = $socket;
        }

        foreach ($structuredData['grouped'] as $type => &$manufacturers) {
            ksort($manufacturers);
            foreach ($manufacturers as $manufacturer => &$sockets) {
                usort($sockets, fn($a, $b) => ($b['year'] ?? 0) <=> ($a['year'] ?? 0)); // Added null coalescing for safety
            }
        }
        unset($manufacturers, $sockets); // Unset references

        return $structuredData;
    }

    /**
     * Fetches socket data from the database and reconstructs it into the
     * original array format expected by processSocketData.
     *
     * @return array The raw socket data array.
     * @throws PDOException If the database query fails.
     */
    private function getRawSocketDataFromDb(): array {
        $sql = "
            SELECT
                s.id, s.name, s.slug, s.year, s.manufacturer, s.type,
                GROUP_CONCAT(sv.variant_name) AS variants_string
            FROM
                sockets s
            LEFT JOIN
                socket_variants sv ON s.id = sv.socket_id
            GROUP BY
                s.id, s.name, s.slug, s.year, s.manufacturer, s.type
            ORDER BY
                s.year DESC, s.manufacturer, s.name;
        ";

        try {
            $stmt = $this->pdo->query($sql);
            $results = $stmt->fetchAll(); // Default is PDO::FETCH_ASSOC due to constructor setting

            $rawData = [];
            foreach ($results as $row) {
                // Reconstruct the array structure
                $variants = [];
                if (!empty($row['variants_string'])) {
                    // Split the comma-separated string into an array
                    $variants = explode(',', $row['variants_string']);
                }

                $rawData[] = [
                    // 'id' => (int)$row['id'], // Keep original format, no ID needed there
                    'name' => $row['name'],
                    'slug' => $row['slug'],
                    'year' => (int)$row['year'], // Cast year to integer
                    'variants' => $variants,
                    'manufacturer' => $row['manufacturer'],
                    'type' => $row['type'],
                ];
            }

            return $rawData;
        } catch (PDOException $e) {
            // Log the error maybe, then re-throw
            error_log("Database error fetching socket data: " . $e->getMessage());
            throw $e; // Re-throw the exception to be handled by the calling method
        }
    }
}
