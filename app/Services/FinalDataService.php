<?php
/**
 * Final Data Service
 *
 * Handles the generation of the final CPU data with custom rules applied,
 * category processing, and socket matching.
 */

namespace App\Services;

use App\Repositories\CustomRuleRepository;
use App\Repositories\CpuModelRepository;
use App\Services\CustomRuleService;
use App\Services\SimplifiedSocketService;
use PDO;
use Exception;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

class FinalDataService {
    /**
     * @var PDO Database connection
     */
    private $db;

    /**
     * @var CustomRuleService
     */
    private $customRuleService;

    /**
     * @var SimplifiedSocketService
     */
    private $socketService;

    /**
     * @var CpuModelRepository
     */
    private $cpuModelRepository;

    /**
     * Constructor
     *
     * @param PDO $db Database connection (can be null, will create a new connection)
     */
    public function __construct($db = null) {
        if ($db === null) {
            // Create a new database connection
            $this->db = \App\Utils\DatabaseUtils::getConnection();
        } else {
            $this->db = $db;
        }
        $this->customRuleService = new \App\Services\CustomRuleService($this->db);
        $this->socketService = new \App\Services\SimplifiedSocketService($this->db);
        $this->cpuModelRepository = new \App\Repositories\CpuModelRepository($this->db);
    }

    /**
     * Find the CPU data JSON file
     *
     * @return array Result with success status, message, and file path
     */
    private function findCpuDataJsonFile() {
        // Check if the JSON file exists
        $jsonFilePath = __DIR__ . '/../../data/cpu_models.json';
        error_log("Looking for CPU models JSON file at: {$jsonFilePath}");

        if (!file_exists($jsonFilePath)) {
            error_log("CPU models JSON file not found: {$jsonFilePath}");

            // Try alternative locations
            $altPaths = [
                __DIR__ . '/../../data/cpus.json',
                __DIR__ . '/../../data/cpu_data.json',
                __DIR__ . '/../../data/generated/cpu_data.json'
            ];

            foreach ($altPaths as $altPath) {
                error_log("Trying alternative path: {$altPath}");
                if (file_exists($altPath)) {
                    $jsonFilePath = $altPath;
                    error_log("Found CPU data at alternative path: {$jsonFilePath}");
                    break;
                }
            }

            if (!file_exists($jsonFilePath)) {
                error_log("No CPU data file found in any location");
                return [
                    'success' => false,
                    'message' => 'No CPU data file found',
                    'file' => null
                ];
            }
        }

        return [
            'success' => true,
            'message' => 'CPU data file found',
            'file' => $jsonFilePath
        ];
    }

    /**
     * Load CPU data from JSON file
     *
     * @param string $jsonFilePath Path to the JSON file
     * @return array Result with success status, message, and CPU data
     */
    private function loadCpuDataFromJsonFile($jsonFilePath) {
        try {
            // Read the JSON file
            error_log("Reading JSON file: {$jsonFilePath}");
            $jsonData = file_get_contents($jsonFilePath);

            if (empty($jsonData)) {
                error_log("Empty JSON file: {$jsonFilePath}");
                return [
                    'success' => false,
                    'message' => 'Empty JSON file',
                    'data' => null
                ];
            }

            error_log("JSON file size: " . strlen($jsonData) . " bytes");

            // Decode the JSON data
            $cpuData = json_decode($jsonData, true);

            // Check if the data is valid
            if ($cpuData === null) {
                error_log("JSON decode error: " . json_last_error_msg());
                return [
                    'success' => false,
                    'message' => 'Invalid JSON data: ' . json_last_error_msg(),
                    'data' => null
                ];
            }

            if (!is_array($cpuData)) {
                error_log("Invalid CPU models JSON data (not an array)");
                return [
                    'success' => false,
                    'message' => 'Invalid CPU models JSON data (not an array)',
                    'data' => null
                ];
            }

            error_log("JSON data structure: " . print_r(array_keys($cpuData), true));

            // Check if the data is in the expected format
            $cpuModels = [];
            if (isset($cpuData['data']) && is_array($cpuData['data'])) {
                // Format: { "data": [ ... ] }
                $cpuModels = $cpuData['data'];
                error_log("Found CPU models in 'data' key, count: " . count($cpuModels));
            } else if (is_array($cpuData) && !empty($cpuData) && !isset($cpuData['data'])) {
                // Format: [ ... ]
                $cpuModels = $cpuData;
                error_log("Found CPU models in root array, count: " . count($cpuModels));
            }

            if (empty($cpuModels)) {
                error_log("Empty CPU models JSON data");
                return [
                    'success' => false,
                    'message' => 'Empty CPU models JSON data',
                    'data' => null
                ];
            }

            // Log the first model to check structure
            if (!empty($cpuModels)) {
                error_log("First CPU model structure: " . print_r($cpuModels[0], true));
            }

            return [
                'success' => true,
                'message' => 'CPU data loaded successfully',
                'data' => $cpuModels,
                'count' => count($cpuModels)
            ];
        } catch (Exception $e) {
            error_log("Error loading CPU data from JSON file: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error loading CPU data: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Generate the whitelisted CPU data
     *
     * @param array $cpuData Raw CPU data
     * @return array Processed CPU data
     */
    public function generateFinalData($cpuData) {
        error_log("Starting generateFinalData with " . (is_array($cpuData) ? count($cpuData) : 'non-array') . " CPU models");

        // Step 1: Apply custom rules
        error_log("Step 1: Applying custom rules");
        $cpuData = $this->customRuleService->applyCpuRules($cpuData);

        // Step 2: Process categories and set whitelist flag
        error_log("Step 2: Processing categories and setting whitelist flag");
        $cpuData = $this->processCategoriesAndWhitelist($cpuData);

        // Step 3: Match against socket variants
        error_log("Step 3: Matching against socket variants");
        $cpuData = $this->matchSocketVariants($cpuData);

        // Step 4: Format the data for output
        error_log("Step 4: Formatting data for output");
        $finalData = $this->formatFinalData($cpuData);

        // Count whitelisted CPUs
        $whitelistedCount = 0;
        foreach ($finalData as $cpu) {
            if ($cpu['whitelist']) {
                $whitelistedCount++;
            }
        }
        error_log("Final data contains " . count($finalData) . " CPUs, " . $whitelistedCount . " whitelisted");

        return $finalData;
    }

    /**
     * Process categories and set whitelist flag
     *
     * @param array $cpuData CPU data
     * @return array Processed CPU data
     */
    private function processCategoriesAndWhitelist($cpuData) {
        // Check if the data has the expected structure with "data" key
        if (isset($cpuData['data']) && is_array($cpuData['data'])) {
            // This is the correct format with a "data" key containing an array of CPU models
            $cpuModels = $cpuData['data'];

            // Process each CPU model
            foreach ($cpuModels as $index => $model) {
                $cpuModels[$index] = $this->processCpuCategory($model);
            }

            // Update the data array
            $cpuData['data'] = $cpuModels;
        } else if (is_array($cpuData)) {
            // This is a direct array of CPU models without the "data" key
            foreach ($cpuData as $index => $model) {
                $cpuData[$index] = $this->processCpuCategory($model);
            }
        }

        return $cpuData;
    }

    /**
     * Process a single CPU model's category and set whitelist flag
     *
     * @param array $model CPU model data
     * @return array Processed CPU model data
     */
    private function processCpuCategory($model) {
        // Default category is 'other'
        $category = 'other';
        $whitelist = false;

        // Get the category from the model
        $originalCategory = isset($model['cat']) ? $model['cat'] : '';

        // Process category based on partial matches
        if (stripos($originalCategory, 'Desktop') !== false) {
            $category = 'desktop';
            $whitelist = true;
        } else if (stripos($originalCategory, 'Server') !== false) {
            $category = 'server';
            $whitelist = true;
        } else if (stripos($originalCategory, 'Laptop') !== false) {
            $category = 'laptop';
        } else if (stripos($originalCategory, 'Mobile') !== false) {
            $category = 'mobile';
        }

        // Update the model with the processed category and whitelist flag
        $model['category'] = $category;
        $model['whitelist'] = $whitelist;

        return $model;
    }

    /**
     * Match CPU models against socket variants
     *
     * @param array $cpuData CPU data
     * @return array Processed CPU data
     */
    private function matchSocketVariants($cpuData) {
        // Get all sockets with their variants
        $socketsResult = $this->socketService->getAllSockets();

        if (!$socketsResult['success']) {
            // If there was an error getting the sockets, return the original data
            error_log("Error getting sockets: " . ($socketsResult['message'] ?? 'Unknown error'));
            return $cpuData;
        }

        $sockets = $socketsResult['data'];
        error_log("Retrieved " . count($sockets) . " sockets from database");

        // Create a map of socket variants to their parent socket
        $socketVariantsMap = [];
        foreach ($sockets as $socket) {
            // Add the main socket name (lowercase for case-insensitive matching)
            $socketVariantsMap[strtolower($socket['name'])] = [
                'name' => $socket['name'],
                'slug' => $socket['slug'],
                'whitelist' => ($socket['type'] === 'desktop' || $socket['type'] === 'server')
            ];

            error_log("Added socket: {$socket['name']}, Type: {$socket['type']}, Whitelist: " .
                     (($socket['type'] === 'desktop' || $socket['type'] === 'server') ? 'true' : 'false'));

            // Add all variants (lowercase for case-insensitive matching)
            if (isset($socket['variants']) && is_array($socket['variants'])) {
                foreach ($socket['variants'] as $variant) {
                    $socketVariantsMap[strtolower($variant)] = [
                        'name' => $socket['name'],
                        'slug' => $socket['slug'],
                        'whitelist' => ($socket['type'] === 'desktop' || $socket['type'] === 'server')
                    ];
                    error_log("  Added variant: {$variant} for socket {$socket['name']}");
                }
            }
        }

        // Check if the data has the expected structure with "data" key
        if (isset($cpuData['data']) && is_array($cpuData['data'])) {
            // This is the correct format with a "data" key containing an array of CPU models
            $cpuModels = $cpuData['data'];

            // Process each CPU model
            foreach ($cpuModels as $index => $model) {
                $cpuModels[$index] = $this->matchCpuSocket($model, $socketVariantsMap);
            }

            // Update the data array
            $cpuData['data'] = $cpuModels;
        } else if (is_array($cpuData)) {
            // This is a direct array of CPU models without the "data" key
            foreach ($cpuData as $index => $model) {
                $cpuData[$index] = $this->matchCpuSocket($model, $socketVariantsMap);
            }
        }

        return $cpuData;
    }

    /**
     * Match a single CPU model against socket variants
     *
     * @param array $model CPU model data
     * @param array $socketVariantsMap Socket variants map
     * @return array Processed CPU model data
     */
    private function matchCpuSocket($model, $socketVariantsMap) {
        // Get the socket from the model and convert to lowercase for case-insensitive matching
        $socket = isset($model['socket']) ? strtolower(trim($model['socket'])) : '';
        $originalSocket = isset($model['socket']) ? $model['socket'] : '';
        $cpuName = isset($model['name']) ? $model['name'] : 'Unknown';

        // Log the CPU being processed
        error_log("Processing CPU: {$cpuName}, Socket: {$originalSocket}");

        // Default whitelist to false for desktop/server CPUs
        if (isset($model['category']) && ($model['category'] === 'desktop' || $model['category'] === 'server')) {
            // Start with whitelist = false, will set to true only if socket matches
            $model['whitelist'] = false;
            error_log("  CPU is desktop/server, setting initial whitelist to false");
        }

        // Check if the socket exists in the variants map
        if (!empty($socket) && isset($socketVariantsMap[$socket])) {
            // Update the model with the matched socket data
            $model['socket'] = $socketVariantsMap[$socket]['name'];
            $model['socket_slug'] = $socketVariantsMap[$socket]['slug'];

            error_log("  Socket matched to: {$socketVariantsMap[$socket]['name']}");

            // Set whitelist to true if this is a desktop/server CPU and the socket is whitelisted
            if ($socketVariantsMap[$socket]['whitelist'] &&
                isset($model['category']) &&
                ($model['category'] === 'desktop' || $model['category'] === 'server')) {
                $model['whitelist'] = true;
                error_log("  Setting whitelist to true for {$cpuName}");
            }
        } else {
            // Socket not found in the variants map
            $model['socket_slug'] = '';
            error_log("  Socket not matched for {$cpuName}");

            // For desktop/server CPUs, whitelist is already set to false above
        }

        return $model;
    }

    /**
     * Format the final data for output
     *
     * @param array $cpuData Processed CPU data
     * @return array Formatted CPU data
     */
    private function formatFinalData($cpuData) {
        $finalData = [];

        // Get the CPU models from the data
        $cpuModels = isset($cpuData['data']) ? $cpuData['data'] : $cpuData;

        foreach ($cpuModels as $cpu) {
            // Calculate total cores (P cores + E cores)
            $p_cores = isset($cpu['cores']) ? intval($cpu['cores']) : 0;
            $e_cores = isset($cpu['secondaryCores']) ? intval($cpu['secondaryCores']) : 0;
            $total_cores = $p_cores + $e_cores;

            // Create a core display string in the format "XP+YE" if both P and E cores are present
            $core_display = $p_cores;
            if ($e_cores > 0) {
                $core_display = $p_cores . 'P+' . $e_cores . 'E';
            }

            // Format the CPU data
            $finalData[] = [
                'id'            => $cpu['id'] ?? '',
                'name'          => $cpu['name'] ?? '',
                'mark'          => isset($cpu['cpumark']) && $cpu['cpumark'] ? intval(str_replace(',', '', $cpu['cpumark'])) : null,
                'speed'         => isset($cpu['speed']) && $cpu['speed'] ? intval($cpu['speed']) : null,
                'cpu_count'     => isset($cpu['cpuCount']) && $cpu['cpuCount'] ? intval($cpu['cpuCount']) : null,
                'cores'         => $total_cores,
                'p_cores'       => $p_cores,
                'e_cores'       => $e_cores,
                'core_display'  => $core_display,
                'tdp'           => isset($cpu['tdp']) && $cpu['tdp'] ? intval($cpu['tdp']) : null,
                'socket'        => $cpu['socket'] ?? '',
                'socket_slug'   => $cpu['socket_slug'] ?? '',
                'category'      => $cpu['category'] ?? '',
                'whitelist'     => $cpu['whitelist'] ?? false,
                'rule_applied'  => $cpu['rule_applied'] ?? false
            ];
        }

        return $finalData;
    }

    /**
     * Save the whitelisted data to the database
     *
     * @param array $finalData Whitelisted CPU data
     * @return array Result with success status and message
     */
    public function saveFinalData($finalData) {
        try {
            // Get existing CPU models to preserve CPU details
            $existingModels = [];
            $existingCpus = $this->cpuModelRepository->getAll();
            foreach ($existingCpus as $cpu) {
                if (!empty($cpu['original_id'])) {
                    $existingModels[$cpu['original_id']] = $cpu;
                }
            }

            // Track which original_ids we've processed
            $processedIds = [];

            // Update existing records and insert new ones
            $insertedCount = 0;
            $updatedCount = 0;
            $deletedCount = 0;

            // First, check for CPUs marked for deletion by custom rules
            // This is done by checking the _delete flag in the CustomRuleService::applyCpuRules method
            // We need to find these CPUs in the database and delete them

            // Get all active delete rules
            $deleteRules = $this->customRuleService->getActiveRulesByActionType('delete');

            if (!empty($deleteRules)) {
                error_log("Found " . count($deleteRules) . " active delete rules");

                foreach ($deleteRules as $rule) {
                    $namePattern = $rule['name_pattern'] ?? '';
                    $matchType = $rule['match_type'] ?? 'name';
                    $ruleCpuCount = isset($rule['cpu_count']) ? (int)$rule['cpu_count'] : null;
                    $ruleOriginalId = $rule['original_id'] ?? null;

                    error_log("Processing delete rule: " . json_encode($rule));

                    // Find matching CPUs in the database based on the rule's match type
                    $cpusToDelete = [];

                    switch ($matchType) {
                        case 'name':
                            // Match by name only (case-insensitive)
                            $cpusToDelete = $this->cpuModelRepository->getByName($namePattern);
                            error_log("Found " . count($cpusToDelete) . " CPUs matching name: " . $namePattern);
                            break;

                        case 'name_cpu_count':
                            // Match by name and CPU count
                            $cpusToDelete = $this->cpuModelRepository->getByNameAndCpuCount($namePattern, $ruleCpuCount);
                            error_log("Found " . count($cpusToDelete) . " CPUs matching name: " . $namePattern . " and CPU count: " . $ruleCpuCount);
                            break;

                        case 'original_id_cpu_count':
                            // Match by original ID and CPU count
                            $cpu = $this->cpuModelRepository->getByOriginalIdAndCpuCount($ruleOriginalId, $ruleCpuCount);
                            if ($cpu) {
                                $cpusToDelete = [$cpu];
                                error_log("Found CPU matching original_id: " . $ruleOriginalId . " and CPU count: " . $ruleCpuCount);
                            }
                            break;
                    }

                    // Delete the matching CPUs
                    foreach ($cpusToDelete as $cpu) {
                        error_log("Deleting CPU: " . json_encode($cpu));
                        $result = $this->cpuModelRepository->delete($cpu['id']);
                        if ($result) {
                            $deletedCount++;
                            error_log("Deleted CPU with ID: " . $cpu['id']);
                        }
                    }
                }
            }

            foreach ($finalData as $cpu) {
                $originalId = $cpu['id'] ?? '';
                if (empty($originalId)) {
                    error_log("Skipping CPU with empty original_id: " . json_encode($cpu));
                    continue;
                }

                // Mark this ID as processed
                $processedIds[] = $originalId;

                // Prepare data for database
                $data = [
                    'original_id' => $originalId,
                    'name' => $cpu['name'] ?? '',
                    'mark' => $cpu['mark'] ?? null,
                    'speed' => $cpu['speed'] ?? null,
                    'cpu_count' => $cpu['cpu_count'] ?? null,
                    'cores' => $cpu['cores'] ?? null,
                    'p_cores' => $cpu['p_cores'] ?? null,
                    'e_cores' => $cpu['e_cores'] ?? null,
                    'core_display' => $cpu['core_display'] ?? '',
                    'tdp' => $cpu['tdp'] ?? null,
                    'socket' => $cpu['socket'] ?? '',
                    'socket_slug' => $cpu['socket_slug'] ?? '',
                    'category' => $cpu['category'] ?? '',
                    'whitelist' => $cpu['whitelist'] ? 1 : 0,
                    'rule_applied' => $cpu['rule_applied'] ? 1 : 0
                ];

                // Check if this CPU already exists in the database
                if (isset($existingModels[$originalId])) {
                    // Get all CPUs with this original_id
                    $cpusWithSameId = $this->cpuModelRepository->getAllByOriginalId($originalId);

                    // If there's only one CPU with this original_id, update it directly
                    if (count($cpusWithSameId) === 1) {
                        $existingCpu = $existingModels[$originalId];

                        // Preserve CPU details from existing record
                        $data['brand_tag'] = $existingCpu['brand_tag'] ?? '';
                        $data['series_tag'] = $existingCpu['series_tag'] ?? '';
                        $data['core_model_tag'] = $existingCpu['core_model_tag'] ?? '';

                        error_log("Updating CPU {$originalId} (single record) and preserving details: " .
                                  "brand_tag={$data['brand_tag']}, " .
                                  "series_tag={$data['series_tag']}, " .
                                  "core_model_tag={$data['core_model_tag']}");

                        // Update the existing record
                        $result = $this->cpuModelRepository->updateByOriginalId($originalId, $data);
                        if ($result) {
                            $updatedCount++;
                        }
                    } else {
                        // Multiple CPUs with the same original_id, try to match by cpu_count
                        $cpuCount = $data['cpu_count'] ?? null;

                        error_log("Found multiple CPUs with original_id {$originalId}, trying to match by cpu_count {$cpuCount}");

                        // Try to find a match by both original_id and cpu_count
                        $matchedCpu = $this->cpuModelRepository->getByOriginalIdAndCpuCount($originalId, $cpuCount);

                        if ($matchedCpu) {
                            // Found a match by both original_id and cpu_count
                            // Preserve CPU details from the matched record
                            $data['brand_tag'] = $matchedCpu['brand_tag'] ?? '';
                            $data['series_tag'] = $matchedCpu['series_tag'] ?? '';
                            $data['core_model_tag'] = $matchedCpu['core_model_tag'] ?? '';

                            error_log("Updating CPU {$originalId} with cpu_count {$cpuCount} and preserving details: " .
                                      "brand_tag={$data['brand_tag']}, " .
                                      "series_tag={$data['series_tag']}, " .
                                      "core_model_tag={$data['core_model_tag']}");

                            // Update the matched record
                            $result = $this->cpuModelRepository->updateByOriginalIdAndCpuCount($originalId, $cpuCount, $data);
                            if ($result) {
                                $updatedCount++;
                            }
                        } else {
                            // No match found by both original_id and cpu_count, create a new record
                            error_log("No match found for CPU {$originalId} with cpu_count {$cpuCount}, creating new record");

                            // This is a new CPU, use empty values for CPU details or values from JSON if available
                            $data['brand_tag'] = $cpu['brand_tag'] ?? '';
                            $data['series_tag'] = $cpu['series_tag'] ?? '';
                            $data['core_model_tag'] = $cpu['core_model_tag'] ?? '';

                            // Insert a new record
                            $result = $this->cpuModelRepository->create($data);
                            if ($result) {
                                $insertedCount++;
                            }
                        }
                    }
                } else {
                    // This is a new CPU, use empty values for CPU details or values from JSON if available
                    $data['brand_tag'] = $cpu['brand_tag'] ?? '';
                    $data['series_tag'] = $cpu['series_tag'] ?? '';
                    $data['core_model_tag'] = $cpu['core_model_tag'] ?? '';

                    error_log("Inserting new CPU {$originalId}");

                    // Insert a new record
                    $result = $this->cpuModelRepository->create($data);
                    if ($result) {
                        $insertedCount++;
                    }
                }
            }

            return [
                'success' => true,
                'message' => 'Whitelisted data saved successfully to database.',
                'count' => $insertedCount + $updatedCount,
                'inserted' => $insertedCount,
                'updated' => $updatedCount,
                'deleted' => $deletedCount
            ];
        } catch (Exception $e) {
            error_log("Error saving whitelisted data: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error saving whitelisted data: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate and save the whitelisted CPU data
     *
     * @return array Result with success status and message
     */
    public function generateAndSaveFinalData() {
        try {
            // Find the CPU data JSON file
            $fileResult = $this->findCpuDataJsonFile();

            if (!$fileResult['success']) {
                error_log("No CPU data file found");

                // No CPU data file found, save an empty array
                $result = $this->saveFinalData([]);
                $result['message'] = 'No CPU data found. Please import CPU data first.';
                return $result;
            }

            // Load the CPU data from the JSON file
            $jsonFilePath = $fileResult['file'];
            $loadResult = $this->loadCpuDataFromJsonFile($jsonFilePath);

            if (!$loadResult['success']) {
                error_log("Error loading CPU data: " . $loadResult['message']);

                // Error loading CPU data, save an empty array
                $result = $this->saveFinalData([]);
                $result['message'] = 'Error loading CPU data: ' . $loadResult['message'];
                return $result;
            }

            // Get the CPU data
            $cpuData = $loadResult['data'];

            if (empty($cpuData)) {
                error_log("No CPU models found in the JSON file");

                // No CPU models found, save an empty array
                $result = $this->saveFinalData([]);
                $result['message'] = 'No CPU data found. Please import CPU data first.';
                return $result;
            }

            error_log("Generating whitelisted data for " . count($cpuData) . " CPU models");

            // Generate the whitelisted data
            $finalData = $this->generateFinalData($cpuData);

            error_log("Generated whitelisted data for " . count($finalData) . " CPU models");

            // Save the final data
            $result = $this->saveFinalData($finalData);

            return $result;
        } catch (Exception $e) {
            error_log("Error generating whitelisted data: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error generating whitelisted data: ' . $e->getMessage()
            ];
        }
    }



    /**
     * Import CPU data from a JSON file
     *
     * @param string $jsonFile Name of the JSON file (default: cpu_models.json)
     * @return array Result with success status and message
     */
    public function importCpuDataFromJson($jsonFile = 'cpu_models.json') {
        try {
            // Check if the file exists
            $jsonFilePath = __DIR__ . '/../../data/' . $jsonFile;
            error_log("Looking for CPU models JSON file at: {$jsonFilePath}");

            if (!file_exists($jsonFilePath)) {
                error_log("CPU models JSON file not found: {$jsonFilePath}");

                if (!file_exists($jsonFilePath)) {
                    return [
                        'success' => false,
                        'message' => 'CPU models JSON file not found'
                    ];
                }
            }

            // Load the CPU data from the JSON file
            $loadResult = $this->loadCpuDataFromJsonFile($jsonFilePath);

            if (!$loadResult['success']) {
                error_log("Error loading CPU data: " . $loadResult['message']);
                return [
                    'success' => false,
                    'message' => 'Error loading CPU data: ' . $loadResult['message']
                ];
            }

            // Get the CPU data
            $cpuModels = $loadResult['data'];

            if (empty($cpuModels)) {
                error_log("Empty CPU models JSON data");
                return [
                    'success' => false,
                    'message' => 'Empty CPU models JSON data'
                ];
            }

            // Generate the final data
            $finalData = $this->generateFinalData($cpuModels);

            // Save the final data to database and JSON file
            $result = $this->saveFinalData($finalData);

            // Add additional information to the result
            $result['count'] = count($cpuModels);
            $result['message'] = "Successfully imported and processed {$result['count']} CPU models";

            return $result;
        } catch (Exception $e) {
            error_log("Error importing CPU models from JSON file: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error importing CPU models: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get the whitelisted data from the database
     *
     * @return array Whitelisted CPU data
     */
    public function getFinalData() {
        try {
            // Get CPU models from the database
            $cpuModels = $this->cpuModelRepository->getAll();

            // If there's no data, return an empty array
            if (empty($cpuModels)) {
                return [
                    'success' => true,
                    'data' => [],
                    'count' => 0,
                    'message' => 'No CPU data found. Please import CPU data first.',
                    'stats' => [
                        'whitelisted' => 0,
                        'ignored' => 0,
                        'rules_applied' => 0
                    ]
                ];
            }

            // Get statistics
            $stats = $this->cpuModelRepository->getStats();

            // Format the data for output
            $finalData = [];
            foreach ($cpuModels as $cpu) {
                $finalData[] = [
                    'id' => $cpu['original_id'], // Use original_id instead of database id
                    'original_id' => $cpu['original_id'],
                    'name' => $cpu['name'],
                    'mark' => $cpu['mark'],
                    'speed' => $cpu['speed'],
                    'cpu_count' => $cpu['cpu_count'],
                    'cores' => $cpu['cores'],
                    'p_cores' => $cpu['p_cores'],
                    'e_cores' => $cpu['e_cores'],
                    'core_display' => $cpu['core_display'],
                    'tdp' => $cpu['tdp'],
                    'socket' => $cpu['socket'],
                    'socket_slug' => $cpu['socket_slug'],
                    'category' => $cpu['category'],
                    'whitelist' => (bool)$cpu['whitelist'],
                    'rule_applied' => (bool)$cpu['rule_applied'],
                    'brand_tag' => $cpu['brand_tag'],
                    'series_tag' => $cpu['series_tag'],
                    'core_model_tag' => $cpu['core_model_tag']
                ];
            }

            return [
                'success' => true,
                'data' => $finalData,
                'count' => count($finalData),
                'stats' => $stats
            ];
        } catch (Exception $e) {
            error_log("Error getting whitelisted data: " . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Error getting whitelisted data: ' . $e->getMessage(),
                'stats' => [
                    'whitelisted' => 0,
                    'ignored' => 0,
                    'rules_applied' => 0
                ]
            ];
        }
    }
}
