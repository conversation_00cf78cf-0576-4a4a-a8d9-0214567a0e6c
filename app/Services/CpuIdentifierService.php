<?php
/**
 * CPU Identifier Service
 * Handles operations related to CPU identifiers
 */

namespace App\Services;

use App\Repositories\CpuIdentifierRepository;
use App\Repositories\CpuModelRepository;
use PDO;
use Exception;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Class for managing CPU identifiers
 */
class CpuIdentifierService {
    /**
     * @var PDO Database connection
     */
    private $db;

    /**
     * @var CpuIdentifierRepository
     */
    private $cpuIdentifierRepository;

    /**
     * @var CpuModelRepository
     */
    private $cpuModelRepository;

    /**
     * Constructor
     */
    public function __construct() {
        // Create a database connection
        $this->db = \App\Utils\DatabaseUtils::getConnection();
        $this->cpuIdentifierRepository = new \App\Repositories\CpuIdentifierRepository($this->db);
        $this->cpuModelRepository = new \App\Repositories\CpuModelRepository($this->db);

        // Ensure the cpu_identifiers table exists
        $this->cpuIdentifierRepository->createTableIfNotExists();
    }

    /**
     * Trigger regeneration of related data after identifiers change.
     * Currently, this regenerates Amazon search terms.
     */
    private function regenerateDependentData()
    {
        try {
            // Ensure AmazonSearchTermService is available or autoloaded
            if (!class_exists('App\Services\AmazonSearchTermService')) {
                $servicePath = __DIR__ . '/AmazonSearchTermService.php'; // Assumes it's in the same directory
                if (file_exists($servicePath)) {
                    require_once $servicePath;
                } else {
                    error_log("AmazonSearchTermService class not found and file does not exist at: " . $servicePath);
                    return;
                }
            }
            $amazonSearchTermService = new AmazonSearchTermService($this->db);
            $result = $amazonSearchTermService->generateAndStoreSearchTerms();
            if ($result['success']) {
                error_log("Successfully regenerated Amazon search terms after CPU identifier change. Count: " . $result['count']);
            } else {
                error_log("Failed to regenerate Amazon search terms after CPU identifier change: " . $result['message']);
            }
        } catch (\Exception $e) {
            error_log("Exception during dependent data regeneration in CpuIdentifierService: " . $e->getMessage());
        }
    }

    /**
     * Get identifiers for a CPU model
     *
     * @param int $cpuId CPU model ID
     * @return array Result with success status, message, and data
     */
    public function getIdentifiers($cpuId) {
        try {
            // Check if CPU exists
            $cpu = $this->cpuModelRepository->getById($cpuId);
            if (!$cpu) {
                return [
                    'success' => false,
                    'message' => 'CPU model not found with ID: ' . $cpuId
                ];
            }

            // Get identifiers
            $identifiers = $this->cpuIdentifierRepository->getFormattedIdentifiersByCpuId($cpuId);

            return [
                'success' => true,
                'data' => $identifiers,
                'cpu' => $cpu
            ];
        } catch (Exception $e) {
            error_log("Error getting CPU identifiers: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error getting CPU identifiers: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate identifiers for a CPU model
     *
     * @param int $cpuId CPU model ID
     * @return array Result with success status and message
     */
    public function generateIdentifiers($cpuId) {
        try {
            // Check if CPU exists
            $cpu = $this->cpuModelRepository->getById($cpuId);
            if (!$cpu) {
                return [
                    'success' => false,
                    'message' => 'CPU model not found with ID: ' . $cpuId
                ];
            }

            // Generate identifiers
            $identifiers = $this->extractCandidatesAdvanced($cpu);

            // Save identifiers
            $result = $this->cpuIdentifierRepository->saveIdentifiers($cpuId, $identifiers);

            if ($result) {
                $this->regenerateDependentData(); // Regenerate after generating identifiers for a CPU
                return [
                    'success' => true,
                    'message' => 'CPU identifiers generated successfully',
                    'data' => $identifiers
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to save CPU identifiers'
                ];
            }
        } catch (Exception $e) {
            error_log("Error generating CPU identifiers: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error generating CPU identifiers: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate identifiers for all CPU models
     *
     * @return array Result with success status, message, and count
     */
    public function generateAllIdentifiers() {
        try {
            // Get all CPU models
            $cpus = $this->cpuModelRepository->getAll();

            $successCount = 0;
            $failCount = 0;

            foreach ($cpus as $cpu) {
                // Generate identifiers
                $identifiers = $this->extractCandidatesAdvanced($cpu);

                // Save identifiers
                $result = $this->cpuIdentifierRepository->saveIdentifiers($cpu['id'], $identifiers);

                if ($result) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            if ($successCount > 0) {
                $this->regenerateDependentData(); // Regenerate after generating all identifiers
            }

            return [
                'success' => true,
                'message' => "Generated identifiers for $successCount CPU models. Failed for $failCount CPU models.",
                'count' => $successCount,
                'total' => count($cpus),
                'failed' => $failCount
            ];
        } catch (Exception $e) {
            error_log("Error generating all CPU identifiers: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error generating all CPU identifiers: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Minimally normalizes a CPU name or identifier string.
     *
     * @param string $identifier The identifier to normalize
     * @return string The normalized identifier
     */
    private function normalizeCpuIdentifierMinimal($identifier) {
        $normalized = strtolower($identifier);
        $normalized = preg_replace('/@ [0-9.]+ghz/', '', $normalized); // Remove @ frequency
        $normalized = preg_replace('/ \([^\)]+\)/', '', $normalized); // Remove content in parentheses
        $normalized = preg_replace('/\bwith radeon graphics\b/', '', $normalized); // Remove "with radeon graphics"

        // List of words/patterns to remove
        $removePatterns = [
            '/\bghz\b/',
            '/\bmhz\b/',
            '/\bapu\b/',
            '/\bprocessor\b/',
            '/\bdesktop\b/',
            '/\bserver\b/',
            '/\bmobile\b/',
            '/\blaptop\b/',
            '/\bquad-core\b/',
            '/\bsix-core\b/',
            '/\beight-core\b/',
            '/\bdual-core\b/',
            '/\bdual core\b/',
            '/\bquad core\b/',
            '/\bsix core\b/',
            '/\beight core\b/',
            '/\bboxed\b/',
            '/\bblack edition\b/',
            '/\bedition\b/'
        ];
        $normalized = preg_replace($removePatterns, '', $normalized);

        // Standardize spacing and trim
        $normalized = trim(preg_replace('/\s+/', ' ', $normalized));
        return $normalized;
    }

    /**
     * Extracts candidate identifiers (simple and compound) from a CPU definition.
     *
     * @param array $cpu Expected to have 'brand_tag', 'series_tag' (optional), 'core_model_tag'.
     * @return array ['simple' => [...], 'compound_and' => [[...],[...]]]
     */
    private function extractCandidatesAdvanced($cpu) {
        $simpleCandidatesSet = [];
        $compoundCandidates = [];
        $potentialIdentifiers = []; // Still used for basic simple rules

        // Read tags - use null coalescing operator for safety
        $brand = isset($cpu['brand_tag']) ? strtolower(trim($cpu['brand_tag'])) : null;
        $series = isset($cpu['series_tag']) ? strtolower(trim($cpu['series_tag'])) : null;
        $coreModel = isset($cpu['core_model_tag']) ? strtolower(trim($cpu['core_model_tag'])) : null;
        $coreModelSpaced = null;
        $coreModelNoPrefix = null;

        // Basic validation
        if (empty($brand) || empty($coreModel)) {
            // Fallback: only use normalized name and identifiers if tags are missing/invalid
            if (isset($cpu['name'])) $potentialIdentifiers[] = $cpu['name'];

            foreach ($potentialIdentifiers as $identifier) {
                $minimalNormalized = $this->normalizeCpuIdentifierMinimal($identifier);
                if (!empty($minimalNormalized)) $simpleCandidatesSet[$minimalNormalized] = true;
            }
            return ['simple' => array_keys($simpleCandidatesSet), 'compound_and' => []];
        }

        // --- Generate Simple Rules ---

        // 1. From Name (Normalized)
        if (isset($cpu['name'])) $potentialIdentifiers[] = $cpu['name'];

        foreach ($potentialIdentifiers as $identifier) {
            $minimalNormalized = $this->normalizeCpuIdentifierMinimal($identifier);
            if (empty($minimalNormalized)) continue;
            $simpleCandidatesSet[$minimalNormalized] = true; // Add original structure (cleaned)

            // Generate and add space-separated version if hyphens exist
            if (strpos($minimalNormalized, '-') !== false) {
                $spacedVersion = trim(preg_replace('/\s+/', ' ', str_replace('-', ' ', $minimalNormalized)));
                if ($spacedVersion !== $minimalNormalized) {
                    $simpleCandidatesSet[$spacedVersion] = true;
                }
            }
        }

        // 2. Process core model variations
        // Handle models with prefixes (like X5660, G3440)
        if (preg_match('/^([a-z])\d+/i', $coreModel, $matches)) {
            // Store the model without the prefix (e.g., 5660 from X5660)
            $coreModelNoPrefix = substr($coreModel, 1);
            // Ensure it's stored as a string, not an integer
            $simpleCandidatesSet[(string)$coreModelNoPrefix] = true;
        }

        // Handle models with hyphens (like i7-4790K)
        if (strpos($coreModel, '-') !== false) {
            $coreModelSpaced = trim(preg_replace('/\s+/', ' ', str_replace('-', ' ', $coreModel)));
            if ($coreModelSpaced !== $coreModel) {
                $simpleCandidatesSet[$coreModelSpaced] = true;
            } else {
                $coreModelSpaced = null; // Reset if not different
            }

            // For Intel Core models, also add the numeric part alone (e.g., 4790K from i7-4790K)
            if (strpos($cpu['name'], 'Intel Core') !== false && preg_match('/-([\d]+[a-z]*)$/i', $coreModel, $matches)) {
                $numericPart = $matches[1];
                // Ensure it's stored as a string, not an integer
                $simpleCandidatesSet[(string)$numericPart] = true;
            }
        }

        // 3. Combine components (handle cases where series might be missing)
        $brandSeries = $brand;
        if (!empty($series)) {
            $brandSeries .= ' ' . $series;
            $simpleCandidatesSet[trim("$series $coreModel")] = true; // Series + CoreModel

            // For AMD Ryzen, add series number + model (e.g., "5 5600x")
            if (strpos($series, 'ryzen') !== false && preg_match('/ryzen\s+(\d+)/i', $series, $matches)) {
                $seriesNum = $matches[1];
                // Ensure it's stored as a string, not an integer
                $simpleCandidatesSet[trim((string)$seriesNum . " $coreModel")] = true;
            }

            // For AMD A-series with suffixes (like A8-7600B)
            if (strpos($brand, 'amd') !== false &&
                strpos($series, 'a') === 0 &&
                preg_match('/a(\d+)/i', $series, $matches)) {

                // Check if the core model has a suffix (like 7600B)
                if (preg_match('/(\d+)([a-z]+)$/i', $coreModel, $modelMatches)) {
                    $baseModel = $modelMatches[1]; // e.g., 7600
                    $suffix = $modelMatches[2];    // e.g., B

                    // Add variations with and without suffix
                    $simpleCandidatesSet["a{$matches[1]}-{$baseModel}{$suffix}"] = true; // e.g., a8-7600b
                    $simpleCandidatesSet["a{$matches[1]} {$baseModel}{$suffix}"] = true;  // e.g., a8 7600b

                    // For PRO variants
                    if (strpos(strtolower($cpu['name']), 'pro') !== false) {
                        $simpleCandidatesSet["a{$matches[1]} pro-{$baseModel}{$suffix}"] = true; // e.g., a8 pro-7600b
                        $simpleCandidatesSet["a{$matches[1]} pro {$baseModel}{$suffix}"] = true;  // e.g., a8 pro 7600b
                        $simpleCandidatesSet["a{$matches[1]}-pro-{$baseModel}{$suffix}"] = true; // e.g., a8-pro-7600b
                    }
                }
            }
        }
        $simpleCandidatesSet[trim("$brandSeries $coreModel")] = true; // Brand + Series + CoreModel

        // 4. CoreModel itself
        $simpleCandidatesSet[$coreModel] = true;

        // --- Generate Compound Rules ---
        $seenCompound = [];

        // [Brand, CoreModel]
        $rule1 = [$brand, $coreModel];
        $key1 = $brand . '|' . $coreModel;
        if (!isset($seenCompound[$key1])) {
            $compoundCandidates[] = $rule1;
            $seenCompound[$key1] = true;
        }

        // Add variations with spaced model
        if ($coreModelSpaced) {
            $rule1s = [$brand, $coreModelSpaced];
            $key1s = $brand . '|' . $coreModelSpaced;
            if (!isset($seenCompound[$key1s])) {
                $compoundCandidates[] = $rule1s;
                $seenCompound[$key1s] = true;
            }
        }

        // Add variations with model without prefix
        if ($coreModelNoPrefix) {
            $rule1np = [$brand, (string)$coreModelNoPrefix];
            $key1np = $brand . '|' . $coreModelNoPrefix;
            if (!isset($seenCompound[$key1np])) {
                $compoundCandidates[] = $rule1np;
                $seenCompound[$key1np] = true;
            }
        }

        // [Brand Series, CoreModel] - only if series exists
        if ($series) {
            $seriesContext = trim("$brand $series");
            $rule2 = [$seriesContext, $coreModel];
            $key2 = $seriesContext . '|' . $coreModel;
            if (!isset($seenCompound[$key2])) {
                $compoundCandidates[] = $rule2;
                $seenCompound[$key2] = true;
            }

            // Add variations with spaced model
            if ($coreModelSpaced) {
                $rule2s = [$seriesContext, $coreModelSpaced];
                $key2s = $seriesContext . '|' . $coreModelSpaced;
                if (!isset($seenCompound[$key2s])) {
                    $compoundCandidates[] = $rule2s;
                    $seenCompound[$key2s] = true;
                }
            }

            // Add variations with model without prefix
            if ($coreModelNoPrefix) {
                $rule2np = [$seriesContext, (string)$coreModelNoPrefix];
                $key2np = $seriesContext . '|' . $coreModelNoPrefix;
                if (!isset($seenCompound[$key2np])) {
                    $compoundCandidates[] = $rule2np;
                    $seenCompound[$key2np] = true;
                }
            }

            // For AMD Ryzen, add [Ryzen Series Number, CoreModel]
            if (strpos($series, 'ryzen') !== false && preg_match('/ryzen\s+(\d+)/i', $series, $matches)) {
                $ryzenSeries = 'ryzen ' . $matches[1];
                $rule3 = [$ryzenSeries, $coreModel];
                $key3 = $ryzenSeries . '|' . $coreModel;
                if (!isset($seenCompound[$key3])) {
                    $compoundCandidates[] = $rule3;
                    $seenCompound[$key3] = true;
                }
            }
        }

        $finalSimple = array_keys($simpleCandidatesSet);

        // Clean up simple rules: remove rules that are just the brand, series, or frequency
        $finalSimple = array_filter($finalSimple, function ($rule) use ($brand, $series, $brandSeries) {
            $lowerRule = strtolower($rule);

            // Filter out brand and series
            if ($brand && $lowerRule === $brand) return false;
            if ($series && $lowerRule === $series) return false;
            if ($brandSeries && $lowerRule === $brandSeries) return false;

            // Filter out frequency-only rules
            if (preg_match('/^\d+(\.\d+)?\s*ghz$/', $lowerRule)) return false;
            if (preg_match('/^\d+(\.\d+)?\s*mhz$/', $lowerRule)) return false;

            // Filter out very short rules (less than 4 characters) unless they contain a model pattern
            if (strlen($lowerRule) < 4) {
                // Keep short rules that have a model pattern (letter+number or number+letter)
                if (!preg_match('/[a-z]\d+/i', $lowerRule) && !preg_match('/\d+[a-z]/i', $lowerRule)) {
                    return false;
                }
            }

            return true;
        });

        // Ensure all simple rules are strings
        $finalSimple = array_map(function($rule) {
            return (string)$rule;
        }, array_values($finalSimple));

        return ['simple' => $finalSimple, 'compound_and' => $compoundCandidates];
    }
}
