<?php
/**
 * CPU Details Service
 * Handles operations related to extracting CPU details like brand, series, and core model tags
 */

namespace App\Services;

use App\Repositories\CpuModelRepository;
use Exception;
use PDO;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Class for extracting CPU details
 */
class CpuDetailsService {
    /**
     * Path to the CPU models JSON file
     * @var string
     */
    private $cpuModelsFile;

    /**
     * Path to the generated CPU data JSON file
     * @var string
     */
    private $generatedDataFile;

    /**
     * @var PDO Database connection
     */
    private $db;

    /**
     * @var CpuModelRepository
     */
    private $cpuModelRepository;

    /**
     * Constructor
     */
    public function __construct() {
        $this->cpuModelsFile = __DIR__ . '/../../data/cpu_models.json';
        $this->generatedDataFile = __DIR__ . '/../../data/generated/cpu_data.json';

        // Create the generated directory if it doesn't exist
        $generatedDir = dirname($this->generatedDataFile);
        if (!is_dir($generatedDir)) {
            if (!mkdir($generatedDir, 0755, true)) {
                throw new Exception("Failed to create directory: $generatedDir");
            }
        }

        // Create a database connection
        $this->db = \App\Utils\DatabaseUtils::getConnection();
        $this->cpuModelRepository = new \App\Repositories\CpuModelRepository($this->db);
    }

    /**
     * Update CPU details manually
     *
     * @param int $id CPU model ID
     * @param string $brandTag Brand tag
     * @param string $seriesTag Series tag
     * @param string $coreModelTag Core model tag
     * @return array Result with success status and message
     */
    public function updateCpuDetails($id, $brandTag, $seriesTag, $coreModelTag) {
        try {
            // Trim whitespace from all tag values
            $brandTag = trim($brandTag);
            $seriesTag = trim($seriesTag);
            $coreModelTag = trim($coreModelTag);
            // Try to get the CPU model by original_id first
            error_log("CpuDetailsService::updateCpuDetails - Looking for CPU with original_id: {$id}");
            $cpu = $this->cpuModelRepository->getByOriginalId($id);

            // If not found by original_id, try by database id as fallback
            if (!$cpu) {
                error_log("CpuDetailsService::updateCpuDetails - CPU not found by original_id, trying database id");
                $cpu = $this->cpuModelRepository->getById($id);
            }

            if ($cpu) {
                error_log("CpuDetailsService::updateCpuDetails - Found CPU: " . json_encode([
                    'id' => $cpu['id'],
                    'original_id' => $cpu['original_id'],
                    'name' => $cpu['name']
                ]));
            }

            if (!$cpu) {
                return [
                    'success' => false,
                    'message' => 'CPU model not found with ID: ' . $id
                ];
            }

            // Update the CPU model with the new details
            $data = [
                'original_id' => $cpu['original_id'],
                'name' => $cpu['name'],
                'mark' => $cpu['mark'],
                'speed' => $cpu['speed'],
                'cpu_count' => $cpu['cpu_count'],
                'cores' => $cpu['cores'],
                'p_cores' => $cpu['p_cores'],
                'e_cores' => $cpu['e_cores'],
                'core_display' => $cpu['core_display'],
                'tdp' => $cpu['tdp'],
                'socket' => $cpu['socket'],
                'socket_slug' => $cpu['socket_slug'],
                'category' => $cpu['category'],
                'whitelist' => $cpu['whitelist'],
                'rule_applied' => $cpu['rule_applied'],
                'brand_tag' => $brandTag,
                'series_tag' => $seriesTag,
                'core_model_tag' => $coreModelTag
            ];

            // Use the database ID (not the original_id) for the update
            error_log("CpuDetailsService::updateCpuDetails - Updating CPU with database ID: {$cpu['id']}");
            $result = $this->cpuModelRepository->update($cpu['id'], $data);
            error_log("CpuDetailsService::updateCpuDetails - Update result: " . ($result ? 'Success' : 'Failed'));

            if (!$result) {
                return [
                    'success' => false,
                    'message' => 'Failed to update CPU details in the database'
                ];
            }

            return [
                'success' => true,
                'message' => 'CPU details updated successfully',
                'data' => [
                    'id' => $cpu['id'], // Use database id for unique identification
                    'original_id' => $cpu['original_id'],
                    'name' => $cpu['name'],
                    'brand_tag' => $brandTag,
                    'series_tag' => $seriesTag,
                    'core_model_tag' => $coreModelTag
                ]
            ];
        } catch (Exception $e) {
            error_log("Error updating CPU details: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error updating CPU details: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate CPU details (brand, series, core model tags)
     *
     * This method is kept for backward compatibility but is no longer used.
     * CPU details are now edited manually through the admin UI.
     *
     * @return array Result with success status and message
     */
    public function generateCpuDetails() {
        return [
            'success' => false,
            'message' => 'Automatic CPU details generation has been disabled. Please edit CPU details manually through the admin UI.'
        ];
    }

    /**
     * Get CPU details for display
     *
     * @return array CPU details data
     */
    public function getCpuDetails() {
        try {
            // Get CPU models with details from the database
            $cpuModels = $this->cpuModelRepository->getWithDetails();

            if (empty($cpuModels)) {
                return [
                    'success' => false,
                    'message' => 'No CPU models found in the database. Please import CPU data first.',
                    'data' => []
                ];
            }

            // Prepare data for display
            $displayData = [];
            foreach ($cpuModels as $cpu) {
                // Only include entries that have at least a name
                if (!empty($cpu['name'])) {
                    $displayData[] = [
                        'id' => $cpu['id'], // Use database id for unique identification
                        'original_id' => $cpu['original_id'],
                        'name' => $cpu['name'],
                        'brand_tag' => $cpu['brand_tag'] ?? '',
                        'series_tag' => $cpu['series_tag'] ?? '',
                        'core_model_tag' => $cpu['core_model_tag'] ?? '',
                        'cpu_count' => $cpu['cpu_count'] ?? 1,
                        'mark' => $cpu['mark'] ?? 0
                    ];
                }
            }

            return [
                'success' => true,
                'data' => $displayData,
                'count' => count($displayData)
            ];
        } catch (Exception $e) {
            error_log("Error getting CPU details: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());

            return [
                'success' => false,
                'message' => 'Error getting CPU details: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

} // End class CpuDetailsService