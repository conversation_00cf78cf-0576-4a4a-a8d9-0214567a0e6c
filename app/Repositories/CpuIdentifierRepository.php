<?php
/**
 * CPU Identifier Repository
 *
 * Handles database operations for CPU identifiers
 */

namespace App\Repositories;

use PDO;
use PDOException;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

class CpuIdentifierRepository {
    /**
     * Database connection
     *
     * @var PDO
     */
    private $db;

    /**
     * Constructor
     *
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * Create the cpu_identifiers table if it doesn't exist
     *
     * @return bool True if successful, false otherwise
     */
    public function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS cpu_identifiers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cpu_id INTEGER NOT NULL,
            identifier_type VARCHAR(20) NOT NULL,
            identifier_value TEXT NOT NULL,
            compound_part INTEGER DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cpu_id) REFERENCES cpu_models(id) ON DELETE CASCADE
        )";

        try {
            error_log('CpuIdentifierRepository::createTableIfNotExists - Executing SQL: ' . $sql);
            $this->db->exec($sql);

            // Create indexes
            $this->db->exec("CREATE INDEX IF NOT EXISTS idx_cpu_identifiers_cpu_id ON cpu_identifiers (cpu_id)");
            $this->db->exec("CREATE INDEX IF NOT EXISTS idx_cpu_identifiers_type ON cpu_identifiers (identifier_type)");
            $this->db->exec("CREATE INDEX IF NOT EXISTS idx_cpu_identifiers_value ON cpu_identifiers (identifier_value)");

            error_log('CpuIdentifierRepository::createTableIfNotExists - Table created successfully');
            return true;
        } catch (PDOException $e) {
            error_log('CpuIdentifierRepository::createTableIfNotExists - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all identifiers for a CPU model
     *
     * @param int $cpuId CPU model ID
     * @return array Array of identifiers
     */
    public function getIdentifiersByCpuId($cpuId) {
        $sql = "SELECT * FROM cpu_identifiers WHERE cpu_id = :cpu_id ORDER BY identifier_type, id";

        try {
            error_log('CpuIdentifierRepository::getIdentifiersByCpuId - Executing SQL: ' . $sql . ' with CPU ID: ' . $cpuId);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':cpu_id', $cpuId, PDO::PARAM_INT);
            $stmt->execute();

            $identifiers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('CpuIdentifierRepository::getIdentifiersByCpuId - Retrieved ' . count($identifiers) . ' identifiers');
            return $identifiers;
        } catch (PDOException $e) {
            error_log('CpuIdentifierRepository::getIdentifiersByCpuId - Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get formatted identifiers for a CPU model
     *
     * @param int $cpuId CPU model ID
     * @return array Array with 'simple' and 'compound_and' keys
     */
    public function getFormattedIdentifiersByCpuId($cpuId) {
        $identifiers = $this->getIdentifiersByCpuId($cpuId);

        $result = [
            'simple' => [],
            'compound_and' => []
        ];

        $compoundMap = [];

        foreach ($identifiers as $identifier) {
            if ($identifier['identifier_type'] === 'simple') {
                $result['simple'][] = $identifier['identifier_value'];
            } else if ($identifier['identifier_type'] === 'compound_and') {
                $compoundId = $identifier['compound_part'];
                if (!isset($compoundMap[$compoundId])) {
                    $compoundMap[$compoundId] = [];
                }
                $compoundMap[$compoundId][] = $identifier['identifier_value'];
            }
        }

        // Convert compound map to array of arrays
        foreach ($compoundMap as $parts) {
            if (count($parts) === 2) {
                $result['compound_and'][] = $parts;
            }
        }

        return $result;
    }

    /**
     * Save identifiers for a CPU model
     *
     * @param int $cpuId CPU model ID
     * @param array $identifiers Array with 'simple' and 'compound_and' keys
     * @return bool True if successful, false otherwise
     */
    public function saveIdentifiers($cpuId, $identifiers) {
        try {
            // Begin transaction
            $this->db->beginTransaction();

            // Delete existing identifiers for this CPU
            $deleteSql = "DELETE FROM cpu_identifiers WHERE cpu_id = :cpu_id";
            $deleteStmt = $this->db->prepare($deleteSql);
            $deleteStmt->bindParam(':cpu_id', $cpuId, PDO::PARAM_INT);
            $deleteStmt->execute();

            // Insert simple identifiers
            if (isset($identifiers['simple']) && is_array($identifiers['simple'])) {
                $insertSimpleSql = "INSERT INTO cpu_identifiers (cpu_id, identifier_type, identifier_value)
                                   VALUES (:cpu_id, 'simple', :value)";
                $insertSimpleStmt = $this->db->prepare($insertSimpleSql);

                foreach ($identifiers['simple'] as $value) {
                    $insertSimpleStmt->bindParam(':cpu_id', $cpuId, PDO::PARAM_INT);
                    $insertSimpleStmt->bindParam(':value', $value, PDO::PARAM_STR);
                    $insertSimpleStmt->execute();
                }
            }

            // Insert compound identifiers
            if (isset($identifiers['compound_and']) && is_array($identifiers['compound_and'])) {
                $insertCompoundSql = "INSERT INTO cpu_identifiers (cpu_id, identifier_type, identifier_value, compound_part)
                                     VALUES (:cpu_id, 'compound_and', :value, :part)";
                $insertCompoundStmt = $this->db->prepare($insertCompoundSql);

                foreach ($identifiers['compound_and'] as $index => $parts) {
                    if (is_array($parts) && count($parts) === 2) {
                        for ($i = 0; $i < 2; $i++) {
                            $insertCompoundStmt->bindParam(':cpu_id', $cpuId, PDO::PARAM_INT);
                            $insertCompoundStmt->bindParam(':value', $parts[$i], PDO::PARAM_STR);
                            $insertCompoundStmt->bindParam(':part', $index, PDO::PARAM_INT);
                            $insertCompoundStmt->execute();
                        }
                    }
                }
            }

            // Commit transaction
            $this->db->commit();
            return true;
        } catch (PDOException $e) {
            // Rollback transaction on error
            $this->db->rollBack();
            error_log('CpuIdentifierRepository::saveIdentifiers - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete all identifiers for a CPU model
     *
     * @param int $cpuId CPU model ID
     * @return bool True if successful, false otherwise
     */
    public function deleteIdentifiersByCpuId($cpuId) {
        $sql = "DELETE FROM cpu_identifiers WHERE cpu_id = :cpu_id";

        try {
            error_log('CpuIdentifierRepository::deleteIdentifiersByCpuId - Executing SQL: ' . $sql . ' with CPU ID: ' . $cpuId);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':cpu_id', $cpuId, PDO::PARAM_INT);
            $result = $stmt->execute();
            error_log('CpuIdentifierRepository::deleteIdentifiersByCpuId - Result: ' . ($result ? 'Success' : 'Failed'));
            return $result;
        } catch (PDOException $e) {
            error_log('CpuIdentifierRepository::deleteIdentifiersByCpuId - Error: ' . $e->getMessage());
            return false;
        }
    }
}
