<?php
/**
 * Socket Repository
 * Handles database operations for sockets and socket variants
 */

namespace App\Repositories;

use App\Utils\DatabaseUtils;
use PDO;
use PDOException;
use Exception;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Repository for socket data operations
 */
class SocketRepository
{
    /**
     * @var PDO Database connection
     */
    private $db;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->db = DatabaseUtils::getConnection();
    }

    /**
     * Get all sockets with their variants
     *
     * @return array Array of sockets with their variants
     * @throws Exception If an error occurs
     */
    public function getAllSockets()
    {
        try {
            // First, get all sockets
            $sql = "
                SELECT
                    id, name, slug, year, manufacturer, type, created_at, updated_at
                FROM
                    sockets
                ORDER BY
                    year DESC, manufacturer, name
            ";

            $stmt = $this->db->query($sql);
            $sockets = $stmt->fetchAll();

            // Then get all variants for each socket
            if (!empty($sockets)) {
                $socketIds = array_column($sockets, 'id');

                // Initialize variants array for each socket
                foreach ($sockets as &$socket) {
                    $socket['variants'] = [];
                }
                unset($socket); // Unset reference to avoid issues

                // Get all variants in a single query
                $variantsSql = "
                    SELECT
                        socket_id, id, variant_name
                    FROM
                        socket_variants
                    WHERE
                        socket_id IN (" . implode(',', $socketIds) . ")
                    ORDER BY
                        socket_id, id
                ";

                $variantsStmt = $this->db->query($variantsSql);
                $variants = $variantsStmt->fetchAll();

                // Group variants by socket_id
                $variantsBySocket = [];
                foreach ($variants as $variant) {
                    $socketId = $variant['socket_id'];
                    if (!isset($variantsBySocket[$socketId])) {
                        $variantsBySocket[$socketId] = [];
                    }
                    $variantsBySocket[$socketId][] = [
                        'id' => $variant['id'],
                        'name' => $variant['variant_name']
                    ];
                }

                // Add variants to their respective sockets
                foreach ($sockets as &$socket) {
                    $socketId = $socket['id'];
                    if (isset($variantsBySocket[$socketId])) {
                        $socket['variants'] = $variantsBySocket[$socketId];
                    }
                }
                unset($socket); // Unset reference
            }

            return $sockets;
        } catch (Exception $e) {
            throw new Exception('Error retrieving sockets: ' . $e->getMessage());
        }
    }

    /**
     * Get a single socket by ID with its variants
     *
     * @param int $socketId Socket ID
     * @return array Socket data with variants
     * @throws Exception If socket not found or an error occurs
     */
    public function getSocketById($socketId)
    {
        try {
            // Get socket data
            $sql = "SELECT id, name, slug, year, manufacturer, type, created_at, updated_at FROM sockets WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
            $stmt->execute();

            $socket = $stmt->fetch();

            if (!$socket) {
                throw new Exception('Socket not found');
            }

            // Get socket variants
            $sql = "SELECT id, variant_name FROM socket_variants WHERE socket_id = :socket_id ORDER BY id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
            $stmt->execute();

            $socket['variants'] = $stmt->fetchAll();

            return $socket;
        } catch (Exception $e) {
            throw new Exception('Error retrieving socket: ' . $e->getMessage());
        }
    }

    /**
     * Add a new socket with variants
     *
     * @param array $socketData Socket data
     * @return int ID of the newly created socket
     * @throws Exception If validation fails or an error occurs
     */
    public function addSocket($socketData)
    {
        try {
            // Validate required fields
            $requiredFields = ['name', 'slug', 'year', 'manufacturer', 'type'];
            foreach ($requiredFields as $field) {
                if (empty($socketData[$field])) {
                    throw new Exception("Field '{$field}' is required");
                }
            }

            // Validate manufacturer
            if (!in_array($socketData['manufacturer'], ['Intel', 'AMD'])) {
                throw new Exception("Manufacturer must be 'Intel' or 'AMD'");
            }

            // Validate type
            if (!in_array($socketData['type'], ['desktop', 'server'])) {
                throw new Exception("Type must be 'desktop' or 'server'");
            }

            // Begin transaction
            $this->db->beginTransaction();

            // Insert socket
            $sql = "
                INSERT INTO sockets (name, slug, year, manufacturer, type)
                VALUES (:name, :slug, :year, :manufacturer, :type)
            ";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':name', $socketData['name']);
            $stmt->bindParam(':slug', $socketData['slug']);
            $stmt->bindParam(':year', $socketData['year'], PDO::PARAM_INT);
            $stmt->bindParam(':manufacturer', $socketData['manufacturer']);
            $stmt->bindParam(':type', $socketData['type']);

            $stmt->execute();

            $socketId = $this->db->lastInsertId();

            // Insert variants if provided
            if (!empty($socketData['variants']) && is_array($socketData['variants'])) {
                $sql = "INSERT INTO socket_variants (socket_id, variant_name) VALUES (:socket_id, :variant_name)";
                $stmt = $this->db->prepare($sql);

                foreach ($socketData['variants'] as $variant) {
                    if (!empty($variant)) {
                        $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
                        $stmt->bindParam(':variant_name', $variant);
                        $stmt->execute();
                    }
                }
            }

            // Commit transaction
            $this->db->commit();

            return $socketId;
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }

            // Check for UNIQUE constraint violation
            if ($e->getCode() == 23000 && (strpos($e->getMessage(), 'UNIQUE constraint failed: sockets.name') !== false ||
                                          strpos($e->getMessage(), 'UNIQUE constraint failed: sockets.slug') !== false)) {
                throw new Exception('A socket with this name or slug already exists');
            }

            throw new Exception('Error adding socket: ' . $e->getMessage());
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }

            throw $e;
        }
    }

    /**
     * Update an existing socket and its variants
     *
     * @param int $socketId Socket ID
     * @param array $socketData Socket data
     * @return bool True on success
     * @throws Exception If validation fails or an error occurs
     */
    public function updateSocket($socketId, $socketData)
    {
        try {
            // Validate required fields
            $requiredFields = ['name', 'slug', 'year', 'manufacturer', 'type'];
            foreach ($requiredFields as $field) {
                if (empty($socketData[$field])) {
                    throw new Exception("Field '{$field}' is required");
                }
            }

            // Validate manufacturer
            if (!in_array($socketData['manufacturer'], ['Intel', 'AMD'])) {
                throw new Exception("Manufacturer must be 'Intel' or 'AMD'");
            }

            // Validate type
            if (!in_array($socketData['type'], ['desktop', 'server'])) {
                throw new Exception("Type must be 'desktop' or 'server'");
            }

            // Check if socket exists
            $sql = "SELECT id FROM sockets WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
            $stmt->execute();

            if (!$stmt->fetch()) {
                throw new Exception('Socket not found');
            }

            // Begin transaction
            $this->db->beginTransaction();

            // Update socket
            $sql = "
                UPDATE sockets
                SET name = :name, slug = :slug, year = :year, manufacturer = :manufacturer, type = :type, updated_at = CURRENT_TIMESTAMP
                WHERE id = :id
            ";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':name', $socketData['name']);
            $stmt->bindParam(':slug', $socketData['slug']);
            $stmt->bindParam(':year', $socketData['year'], PDO::PARAM_INT);
            $stmt->bindParam(':manufacturer', $socketData['manufacturer']);
            $stmt->bindParam(':type', $socketData['type']);
            $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);

            $stmt->execute();

            // Handle variants
            if (isset($socketData['variants'])) {
                // Delete existing variants
                $sql = "DELETE FROM socket_variants WHERE socket_id = :socket_id";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
                $stmt->execute();

                // Insert new variants
                if (!empty($socketData['variants']) && is_array($socketData['variants'])) {
                    $sql = "INSERT INTO socket_variants (socket_id, variant_name) VALUES (:socket_id, :variant_name)";
                    $stmt = $this->db->prepare($sql);

                    foreach ($socketData['variants'] as $variant) {
                        if (!empty($variant)) {
                            $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
                            $stmt->bindParam(':variant_name', $variant);
                            $stmt->execute();
                        }
                    }
                }
            }

            // Commit transaction
            $this->db->commit();

            return true;
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }

            // Check for UNIQUE constraint violation
            if ($e->getCode() == 23000 && (strpos($e->getMessage(), 'UNIQUE constraint failed: sockets.name') !== false ||
                                          strpos($e->getMessage(), 'UNIQUE constraint failed: sockets.slug') !== false)) {
                throw new Exception('A socket with this name or slug already exists');
            }

            throw new Exception('Error updating socket: ' . $e->getMessage());
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }

            throw $e;
        }
    }

    /**
     * Delete a socket and its variants
     *
     * @param int $socketId Socket ID
     * @return bool True on success
     * @throws Exception If socket not found or an error occurs
     */
    public function deleteSocket($socketId)
    {
        try {
            // Check if socket exists
            $sql = "SELECT id FROM sockets WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
            $stmt->execute();

            if (!$stmt->fetch()) {
                throw new Exception('Socket not found');
            }

            // Begin transaction
            $this->db->beginTransaction();

            // Delete socket (variants will be deleted automatically due to ON DELETE CASCADE)
            $sql = "DELETE FROM sockets WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
            $stmt->execute();

            // Commit transaction
            $this->db->commit();

            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }

            throw new Exception('Error deleting socket: ' . $e->getMessage());
        }
    }

    /**
     * Add a new socket variant
     *
     * @param int $socketId Socket ID
     * @param string $variantName Variant name
     * @return int ID of the newly created variant
     * @throws Exception If socket not found, variant already exists, or an error occurs
     */
    public function addSocketVariant($socketId, $variantName)
    {
        try {
            // Check if socket exists
            $sql = "SELECT id FROM sockets WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
            $stmt->execute();

            if (!$stmt->fetch()) {
                throw new Exception('Socket not found');
            }

            // Check if variant already exists
            $sql = "SELECT id FROM socket_variants WHERE socket_id = :socket_id AND variant_name = :variant_name";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
            $stmt->bindParam(':variant_name', $variantName);
            $stmt->execute();

            if ($stmt->fetch()) {
                throw new Exception('Variant already exists for this socket');
            }

            // Insert variant
            $sql = "INSERT INTO socket_variants (socket_id, variant_name) VALUES (:socket_id, :variant_name)";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
            $stmt->bindParam(':variant_name', $variantName);
            $stmt->execute();

            return $this->db->lastInsertId();
        } catch (Exception $e) {
            throw new Exception('Error adding variant: ' . $e->getMessage());
        }
    }

    /**
     * Update a socket variant
     *
     * @param int $variantId Variant ID
     * @param string $variantName New variant name
     * @return bool True on success
     * @throws Exception If variant not found, name already exists, or an error occurs
     */
    public function updateSocketVariant($variantId, $variantName)
    {
        try {
            // Check if variant exists
            $sql = "SELECT id, socket_id FROM socket_variants WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
            $stmt->execute();

            $variant = $stmt->fetch();

            if (!$variant) {
                throw new Exception('Variant not found');
            }

            // Check if variant name already exists for this socket
            $sql = "SELECT id FROM socket_variants WHERE socket_id = :socket_id AND variant_name = :variant_name AND id != :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':socket_id', $variant['socket_id'], PDO::PARAM_INT);
            $stmt->bindParam(':variant_name', $variantName);
            $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
            $stmt->execute();

            if ($stmt->fetch()) {
                throw new Exception('Variant name already exists for this socket');
            }

            // Update variant
            $sql = "UPDATE socket_variants SET variant_name = :variant_name WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':variant_name', $variantName);
            $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
            $stmt->execute();

            return true;
        } catch (Exception $e) {
            throw new Exception('Error updating variant: ' . $e->getMessage());
        }
    }

    /**
     * Delete a socket variant
     *
     * @param int $variantId Variant ID
     * @return bool True on success
     * @throws Exception If variant not found or an error occurs
     */
    public function deleteSocketVariant($variantId)
    {
        try {
            // Check if variant exists
            $sql = "SELECT id FROM socket_variants WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
            $stmt->execute();

            if (!$stmt->fetch()) {
                throw new Exception('Variant not found');
            }

            // Delete variant
            $sql = "DELETE FROM socket_variants WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
            $stmt->execute();

            return true;
        } catch (Exception $e) {
            throw new Exception('Error deleting variant: ' . $e->getMessage());
        }
    }

    /**
     * Get all socket variants with their socket information
     *
     * @return array Array of socket variants with socket information
     * @throws Exception If an error occurs
     */
    public function getAllSocketVariants()
    {
        try {
            $sql = "
                SELECT
                    sv.id, sv.variant_name,
                    s.id as socket_id, s.name as socket_name, s.manufacturer
                FROM
                    socket_variants sv
                JOIN
                    sockets s ON sv.socket_id = s.id
                WHERE
                    s.type IN ('desktop', 'server')
                ORDER BY
                    s.manufacturer, sv.variant_name
            ";

            $stmt = $this->db->query($sql);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            throw new Exception('Error retrieving socket variants: ' . $e->getMessage());
        }
    }
}
