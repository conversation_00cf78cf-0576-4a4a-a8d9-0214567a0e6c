<?php
/**
 * CPU Model Repository
 *
 * Handles database operations for CPU models
 */

namespace App\Repositories;

use PD<PERSON>;
use PDOException;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

class CpuModelRepository {
    /**
     * Database connection
     *
     * @var PDO
     */
    private $db;

    /**
     * Constructor
     *
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * Create the cpu_models table if it doesn't exist
     *
     * @return bool True if successful, false otherwise
     */
    public function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS cpu_models (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            original_id TEXT,
            name TEXT NOT NULL,
            mark INTEGER,
            speed INTEGER,
            cpu_count INTEGER,
            cores INTEGER,
            p_cores INTEGER,
            e_cores INTEGER,
            core_display TEXT,
            tdp INTEGER,
            socket TEXT,
            socket_slug TEXT,
            category TEXT,
            whitelist INTEGER DEFAULT 0,
            rule_applied INTEGER DEFAULT 0,
            brand_tag TEXT,
            series_tag TEXT,
            core_model_tag TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        try {
            error_log('CpuModelRepository::createTableIfNotExists - Executing SQL: ' . $sql);
            $this->db->exec($sql);

            // Create indexes
            $this->db->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_name ON cpu_models (name)");
            $this->db->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_whitelist ON cpu_models (whitelist)");
            $this->db->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_socket ON cpu_models (socket)");
            $this->db->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_category ON cpu_models (category)");

            // Create trigger
            $this->db->exec("
                CREATE TRIGGER IF NOT EXISTS trigger_cpu_models_update_timestamp
                AFTER UPDATE ON cpu_models
                FOR EACH ROW
                BEGIN
                    UPDATE cpu_models SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
                END
            ");

            error_log('CpuModelRepository::createTableIfNotExists - Table created successfully');
            return true;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::createTableIfNotExists - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all CPU models
     *
     * @return array Array of CPU models
     */
    public function getAll() {
        $sql = "SELECT * FROM cpu_models ORDER BY name";

        try {
            error_log('CpuModelRepository::getAll - Executing SQL: ' . $sql);
            $stmt = $this->db->query($sql);
            $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('CpuModelRepository::getAll - Retrieved ' . count($models) . ' models');
            return $models;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getAll - Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get whitelisted CPU models
     *
     * @return array Array of whitelisted CPU models
     */
    public function getWhitelisted() {
        $sql = "SELECT * FROM cpu_models WHERE whitelist = 1 ORDER BY name";

        try {
            error_log('CpuModelRepository::getWhitelisted - Executing SQL: ' . $sql);
            $stmt = $this->db->query($sql);
            $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('CpuModelRepository::getWhitelisted - Retrieved ' . count($models) . ' whitelisted models');
            return $models;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getWhitelisted - Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get CPU models with details
     *
     * @return array Array of CPU models with details
     */
    public function getWithDetails() {
        $sql = "SELECT id, original_id, name, brand_tag, series_tag, core_model_tag, cpu_count, mark FROM cpu_models ORDER BY name";

        try {
            error_log('CpuModelRepository::getWithDetails - Executing SQL: ' . $sql);
            $stmt = $this->db->query($sql);
            $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('CpuModelRepository::getWithDetails - Retrieved ' . count($models) . ' models with details');
            return $models;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getWithDetails - Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get a CPU model by ID
     *
     * @param int $id CPU model ID
     * @return array|null CPU model data or null if not found
     */
    public function getById($id) {
        $sql = "SELECT * FROM cpu_models WHERE id = :id";

        try {
            error_log('CpuModelRepository::getById - Executing SQL: ' . $sql . ' with ID: ' . $id);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            error_log('CpuModelRepository::getById - Result: ' . ($result ? 'Found' : 'Not found'));
            return $result ? $result : null;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getById - Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get a CPU model by original ID
     *
     * @param string $originalId Original CPU model ID
     * @return array|null CPU model data or null if not found
     */
    public function getByOriginalId($originalId) {
        $sql = "SELECT * FROM cpu_models WHERE original_id = :original_id";

        try {
            error_log('CpuModelRepository::getByOriginalId - Executing SQL: ' . $sql . ' with Original ID: ' . $originalId);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':original_id', $originalId, PDO::PARAM_STR);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            error_log('CpuModelRepository::getByOriginalId - Result: ' . ($result ? 'Found' : 'Not found'));
            return $result ? $result : null;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getByOriginalId - Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get all CPU models with a specific original ID
     *
     * @param string $originalId Original CPU model ID
     * @return array Array of CPU models with the given original ID
     */
    public function getAllByOriginalId($originalId) {
        $sql = "SELECT * FROM cpu_models WHERE original_id = :original_id";

        try {
            error_log('CpuModelRepository::getAllByOriginalId - Executing SQL: ' . $sql . ' with Original ID: ' . $originalId);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':original_id', $originalId, PDO::PARAM_STR);
            $stmt->execute();

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('CpuModelRepository::getAllByOriginalId - Found ' . count($results) . ' records');
            return $results;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getAllByOriginalId - Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get a CPU model by original ID and CPU count
     *
     * @param string $originalId Original CPU model ID
     * @param int $cpuCount CPU count
     * @return array|null CPU model data or null if not found
     */
    public function getByOriginalIdAndCpuCount($originalId, $cpuCount) {
        $sql = "SELECT * FROM cpu_models WHERE original_id = :original_id AND cpu_count = :cpu_count";

        try {
            error_log('CpuModelRepository::getByOriginalIdAndCpuCount - Executing SQL: ' . $sql . ' with Original ID: ' . $originalId . ' and CPU Count: ' . $cpuCount);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':original_id', $originalId, PDO::PARAM_STR);
            $stmt->bindParam(':cpu_count', $cpuCount, PDO::PARAM_INT);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            error_log('CpuModelRepository::getByOriginalIdAndCpuCount - Result: ' . ($result ? 'Found' : 'Not found'));
            return $result ? $result : null;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getByOriginalIdAndCpuCount - Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create a new CPU model
     *
     * @param array $data CPU model data
     * @return int|false The ID of the new model or false on failure
     */
    public function create($data) {
        $sql = "INSERT INTO cpu_models (
                original_id, name, mark, speed, cpu_count, cores, p_cores, e_cores,
                core_display, tdp, socket, socket_slug, category, whitelist,
                rule_applied, brand_tag, series_tag, core_model_tag
            ) VALUES (
                :original_id, :name, :mark, :speed, :cpu_count, :cores, :p_cores, :e_cores,
                :core_display, :tdp, :socket, :socket_slug, :category, :whitelist,
                :rule_applied, :brand_tag, :series_tag, :core_model_tag
            )";

        try {
            error_log('CpuModelRepository::create - Executing SQL: ' . $sql);
            error_log('CpuModelRepository::create - Data: ' . json_encode($data));

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':original_id', $data['original_id'], PDO::PARAM_STR);
            $stmt->bindParam(':name', $data['name'], PDO::PARAM_STR);
            $stmt->bindParam(':mark', $data['mark'], PDO::PARAM_INT);
            $stmt->bindParam(':speed', $data['speed'], PDO::PARAM_INT);
            $stmt->bindParam(':cpu_count', $data['cpu_count'], PDO::PARAM_INT);
            $stmt->bindParam(':cores', $data['cores'], PDO::PARAM_INT);
            $stmt->bindParam(':p_cores', $data['p_cores'], PDO::PARAM_INT);
            $stmt->bindParam(':e_cores', $data['e_cores'], PDO::PARAM_INT);
            $stmt->bindParam(':core_display', $data['core_display'], PDO::PARAM_STR);
            $stmt->bindParam(':tdp', $data['tdp'], PDO::PARAM_INT);
            $stmt->bindParam(':socket', $data['socket'], PDO::PARAM_STR);
            $stmt->bindParam(':socket_slug', $data['socket_slug'], PDO::PARAM_STR);
            $stmt->bindParam(':category', $data['category'], PDO::PARAM_STR);
            $stmt->bindParam(':whitelist', $data['whitelist'], PDO::PARAM_INT);
            $stmt->bindParam(':rule_applied', $data['rule_applied'], PDO::PARAM_INT);
            $stmt->bindParam(':brand_tag', $data['brand_tag'], PDO::PARAM_STR);
            $stmt->bindParam(':series_tag', $data['series_tag'], PDO::PARAM_STR);
            $stmt->bindParam(':core_model_tag', $data['core_model_tag'], PDO::PARAM_STR);

            $stmt->execute();
            $id = $this->db->lastInsertId();
            error_log('CpuModelRepository::create - New CPU model created with ID: ' . $id);
            return $id;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::create - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a CPU model
     *
     * @param int $id CPU model ID
     * @param array $data CPU model data
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data) {
        $sql = "UPDATE cpu_models
                SET original_id = :original_id,
                    name = :name,
                    mark = :mark,
                    speed = :speed,
                    cpu_count = :cpu_count,
                    cores = :cores,
                    p_cores = :p_cores,
                    e_cores = :e_cores,
                    core_display = :core_display,
                    tdp = :tdp,
                    socket = :socket,
                    socket_slug = :socket_slug,
                    category = :category,
                    whitelist = :whitelist,
                    rule_applied = :rule_applied,
                    brand_tag = :brand_tag,
                    series_tag = :series_tag,
                    core_model_tag = :core_model_tag
                WHERE id = :id";

        try {
            error_log('CpuModelRepository::update - Executing SQL: ' . $sql);
            error_log('CpuModelRepository::update - Data: ' . json_encode($data));

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':original_id', $data['original_id'], PDO::PARAM_STR);
            $stmt->bindParam(':name', $data['name'], PDO::PARAM_STR);
            $stmt->bindParam(':mark', $data['mark'], PDO::PARAM_INT);
            $stmt->bindParam(':speed', $data['speed'], PDO::PARAM_INT);
            $stmt->bindParam(':cpu_count', $data['cpu_count'], PDO::PARAM_INT);
            $stmt->bindParam(':cores', $data['cores'], PDO::PARAM_INT);
            $stmt->bindParam(':p_cores', $data['p_cores'], PDO::PARAM_INT);
            $stmt->bindParam(':e_cores', $data['e_cores'], PDO::PARAM_INT);
            $stmt->bindParam(':core_display', $data['core_display'], PDO::PARAM_STR);
            $stmt->bindParam(':tdp', $data['tdp'], PDO::PARAM_INT);
            $stmt->bindParam(':socket', $data['socket'], PDO::PARAM_STR);
            $stmt->bindParam(':socket_slug', $data['socket_slug'], PDO::PARAM_STR);
            $stmt->bindParam(':category', $data['category'], PDO::PARAM_STR);
            $stmt->bindParam(':whitelist', $data['whitelist'], PDO::PARAM_INT);
            $stmt->bindParam(':rule_applied', $data['rule_applied'], PDO::PARAM_INT);
            $stmt->bindParam(':brand_tag', $data['brand_tag'], PDO::PARAM_STR);
            $stmt->bindParam(':series_tag', $data['series_tag'], PDO::PARAM_STR);
            $stmt->bindParam(':core_model_tag', $data['core_model_tag'], PDO::PARAM_STR);

            $result = $stmt->execute();
            error_log('CpuModelRepository::update - Result: ' . ($result ? 'Success' : 'Failed'));
            return $result;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::update - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a CPU model by original_id
     *
     * @param string $originalId Original CPU model ID
     * @param array $data CPU model data
     * @return bool True if successful, false otherwise
     */
    public function updateByOriginalId($originalId, $data) {
        // First, get the database ID for this original_id
        $existingCpu = $this->getByOriginalId($originalId);

        if (!$existingCpu) {
            error_log("CpuModelRepository::updateByOriginalId - No CPU found with original_id: {$originalId}");
            return false;
        }

        // Now update using the database ID
        return $this->update($existingCpu['id'], $data);
    }

    /**
     * Update a CPU model by original_id and cpu_count
     *
     * @param string $originalId Original CPU model ID
     * @param int $cpuCount CPU count
     * @param array $data CPU model data
     * @return bool True if successful, false otherwise
     */
    public function updateByOriginalIdAndCpuCount($originalId, $cpuCount, $data) {
        // First, get the database ID for this original_id and cpu_count
        $existingCpu = $this->getByOriginalIdAndCpuCount($originalId, $cpuCount);

        if (!$existingCpu) {
            error_log("CpuModelRepository::updateByOriginalIdAndCpuCount - No CPU found with original_id: {$originalId} and cpu_count: {$cpuCount}");
            return false;
        }

        // Now update using the database ID
        return $this->update($existingCpu['id'], $data);
    }

    /**
     * Delete a CPU model
     *
     * @param int $id CPU model ID
     * @return bool True if successful, false otherwise
     */
    public function delete($id) {
        $sql = "DELETE FROM cpu_models WHERE id = :id";

        try {
            error_log('CpuModelRepository::delete - Executing SQL: ' . $sql . ' with ID: ' . $id);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);

            $result = $stmt->execute();
            error_log('CpuModelRepository::delete - Result: ' . ($result ? 'Success' : 'Failed'));
            return $result;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::delete - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete all CPU models
     *
     * @return bool True if successful, false otherwise
     */
    public function deleteAll() {
        $sql = "DELETE FROM cpu_models";

        try {
            error_log('CpuModelRepository::deleteAll - Executing SQL: ' . $sql);
            $result = $this->db->exec($sql);
            error_log('CpuModelRepository::deleteAll - Result: ' . ($result !== false ? 'Success' : 'Failed'));
            return $result !== false;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::deleteAll - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get CPU models by name (exact match, case-insensitive)
     *
     * @param string $name CPU model name
     * @return array Array of CPU models with the given name
     */
    public function getByName($name) {
        $sql = "SELECT * FROM cpu_models WHERE LOWER(name) = LOWER(:name)";

        try {
            error_log('CpuModelRepository::getByName - Executing SQL: ' . $sql . ' with Name: ' . $name);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':name', $name, PDO::PARAM_STR);
            $stmt->execute();

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('CpuModelRepository::getByName - Found ' . count($results) . ' records');
            return $results;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getByName - Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get CPU models by name and CPU count
     *
     * @param string $name CPU model name
     * @param int $cpuCount CPU count
     * @return array Array of CPU models with the given name and CPU count
     */
    public function getByNameAndCpuCount($name, $cpuCount) {
        $sql = "SELECT * FROM cpu_models WHERE LOWER(name) = LOWER(:name) AND cpu_count = :cpu_count";

        try {
            error_log('CpuModelRepository::getByNameAndCpuCount - Executing SQL: ' . $sql . ' with Name: ' . $name . ' and CPU Count: ' . $cpuCount);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':name', $name, PDO::PARAM_STR);
            $stmt->bindParam(':cpu_count', $cpuCount, PDO::PARAM_INT);
            $stmt->execute();

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('CpuModelRepository::getByNameAndCpuCount - Found ' . count($results) . ' records');
            return $results;
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getByNameAndCpuCount - Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get statistics about CPU models
     *
     * @return array Statistics about CPU models
     */
    public function getStats() {
        try {
            // Get total count
            $stmt = $this->db->query("SELECT COUNT(*) as total FROM cpu_models");
            $total = $stmt->fetch()['total'];

            // Get whitelisted count
            $stmt = $this->db->query("SELECT COUNT(*) as whitelisted FROM cpu_models WHERE whitelist = 1");
            $whitelisted = $stmt->fetch()['whitelisted'];

            // Get rules applied count
            $stmt = $this->db->query("SELECT COUNT(*) as rules_applied FROM cpu_models WHERE rule_applied = 1");
            $rulesApplied = $stmt->fetch()['rules_applied'];

            return [
                'total' => $total,
                'whitelisted' => $whitelisted,
                'ignored' => $total - $whitelisted,
                'rules_applied' => $rulesApplied
            ];
        } catch (PDOException $e) {
            error_log('CpuModelRepository::getStats - Error: ' . $e->getMessage());
            return [
                'total' => 0,
                'whitelisted' => 0,
                'ignored' => 0,
                'rules_applied' => 0
            ];
        }
    }
}