<?php

namespace App\Repositories;

use PDO;

class AmazonSearchTermRepository
{
    private PDO $db;

    public function __construct(PDO $db)
    {
        $this->db = $db;
    }

    /**
     * Get all Amazon search terms.
     * @return array
     */
    public function getAll(): array
    {
        $stmt = $this->db->query("SELECT id, brand_tag, keywords, generated_search_term, product_count, type, active, created_at FROM amazon_search_terms ORDER BY type DESC, created_at DESC");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Add a new search term.
     * @param string|null $brandTag
     * @param string|null $keywords
     * @param string $generatedSearchTerm
     * @param int $productCount Number of products available for this search term
     * @param string $type Type of search term ('auto' or 'manual')
     * @param bool $active Whether the search term is active
     * @return int The ID of the inserted row.
     */
    public function add(?string $brandTag, ?string $keywords, string $generatedSearchTerm, int $productCount = 0, string $type = 'auto', bool $active = true): int
    {
        $sql = "INSERT INTO amazon_search_terms (brand_tag, keywords, generated_search_term, product_count, type, active)
                VALUES (:brand_tag, :keywords, :generated_search_term, :product_count, :type, :active)";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':brand_tag', $brandTag);
        $stmt->bindParam(':keywords', $keywords);
        $stmt->bindParam(':generated_search_term', $generatedSearchTerm);
        $stmt->bindParam(':product_count', $productCount, PDO::PARAM_INT);
        $stmt->bindParam(':type', $type);
        $activeInt = $active ? 1 : 0;
        $stmt->bindParam(':active', $activeInt, PDO::PARAM_INT);
        $stmt->execute();
        return (int)$this->db->lastInsertId();
    }

    /**
     * Get all auto-generated search terms with their active status.
     * @return array Array of search terms with generated_search_term as key and active status as value
     */
    public function getAutoTermsActiveStatus(): array
    {
        $stmt = $this->db->query("SELECT generated_search_term, active FROM amazon_search_terms WHERE type = 'auto'");
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $activeStatus = [];
        foreach ($results as $row) {
            $activeStatus[$row['generated_search_term']] = (bool)$row['active'];
        }

        return $activeStatus;
    }

    /**
     * Clear all auto-generated search terms from the table.
     * @return bool
     */
    public function clearAutoTerms(): bool
    {
        $stmt = $this->db->exec("DELETE FROM amazon_search_terms WHERE type = 'auto'");
        return $stmt !== false;
    }

    /**
     * Bulk insert search terms.
     * @param array $searchTerms Array of search term data arrays, each with 'brand_tag', 'keywords', 'generated_search_term', and optionally 'product_count', 'type', and 'active'.
     * @param array $preservedActiveStatus Optional array with generated_search_term as key and active status as value to preserve existing active statuses.
     * @return bool True on success, false on failure.
     */
    public function bulkInsert(array $searchTerms, array $preservedActiveStatus = []): bool
    {
        if (empty($searchTerms)) {
            return true;
        }

        $this->db->beginTransaction();
        try {
            $sql = "INSERT INTO amazon_search_terms (brand_tag, keywords, generated_search_term, product_count, type, active)
                    VALUES (:brand_tag, :keywords, :generated_search_term, :product_count, :type, :active)";
            $stmt = $this->db->prepare($sql);

            foreach ($searchTerms as $term) {
                $stmt->bindParam(':brand_tag', $term['brand_tag']);
                $stmt->bindParam(':keywords', $term['keywords']);
                $stmt->bindParam(':generated_search_term', $term['generated_search_term']);
                $productCount = $term['product_count'] ?? 0;
                $stmt->bindParam(':product_count', $productCount, PDO::PARAM_INT);
                $type = $term['type'] ?? 'auto';
                $stmt->bindParam(':type', $type);

                // Determine active status: use preserved status if available, otherwise use term's active value or default to true
                if (isset($preservedActiveStatus[$term['generated_search_term']])) {
                    $active = $preservedActiveStatus[$term['generated_search_term']] ? 1 : 0;
                } else {
                    $active = isset($term['active']) ? ($term['active'] ? 1 : 0) : 1;
                }
                $stmt->bindParam(':active', $active, PDO::PARAM_INT);
                $stmt->execute();
            }
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            error_log("Error in bulkInsert for AmazonSearchTermRepository: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update the product count for a search term.
     * @param int $id The ID of the search term
     * @param int $productCount The number of products found for this search term
     * @return bool True on success, false on failure
     */
    public function updateProductCount(int $id, int $productCount): bool
    {
        try {
            $sql = "UPDATE amazon_search_terms SET product_count = :product_count WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':product_count', $productCount, PDO::PARAM_INT);
            return $stmt->execute();
        } catch (\Exception $e) {
            error_log("Error updating product count for search term ID {$id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a search term.
     * @param int $id The ID of the search term
     * @param string|null $brandTag
     * @param string|null $keywords
     * @param string $generatedSearchTerm
     * @return bool True on success, false on failure
     */
    public function updateSearchTerm(int $id, ?string $brandTag, ?string $keywords, string $generatedSearchTerm): bool
    {
        try {
            $sql = "UPDATE amazon_search_terms
                    SET brand_tag = :brand_tag,
                        keywords = :keywords,
                        generated_search_term = :generated_search_term
                    WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':brand_tag', $brandTag);
            $stmt->bindParam(':keywords', $keywords);
            $stmt->bindParam(':generated_search_term', $generatedSearchTerm);
            return $stmt->execute();
        } catch (\Exception $e) {
            error_log("Error updating search term ID {$id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Toggle the active status of a search term.
     * @param int $id The ID of the search term
     * @return bool True on success, false on failure
     */
    public function toggleActive(int $id): bool
    {
        try {
            // First get the current active status
            $sql = "SELECT active FROM amazon_search_terms WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$result) {
                return false; // Search term not found
            }

            // Toggle the active status
            $newActive = $result['active'] ? 0 : 1;

            $sql = "UPDATE amazon_search_terms SET active = :active WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':active', $newActive, PDO::PARAM_INT);
            return $stmt->execute();
        } catch (\Exception $e) {
            error_log("Error toggling active status for search term ID {$id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a search term by ID.
     * @param int $id The ID of the search term
     * @return array|null The search term data or null if not found
     */
    public function getById(int $id): ?array
    {
        try {
            $sql = "SELECT id, brand_tag, keywords, generated_search_term, product_count, type, active, created_at
                    FROM amazon_search_terms
                    WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result ?: null;
        } catch (\Exception $e) {
            error_log("Error getting search term ID {$id}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete a search term by ID.
     * @param int $id The ID of the search term to delete
     * @return bool True on success, false on failure
     */
    public function delete(int $id): bool
    {
        try {
            $sql = "DELETE FROM amazon_search_terms WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            return $stmt->execute();
        } catch (\Exception $e) {
            error_log("Error deleting search term ID {$id}: " . $e->getMessage());
            return false;
        }
    }
}
