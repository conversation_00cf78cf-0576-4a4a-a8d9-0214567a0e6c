<?php
/**
 * Custom Rule Repository
 *
 * Handles database operations for custom CPU data rules
 */

namespace App\Repositories;

use PDO;
use <PERSON>OException;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

class CustomRuleRepository {
    /**
     * Database connection
     *
     * @var PDO
     */
    private $db;

    /**
     * Constructor
     *
     * @param PDO $db Database connection
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * Create the custom_rules table if it doesn't exist
     *
     * @return bool True if successful, false otherwise
     */
    public function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS custom_rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_pattern TEXT NOT NULL,
            field_to_change TEXT NOT NULL,
            new_value TEXT NOT NULL,
            match_type TEXT DEFAULT 'name',
            cpu_count INTEGER DEFAULT NULL,
            original_id TEXT DEFAULT NULL,
            action_type TEXT DEFAULT 'update',
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";

        try {
            error_log('CustomRuleRepository::createTableIfNotExists - Executing SQL: ' . $sql);
            $this->db->exec($sql);
            error_log('CustomRuleRepository::createTableIfNotExists - Table created successfully');
            return true;
        } catch (PDOException $e) {
            error_log('CustomRuleRepository::createTableIfNotExists - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all custom rules
     *
     * @return array Array of custom rules
     */
    public function getAll() {
        $sql = "SELECT * FROM custom_rules ORDER BY id DESC";

        try {
            error_log('CustomRuleRepository::getAll - Executing SQL: ' . $sql);
            $stmt = $this->db->query($sql);
            $rules = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('CustomRuleRepository::getAll - Retrieved ' . count($rules) . ' rules');
            return $rules;
        } catch (PDOException $e) {
            error_log('CustomRuleRepository::getAll - Error: ' . $e->getMessage());
            throw $e; // Re-throw the exception for better debugging
        }
    }

    /**
     * Get active custom rules
     *
     * @return array Array of active custom rules
     */
    public function getActive() {
        $sql = "SELECT * FROM custom_rules WHERE is_active = 1 ORDER BY id DESC";

        try {
            error_log('CustomRuleRepository::getActive - Executing SQL: ' . $sql);
            $stmt = $this->db->query($sql);
            $rules = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('CustomRuleRepository::getActive - Retrieved ' . count($rules) . ' active rules');
            return $rules;
        } catch (PDOException $e) {
            error_log('CustomRuleRepository::getActive - Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get a custom rule by ID
     *
     * @param int $id Custom rule ID
     * @return array|null Custom rule data or null if not found
     */
    public function getById($id) {
        $sql = "SELECT * FROM custom_rules WHERE id = :id";

        try {
            error_log('CustomRuleRepository::getById - Executing SQL: ' . $sql . ' with ID: ' . $id);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            error_log('CustomRuleRepository::getById - Result: ' . ($result ? 'Found' : 'Not found'));
            return $result ? $result : null;
        } catch (PDOException $e) {
            error_log('CustomRuleRepository::getById - Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create a new custom rule
     *
     * @param array $data Custom rule data
     * @return int|false The ID of the new rule or false on failure
     */
    public function create($data) {
        $sql = "INSERT INTO custom_rules (
                name_pattern, field_to_change, new_value, is_active,
                match_type, cpu_count, original_id, action_type
            ) VALUES (
                :name_pattern, :field_to_change, :new_value, :is_active,
                :match_type, :cpu_count, :original_id, :action_type
            )";

        try {
            error_log('CustomRuleRepository::create - Executing SQL: ' . $sql);
            error_log('CustomRuleRepository::create - Data: ' . json_encode($data));

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':name_pattern', $data['name_pattern'], PDO::PARAM_STR);
            $stmt->bindParam(':field_to_change', $data['field_to_change'], PDO::PARAM_STR);
            $stmt->bindParam(':new_value', $data['new_value'], PDO::PARAM_STR);
            $stmt->bindParam(':is_active', $data['is_active'], PDO::PARAM_INT);

            // New fields
            $matchType = isset($data['match_type']) ? $data['match_type'] : 'name';
            $stmt->bindParam(':match_type', $matchType, PDO::PARAM_STR);

            $cpuCount = isset($data['cpu_count']) ? $data['cpu_count'] : null;
            $stmt->bindParam(':cpu_count', $cpuCount, PDO::PARAM_INT);

            $originalId = isset($data['original_id']) ? $data['original_id'] : null;
            $stmt->bindParam(':original_id', $originalId, PDO::PARAM_STR);

            $actionType = isset($data['action_type']) ? $data['action_type'] : 'update';
            $stmt->bindParam(':action_type', $actionType, PDO::PARAM_STR);

            $stmt->execute();
            $id = $this->db->lastInsertId();
            error_log('CustomRuleRepository::create - New rule created with ID: ' . $id);
            return $id;
        } catch (PDOException $e) {
            error_log('CustomRuleRepository::create - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a custom rule
     *
     * @param int $id Custom rule ID
     * @param array $data Custom rule data
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data) {
        // Let's try a simpler approach with direct variable binding
        try {
            error_log('CustomRuleRepository::update - Starting update for ID: ' . $id);
            error_log('CustomRuleRepository::update - Data: ' . json_encode($data));

            // Prepare variables with proper types
            $namePattern = $data['name_pattern'] ?? '';
            $fieldToChange = $data['field_to_change'] ?? '';
            $newValue = $data['new_value'] ?? '';
            $isActive = isset($data['is_active']) ? (int)$data['is_active'] : 0;
            $matchType = isset($data['match_type']) ? $data['match_type'] : 'name';
            $cpuCount = isset($data['cpu_count']) ? (int)$data['cpu_count'] : null;
            $originalId = isset($data['original_id']) ? $data['original_id'] : null;
            $actionType = isset($data['action_type']) ? $data['action_type'] : 'update';

            // Log all variables
            error_log('CustomRuleRepository::update - Variables:');
            error_log('  - id: ' . $id);
            error_log('  - namePattern: ' . $namePattern);
            error_log('  - fieldToChange: ' . $fieldToChange);
            error_log('  - newValue: ' . $newValue);
            error_log('  - isActive: ' . $isActive);
            error_log('  - matchType: ' . $matchType);
            error_log('  - cpuCount: ' . ($cpuCount ?? 'null'));
            error_log('  - originalId: ' . ($originalId ?? 'null'));
            error_log('  - actionType: ' . $actionType);

            // Build SQL query
            $sql = "UPDATE custom_rules
                    SET name_pattern = :name_pattern,
                        field_to_change = :field_to_change,
                        new_value = :new_value,
                        is_active = :is_active,
                        match_type = :match_type,
                        cpu_count = :cpu_count,
                        original_id = :original_id,
                        action_type = :action_type,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id";

            error_log('CustomRuleRepository::update - SQL: ' . $sql);

            // Prepare statement
            $stmt = $this->db->prepare($sql);

            // Bind parameters
            $stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $stmt->bindValue(':name_pattern', $namePattern, PDO::PARAM_STR);
            $stmt->bindValue(':field_to_change', $fieldToChange, PDO::PARAM_STR);
            $stmt->bindValue(':new_value', $newValue, PDO::PARAM_STR);
            $stmt->bindValue(':is_active', $isActive, PDO::PARAM_INT);
            $stmt->bindValue(':match_type', $matchType, PDO::PARAM_STR);

            // Handle null values properly
            if ($cpuCount === null) {
                $stmt->bindValue(':cpu_count', null, PDO::PARAM_NULL);
            } else {
                $stmt->bindValue(':cpu_count', $cpuCount, PDO::PARAM_INT);
            }

            if ($originalId === null) {
                $stmt->bindValue(':original_id', null, PDO::PARAM_NULL);
            } else {
                $stmt->bindValue(':original_id', $originalId, PDO::PARAM_STR);
            }

            $stmt->bindValue(':action_type', $actionType, PDO::PARAM_STR);

            // Execute the query
            error_log('CustomRuleRepository::update - Executing statement');
            $result = $stmt->execute();

            // Check the result
            error_log('CustomRuleRepository::update - Result: ' . ($result ? 'Success' : 'Failed'));
            if (!$result) {
                $errorInfo = $stmt->errorInfo();
                error_log('CustomRuleRepository::update - Error info: ' . json_encode($errorInfo));
            } else {
                // Check if any rows were affected
                $rowCount = $stmt->rowCount();
                error_log('CustomRuleRepository::update - Rows affected: ' . $rowCount);
                if ($rowCount === 0) {
                    error_log('CustomRuleRepository::update - No rows were affected, but query executed successfully');

                    // Let's verify if the record exists
                    $checkSql = "SELECT COUNT(*) FROM custom_rules WHERE id = :id";
                    $checkStmt = $this->db->prepare($checkSql);
                    $checkStmt->bindValue(':id', $id, PDO::PARAM_INT);
                    $checkStmt->execute();
                    $count = $checkStmt->fetchColumn();
                    error_log('CustomRuleRepository::update - Record exists check: ' . ($count > 0 ? 'Yes' : 'No'));

                    // If the record exists but no rows were affected, it might be because the data is the same
                    // Let's consider this a success
                    if ($count > 0) {
                        error_log('CustomRuleRepository::update - Record exists but no changes were made');
                        return true;
                    }
                }
            }

            return $result;
        } catch (PDOException $e) {
            error_log('CustomRuleRepository::update - Error: ' . $e->getMessage());
            error_log('CustomRuleRepository::update - Error code: ' . $e->getCode());
            error_log('CustomRuleRepository::update - Error trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Delete a custom rule
     *
     * @param int $id Custom rule ID
     * @return bool True if successful, false otherwise
     */
    public function delete($id) {
        $sql = "DELETE FROM custom_rules WHERE id = :id";

        try {
            error_log('CustomRuleRepository::delete - Executing SQL: ' . $sql . ' with ID: ' . $id);
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);

            $result = $stmt->execute();
            error_log('CustomRuleRepository::delete - Result: ' . ($result ? 'Success' : 'Failed'));
            return $result;
        } catch (PDOException $e) {
            error_log('CustomRuleRepository::delete - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Toggle a custom rule's active status
     *
     * @param int $id Custom rule ID
     * @param bool $isActive Whether the rule should be active
     * @return bool True if successful, false otherwise
     */
    public function toggleActive($id, $isActive) {
        $sql = "UPDATE custom_rules
                SET is_active = :is_active,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = :id";

        try {
            error_log('CustomRuleRepository::toggleActive - Executing SQL: ' . $sql);
            error_log('CustomRuleRepository::toggleActive - ID: ' . $id . ', isActive: ' . ($isActive ? 'true' : 'false'));

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':is_active', $isActive, PDO::PARAM_INT);

            $result = $stmt->execute();
            error_log('CustomRuleRepository::toggleActive - Result: ' . ($result ? 'Success' : 'Failed'));
            return $result;
        } catch (PDOException $e) {
            error_log('CustomRuleRepository::toggleActive - Error: ' . $e->getMessage());
            return false;
        }
    }
}
