<?php
/**
 * Database Utilities
 * Provides database connection and utility functions
 */

namespace App\Utils;

use PDO;
use PDOException;
use Exception;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Database utilities class
 */
class DatabaseUtils
{
    /**
     * Get database connection
     *
     * @return PDO Database connection
     * @throws Exception If connection fails
     */
    public static function getConnection()
    {
        $dbPath = __DIR__ . '/../../data/database.sqlite';
        $dbDir = dirname($dbPath);

        // Create data directory if it doesn't exist
        if (!is_dir($dbDir)) {
            if (!mkdir($dbDir, 0755, true)) {
                $errorMsg = "Failed to create data directory: {$dbDir}";
                error_log($errorMsg);
                throw new Exception($errorMsg);
            }
        }

        $createTables = false;
        if (!file_exists($dbPath)) {
            $createTables = true;
            error_log("Database file not found, will create it at: {$dbPath}");
        }

        try {
            $pdo = new PDO('sqlite:' . $dbPath);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

            // Create tables if needed
            if ($createTables) {
                // Create sockets table
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS sockets (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        slug TEXT NOT NULL UNIQUE,
                        year INTEGER NOT NULL,
                        manufacturer TEXT NOT NULL,
                        type TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ");

                // Create socket_variants table
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS socket_variants (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        socket_id INTEGER NOT NULL,
                        variant_name TEXT NOT NULL,
                        FOREIGN KEY (socket_id) REFERENCES sockets(id) ON DELETE CASCADE,
                        UNIQUE(socket_id, variant_name)
                    )
                ");

                error_log("Database tables created successfully");
            }

            // Test the connection
            $pdo->query('SELECT 1');

            return $pdo;
        } catch (PDOException $e) {
            $errorMsg = "Database connection error: " . $e->getMessage();
            error_log($errorMsg);
            throw new Exception($errorMsg);
        }
    }
}
