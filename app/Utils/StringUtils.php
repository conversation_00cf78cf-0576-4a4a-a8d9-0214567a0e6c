<?php
/**
 * String Utilities
 * Provides string manipulation and processing functions
 */

namespace App\Utils;

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * String utilities class
 */
class StringUtils
{
    /**
     * Normalize text for matching
     * Removes special characters, standardizes whitespace, etc.
     *
     * @param string $text Text to normalize
     * @return string Normalized text
     */
    public static function normalizeText($text)
    {
        // Remove special characters like ® and ™
        $text = str_replace(['\u00ae', '\u2122', '\u00a9'], '', $text);
        $text = strtolower($text);

        // Standardize version notation (v2, v3, etc.)
        $text = preg_replace('/\bv(\d+)\b/i', 'v$1', $text); // Ensure 'v' is lowercase
        $text = preg_replace('/\s+v(\d+)\b/i', 'v$1', $text); // Remove space before v

        // Standardize Intel Core i-series notation with suffixes (T, K, F, etc.)
        // First, handle the case where the suffix is separated (e.g., "i7-7700 T" -> "i7-7700t")
        $text = preg_replace('/\bi(\d)-([0-9]+)\s+([a-z])\b/i', 'i$1-$2$3', $text);

        // Then handle the standard case (e.g., "i7-7700T" -> "i7-7700t")
        $text = preg_replace('/\bi(\d)\s*-\s*(\d+)([a-z]*)\b/i', 'i$1-$2$3', $text);

        $text = preg_replace('/[^a-z0-9\s\-v]/', '', $text); // Remove special characters except spaces, hyphens, and 'v'
        $text = preg_replace('/\s+/', ' ', $text); // Normalize whitespace
        $text = trim($text);
        
        return $text;
    }

    /**
     * Generate a slug from a string
     *
     * @param string $text Text to convert to slug
     * @return string Slug
     */
    public static function slugify($text)
    {
        // Convert to lowercase
        $text = strtolower($text);
        
        // Replace non-alphanumeric characters with hyphens
        $text = preg_replace('/[^a-z0-9]+/', '-', $text);
        
        // Remove leading/trailing hyphens
        $text = trim($text, '-');
        
        return $text;
    }

    /**
     * Validate that a string contains only alphanumeric characters and specific symbols
     *
     * @param string $text Text to validate
     * @param string $allowedChars Additional allowed characters
     * @return bool True if valid, false otherwise
     */
    public static function isValidAlphanumeric($text, $allowedChars = '-_')
    {
        return preg_match('/^[a-zA-Z0-9' . preg_quote($allowedChars, '/') . ']+$/', $text) === 1;
    }
}
