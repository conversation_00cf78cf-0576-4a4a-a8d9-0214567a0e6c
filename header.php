<?php
// Disable direct access to this file
if (!defined('ABSPATH')) {
    exit;
}

// Page title and common variables
$path = $_SERVER['REQUEST_URI'];
$name = 'CPU Prices';
$title = 'Comparison of CPUs available on Amazon, sorted by value';

// Determine which page we're on - handle both with and without query parameters
$is_faq = (strpos($path, '/faq.php') === 0); // Matches /faq.php and /faq.php?any=params
$is_privacy = (strpos($path, '/privacy.php') === 0); // Matches /privacy.php and /privacy.php?any=params
$is_home = ($path === '/' || $path === '/index.php' || strpos($path, '/?') === 0 || strpos($path, '/?') === 1);

// Set page title based on current page
if ($is_faq) {
    $title = 'FAQ';
} else if ($is_privacy) {
    $title = 'Privacy Policy';
}

// Get current locale for use throughout the page
$current_locale = isset($_GET['locale']) ? $_GET['locale'] : 'us';

// Only add locale parameter if it's not the default (us)
$locale_param = ($current_locale !== 'us') ? "?locale={$current_locale}" : "";

// Define common URLs with locale parameter (only if not default)
$home_url = "/{$locale_param}";
$faq_url = "/faq.php{$locale_param}";
$privacy_url = "/privacy.php{$locale_param}";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <title><?php echo $name . ' - ' . $title; ?></title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Compare CPU prices from Amazon marketplaces worldwide. Find the best value processors sorted by price per performance, filter by socket, cores, TDP, and condition. Updated every few hours." />
    <link rel="icon" type="image/png" href="/assets/favicon/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/assets/favicon/favicon.svg" />
    <link rel="shortcut icon" href="/assets/favicon/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="CPU Prices" />
    <link rel="manifest" href="/assets/favicon/site.webmanifest" />
    <link rel="stylesheet" href="/assets/css/main.css?v=<?php echo filemtime($_SERVER['DOCUMENT_ROOT'] . '/assets/css/main.css'); ?>" />
</head>

<body class="page-loading">

    <div class="page-loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
        <div class="loading-text">Loading CPU data...</div>
    </div>

    <div class="body-overlay" id="body-overlay"></div>

    <header id="header">
        <div class="header-main">
            <a href="<?php echo $home_url; ?>" class="site-icon" aria-label="Go to homepage" title="Go to homepage">
                <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"width="20px" height="20px" viewBox="0,0,256,256"><g transform="translate(-43.52,-43.52) scale(1.34,1.34)"><g fill="none" fill-rule="nonzero" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none" font-size="none" text-anchor="none" style="mix-blend-mode: normal"><g transform="scale(5.33333,5.33333)"><path d="M6,8v32c0,1.1 0.9,2 2,2h32c1.1,0 2,-0.9 2,-2v-32c0,-1.1 -0.9,-2 -2,-2h-32c-1.1,0 -2,0.9 -2,2z" fill="#43a047"></path><path d="M15,10c0,0.6 -0.4,1 -1,1c-0.6,0 -1,-0.4 -1,-1c0,-0.6 0.4,-1 1,-1c0.6,0 1,0.4 1,1zM18,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM18,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM18,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM18,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1z" fill="#ffeb3b"></path><path d="M29,30h-10c-0.5,0 -1,-0.5 -1,-1v-10c0,-0.5 0.5,-1 1,-1h10c0.5,0 1,0.5 1,1v10c0,0.5 -0.5,1 -1,1z" fill="#37474f"></path></g></g></g></svg>
            </a>

            <?php if (!$is_faq && !$is_privacy): ?>
                <div class="source-button" id="source-toggle" role="button" aria-label="Select marketplace source" title="Select marketplace source">
                    <span class="current-source">amazon.com</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>
                </div>

                <button class="header-button" id="filters-toggle" aria-label="Toggle filters" title="Toggle filters">
                    <div class="filter-indicator"></div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="4" y1="21" x2="4" y2="14"></line><line x1="4" y1="10" x2="4" y2="3"></line><line x1="12" y1="21" x2="12" y2="12"></line><line x1="12" y1="8" x2="12" y2="3"></line><line x1="20" y1="21" x2="20" y2="16"></line><line x1="20" y1="12" x2="20" y2="3"></line><line x1="1" y1="14" x2="7" y2="14"></line><line x1="9" y1="8" x2="15" y2="8"></line><line x1="17" y1="16" x2="23" y2="16"></line></svg>
                </button>

                <div class="header-search-container">
                    <input type="text" id="header-search" placeholder="Search CPUs..." />
                    <button type="button" class="search-clear-button" id="header-search-clear">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>

                <div class="header-condition-filter">
                    <span class="header-condition-legend">Condition</span>
                    <div class="header-condition-options">
                        <label><input type="checkbox" data-condition="new" />New</label>
                        <label><input type="checkbox" data-condition="used" />Used</label>
                    </div>
                </div>
            <?php endif; ?>

            <div class="header-right-buttons">
                <button class="header-button" id="menu-toggle" aria-label="Toggle navigation" title="Toggle navigation">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg>
                </button>
            </div>
        </div>

        <?php if (!$is_faq && !$is_privacy): ?>
            <div class="header-panel" id="source-panel">
                <div class="panel-content">
                    <ul class="source-list">
                        <li><a href="/" <?php echo ($current_locale === 'us') ? 'class="active"' : ''; ?>>amazon.com</a></li>
                        <li><a href="/?locale=uk" <?php echo ($current_locale === 'uk') ? 'class="active"' : ''; ?>>amazon.co.uk</a></li>
                        <li><a href="/?locale=de" <?php echo ($current_locale === 'de') ? 'class="active"' : ''; ?>>amazon.de</a></li>
                    </ul>
                </div>
            </div>
        <?php endif; ?>

        <div class="header-panel" id="menu-panel">
            <div class="panel-content">
                <ul class="menu-list">
                    <li><a href="<?php echo $home_url; ?>" <?php echo $is_home ? 'class="active"' : ''; ?>>Home</a></li>
                    <li><a href="<?php echo $faq_url; ?>" <?php echo $is_faq ? 'class="active"' : ''; ?>>FAQ</a></li>
                    <li><a href="<?php echo $privacy_url; ?>" <?php echo $is_privacy ? 'class="active"' : ''; ?>>Privacy Policy</a></li>
                </ul>
            </div>
        </div>
    </header>
