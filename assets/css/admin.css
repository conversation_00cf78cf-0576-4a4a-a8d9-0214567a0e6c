/*
 * CPU Prices Admin Dashboard Styles
 * This file contains all styles for the admin dashboard
 *
 * Optimized for:
 * - Consolidated CSS variables for consistent colors and styling
 * - Reduced redundancy in selectors and properties
 * - Improved organization with logical grouping of related styles
 * - Better responsive design with consolidated media queries
 * - Optimized table layouts with consistent styling
 * - Improved form element styling consistency
 */

:root {
    /* Color palette */
    --primary-color: #2e8a32; /* Darker green for better readability */
    --primary-dark: #1e6022; /* Darker shade of primary color */
    --secondary-color: #ffeb3b;
    --success-color: #2e8a32; /* Matching primary color */
    --error-color: #f44336;
    --warning-color: #f9a825;

    /* Text colors */
    --text-color: #222; /* Darker text color for better readability */
    --text-secondary: #444; /* Darker secondary text */

    /* Background colors */
    --light-gray: #f5f5f5;
    --header-bg: #fff;

    /* Border colors */
    --border-color: #c0c0c0; /* Darker border color for better contrast */
    --input-border: #999999; /* Even darker border for inputs */
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    background-color: var(--light-gray);
    color: var(--text-color);
    font-size: 14px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: #fff;
    overflow: hidden;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    margin-top: 20px;

}

.header {
    background-color: var(--header-bg);
    color: var(--header-text);
    padding: 10px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
}

.header-left {
    display: flex;
    align-items: center;
}

.logo-container {
    margin: 0;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo {
    width: 32px;
    height: 32px;
    margin-right: 10px;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
}

h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: var(--text-color);
}

.content {
    padding: 30px 20px;
}

.login-form {
    max-width: 350px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.form-title {
    text-align: center;
    margin-bottom: 15px;
    margin-top: 0;
    font-size: 18px;
    font-weight: 600; /* Slightly bolder for better readability */
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-color);
    font-size: 13px;
}

/* Common input styles */
.input-common,
input[type="text"],
input[type="password"],
input[type="number"],
input[type="search"],
textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--input-border);
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    background-color: #ffffff;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.08);
}

.input-common:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(67, 160, 71, 0.2);
}

/* Style number input spinner buttons */
input[type="number"] {
    -moz-appearance: textfield; /* Firefox */
    appearance: textfield; /* Standard */
}

/* Hide default spinner buttons */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Ensure number inputs have the same styling as text inputs */
#rule-form input[type="number"] {
    padding-right: 10px; /* Same padding as left side */
}

/* Global select styling */
select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 5px center;
    background-size: 16px;
    padding-right: 40px; /* Increased padding on the right to move the arrow away from the border */
    background-color: white;
    border: 1px solid var(--input-border);
    border-radius: 4px;
    font-size: 14px;
}

/* Button styles */
button,
.btn,
.primary-button {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s;
    letter-spacing: 0.2px; /* Slightly increased letter spacing */
    text-align: center;
    display: inline-block;
}

button:hover,
.btn:hover,
.primary-button:hover {
    background-color: var(--primary-dark);
}

/* Secondary button style */
.btn-secondary,
.secondary-button {
    background-color: #f0f0f0;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover,
.secondary-button:hover {
    background-color: #e0e0e0;
    color: var(--text-color);
}

/* Danger button style */
.btn-danger,
.danger-button {
    background-color: var(--error-color);
}

.btn-danger:hover,
.danger-button:hover {
    background-color: #d32f2f;
}

.error, .error-message {
    color: var(--error-color);
    background-color: rgba(244, 67, 54, 0.1);
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 13px;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.error-container {
    margin-bottom: 15px;
}

.error-container p {
    margin: 0 0 10px 0;
    color: var(--error-color);
}

.error-container details {
    margin-top: 10px;
    font-size: 12px;
}

.error-container summary {
    cursor: pointer;
    color: var(--text-secondary);
    font-weight: 500;
}

.error-container pre {
    margin: 10px 0 0;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    overflow-x: auto;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    border: 1px solid var(--border-color);
}

/* Tab Navigation Styles */
.tab-navigation {
    margin-bottom: 0; /* Remove margin to eliminate gap */
    border-bottom: none; /* Remove border as it's now part of the tab content */
}

.tab-navigation ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.tab-navigation li {
    margin-right: 2px;
}

.tab-navigation a {
    display: block;
    padding: 6px 20px;
    text-decoration: none;
    color: var(--text-secondary);
    background-color: #f5f5f5;
    border: 1px solid var(--border-color);
    border-radius: 4px 4px 0 0;
    font-weight: 500;
    font-size: 14px;
}

.tab-navigation a:hover {
    background-color: #f0f0f0;
}

.tab-navigation a.active {
    background-color: #fff;
    color: var(--primary-color);
    margin-bottom: -1px;
    border-bottom: 1px solid transparent;
    position: relative;
    z-index: 1; /* Ensure active tab is above the content */
}

.tab-navigation a.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: 2px 2px 0 0;
}

/* Tab Content Styles */
.tab-content {
    display: none;
    padding: 20px;
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: 0 0 4px 4px;
    margin-top: -1px; /* Connect with tabs by overlapping */
}

.tab-content.active {
    display: block;
}

/* Inner Tab Navigation Styles */
.inner-tab-navigation {
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.inner-tab-navigation ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.inner-tab-navigation li {
    margin-right: 2px;
}

.inner-tab-navigation a {
    display: block;
    padding: 8px 16px;
    text-decoration: none;
    color: var(--text-secondary);
    background-color: #f5f5f5;
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    font-weight: 500;
    font-size: 13px;
    position: relative;
    bottom: -1px; /* Align with bottom border */
}

.inner-tab-navigation a:hover {
    background-color: #f0f0f0;
}

.inner-tab-navigation a.active {
    background-color: #fff;
    color: var(--primary-color);
    border-bottom: 1px solid #fff;
    z-index: 1; /* Ensure active tab is above the border */
}

.inner-tab-navigation a.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: 2px 2px 0 0;
}

/* Inner Tab Content Styles */
.inner-tab-content {
    display: none;
    margin-top: 20px;
}

.inner-tab-content.active {
    display: block;
}

/* Placeholder content for future tabs */
.placeholder-content {
    background-color: #f9f9f9;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    color: var(--text-secondary);
}

.tab-content h3 {
    margin-top: 0;
    color: var(--text-color);
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 10px;
}

.tab-content p {
    color: var(--text-secondary);
    margin-top: 0;
    margin-bottom: 20px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.action-buttons button {
    flex: 1;
    max-width: 200px;
}

/* Status Panel Styles */
.status-panel {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.status-item {
    background-color: #f9f9f9;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 15px;
    text-align: center;
}

/* CPU Data Management Styles */
.cpu-data-import {
    margin-bottom: 30px;
    background-color: #f9f9f9;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 20px;
}

.cpu-data-import h4,
.cpu-data-stats h4,
.cpu-models-viewer h4,
.final-data-container h4,
.cpu-details-container h4 {
    margin-top: 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
}

.cpu-data-import textarea {
    width: 100%;
    min-height: 200px;
    padding: 10px;
    border: 1px solid var(--input-border);
    border-radius: 4px;
    font-family: monospace;
    font-size: 13px;
    resize: vertical;
    box-sizing: border-box;
}

.cpu-data-import textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(67, 160, 71, 0.2);
}

.form-hint {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 5px;
}

.cpu-data-stats,
.final-data-stats {
    text-align: right;
}

.stats-text {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-secondary);
}

.error-text {
    color: var(--error-color);
}

/* CPU Models Viewer Styles */
.cpu-models-viewer,
.final-data-container,
.cpu-details-container {
    background-color: #f9f9f9;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 20px;
}

.cpu-models-filters,
.final-data-filters,
.cpu-details-filters {
    margin-bottom: 15px;
}

.cpu-models-search,
.final-data-search,
.cpu-details-search {
    position: relative;
    margin-bottom: 10px;
}

.cpu-models-search input,
.final-data-search input,
.cpu-details-search input {
    width: 100%;
    padding: 8px 30px 8px 10px;
    border: 1px solid var(--input-border);
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.cpu-models-search .search-clear-button,
.final-data-search .search-clear-button,
.cpu-details-search .search-clear-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: #999;
    display: none;
    width: auto;
    height: 14px;
}

.cpu-models-search .search-clear-button:hover,
.final-data-search .search-clear-button:hover,
.cpu-details-search .search-clear-button:hover {
    color: var(--error-color);
}

.cpu-models-filter-selects,
.final-data-filter-selects {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
}

.filter-select-group {
    flex: 1;
}

.filter-select-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 13px;
    font-weight: 500;
}

.filter-select-group select {
    width: 100%;
    padding: 7px 10px;
    font-size: 13px;
}

.cpu-models-container {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: white;
}

/* CPU models table specific styles */
.cpu-models-table th.nowrap {
    white-space: nowrap;
}

.cpu-models-table td {
    white-space: nowrap;
}

.stats-item {
    text-align: center;
    padding: 15px;
    background-color: white;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    margin-bottom: 10px;
}

.stats-item h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
}

.stats-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
}

.status-item h4 {
    margin: 0 0 10px 0;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
}

.status-value {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.status-value.success {
    color: var(--primary-color);
}

.status-value.warning {
    color: #f9a825;
}

.status-value.error {
    color: var(--error-color);
}

.welcome-message h2 {
    margin: 0;
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 500;
}

.welcome-message p {
    margin: 3px 0 0;
    color: var(--text-secondary);
    font-size: 13px;
}

.logout {
    text-align: right;
}

.logout a {
    display: inline-block;
    color: #444; /* Darker text for better readability */
    text-decoration: none;
    padding: 5px 10px;
    border: 1px solid var(--input-border);
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.3s;
    background-color: #f8f8f8;
    font-weight: 500;
}

.logout a:hover {
    background-color: #f0f0f0;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.admin-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.admin-option {
    background-color: white;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    text-align: center;
    transition: box-shadow 0.2s, border-color 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.admin-option:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    border-color: var(--input-border);
}

.admin-option h3 {
    margin-top: 0;
    color: var(--primary-color);
    font-size: 15px;
    font-weight: 500;
}

.admin-option p {
    color: var(--text-secondary);
    margin-bottom: 12px;
    font-size: 13px;
}

.admin-option button {
    width: 100%;
    padding: 8px;
    font-size: 13px;
}

.option-icon {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--primary-color);
}

/* Socket Management Styles */
.socket-management {
    margin-top: 20px;
}

.socket-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 15px;
}

.socket-filters {
    display: flex;
    gap: 10px;
    width: 100%;
}

.socket-actions .action-buttons {
    width: 100%;
}

#add-socket-btn {
    width: auto;
}

.socket-filters select {
    padding: 6px 8px;
    font-size: 13px;
    flex: 1;
}

.socket-list-container {
    margin-top: 15px;
    overflow-x: auto;
}

/* Common table styles */
.data-table,
.cpu-models-table,
.custom-rules-table,
.cpu-details-table {
    width: 100%;
    max-width: 100%;
    border-collapse: collapse;
    border: 1px solid var(--border-color);
    background-color: white;
    table-layout: auto;
}

.data-table th,
.cpu-models-table th,
.custom-rules-table th,
.cpu-details-table th {
    padding: 6px 8px 6px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
    background-color: #f5f5f5;
    font-weight: 500;
    color: var(--text-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table td,
.cpu-models-table td,
.custom-rules-table td,
.cpu-details-table td {
    padding: 6px 8px 6px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 13px;
    vertical-align: middle;
}

.data-table th:nth-child(1),
.data-table th:nth-child(2),
.data-table th:nth-child(3),
.data-table th:nth-child(4),
.data-table th:nth-child(5) {
   white-space: nowrap;
}

.data-table tr:hover,
.cpu-models-table tr:hover,
.custom-rules-table tr:hover,
.cpu-details-table tr:hover {
    background-color: #f9f9f9;
}

/* Category header styles */
.data-table tr.category-header {
    background-color: #e0f2f1;
    font-weight: 500;
    color: #00796b;
    text-align: left;
}

.data-table tr.category-header td {
    padding: 5px 8px;
    font-size: 13px;
    border-top: 1px solid #b2dfdb;
}

/* Don't apply hover effect to category headers */
.data-table tr.category-header:hover {
    background-color: #e0f2f1;
}

.data-table .loading-row td {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
}

.data-table .no-data-row td {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
}

.error-container {
    text-align: left;
    background-color: #fff8f8;
    border: 1px solid #ffdddd;
    border-radius: 4px;
    padding: 15px;
    margin: 10px 0;
}

.error-container p {
    color: var(--error-color);
    margin-bottom: 10px;
    font-weight: 500;
}

.error-container details {
    margin-top: 10px;
}

.error-container summary {
    cursor: pointer;
    color: #666;
    font-size: 13px;
    padding: 5px 0;
}

.error-container pre {
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 10px;
    overflow: auto;
    max-height: 200px;
    font-size: 12px;
    margin-top: 5px;
    white-space: pre-wrap;
    word-break: break-all;
}

.variant-badge {
    display: inline-block;
    background-color: #f0f0f0;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 1px 6px;
    margin: 2px;
    font-size: 11px;
}

.socket-tag {
    display: inline-block;
    background-color: #f0f0f0;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    padding: 0px 3px; /* Reduced padding */
    margin: 1px 0; /* Reduced vertical margins */
    font-size: 10px;
}

.socket-tag.matched {
    background-color: #e8f5e9;
    border-color: #c8e6c9;
    color: var(--primary-color);
}

.socket-overview {
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 15px;
}

.socket-overview h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
}

.socket-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    max-height: 300px; /* Increased from 195px per user preference */
    overflow-y: auto;
    padding-right: 5px;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    max-width: 100%;
}

/* Custom scrollbar for Webkit browsers (Chrome, Safari, etc.) */
.socket-tags-container::-webkit-scrollbar {
    width: 6px;
}

.socket-tags-container::-webkit-scrollbar-track {
    background: transparent;
}

.socket-tags-container::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.socket-tags-container::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

.loading-message {
    color: var(--text-secondary);
    font-size: 13px;
    font-style: italic;
}

.error-message {
    color: var(--error-color);
    font-size: 13px;
}

.info-message {
    color: var(--text-secondary);
    font-size: 13px;
}

.action-icon {
    cursor: pointer;
    padding: 5px;
    color: var(--text-secondary);
    transition: color 0.2s;
}

.action-icon:hover {
    color: var(--primary-color);
}

.action-icon.delete:hover {
    color: var(--error-color);
}

.action-cell {
    white-space: nowrap;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden; /* Ensure content doesn't overflow rounded corners */
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.modal-body {
    padding: 20px;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.modal-close {
    color: var(--text-secondary);
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.modal-close:hover {
    color: var(--error-color);
}

.modal-body {
    padding: 20px;
}

/* Form Styles for Socket Management */
#socket-form .form-group {
    margin-bottom: 15px;
}

#socket-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

/* Form-specific input styles are now handled by common input styles */

#socket-form select,
#rule-form select {
    width: 100%;
    padding: 8px 10px;
    box-sizing: border-box;
}

.form-hint {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 3px;
}

#socket-variants-container {
    margin-bottom: 10px;
}

.variant-row {
    display: flex;
    gap: 10px;
    margin-bottom: 8px;
    align-items: center;
}

.variant-row input {
    flex: 1;
}

.remove-variant-btn {
    background-color: #f0f0f0;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    width: auto;
}

.remove-variant-btn:hover {
    background-color: #e0e0e0;
    color: var(--error-color);
}

#add-variant-btn {
    background-color: #f0f0f0;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    width: auto;
    font-size: 13px;
}

#add-variant-btn:hover {
    background-color: #e0e0e0;
    color: var(--primary-color);
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.form-actions button {
    width: 100%;
}

/* Button styles are now consolidated in the common button section */

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #333;
    color: white;
    padding: 15px 20px;
    border-radius: 4px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    display: none;
    z-index: 9999; /* Ensure it's above everything */
    max-width: 300px;
    font-weight: 500;
    font-size: 14px;
    animation: fadeIn 0.3s ease-out;
}

.notification.success {
    background-color: var(--success-color);
}

.notification.error {
    background-color: var(--error-color);
}

.notification-close {
    margin-left: 15px;
    cursor: pointer;
    font-weight: bold;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.notification-close:hover {
    opacity: 1;
}

/* Animation for notifications */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom Rules */
.custom-rules-container {
    margin-top: 20px;
    background-color: #f9f9f9;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 20px;
}

.custom-rules-header {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.custom-rules-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
}

.custom-rules-header p {
    margin: 0 0 10px 0;
    color: var(--text-secondary);
    font-size: 13px;
}

.custom-rules-header button {
    width: auto;
    align-self: flex-start;
}

.custom-rules-list-container {
    margin-top: 15px;
    overflow-x: auto;
}

.custom-rules-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid var(--border-color);
    background-color: white;
}

.custom-rules-table th {
    padding: 6px 8px 6px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
    background-color: #f5f5f5;
    font-weight: 500;
    color: var(--text-color);
}

.custom-rules-table td {
    padding: 6px 8px 6px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 13px;
}

.custom-rules-table tr:hover {
    background-color: #f9f9f9;
}

.custom-rules-table .actions-cell {
    white-space: nowrap;
    text-align: right;
}

.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background-color: #e8f5e9;
    color: var(--primary-color);
    border: 1px solid #c8e6c9;
}

.status-badge.inactive {
    background-color: #f5f5f5;
    color: #757575;
    border: 1px solid #e0e0e0;
}

/* Final Data Styles */
.final-data-header {
    margin-bottom: 20px;
}

.final-data-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
}

.final-data-buttons {
    display: flex;
    gap: 10px;
}

.final-data-stats {
    margin-left: 20px;
    display: flex;
    align-items: center;
    flex-direction: column;
}

.final-data-stats-box,
.cpu-data-stats-box {
    margin-top: 15px;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 10px 15px;
    margin-bottom: 15px;
}

.stats-text {
    font-size: 14px;
    color: var(--text-secondary);
    display: flex;
    flex-wrap: wrap;
    gap: 18px;
    line-height: 1.5;
    font-weight: 400;
}

.stats-text strong {
    color: var(--text-color);
    font-weight: 600;
    letter-spacing: 0.01em;
}

.final-data-list-container {
    margin-top: 20px;
}

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    margin-left: 5px;
    border-radius: 4px;
    color: var(--text-secondary);
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    vertical-align: middle;
}

.icon-button:hover {
    background-color: #f0f0f0;
}

.icon-button.edit-rule:hover {
    color: #2196f3;
}

.icon-button.toggle-rule:hover {
    color: var(--primary-color);
}

/* Active/inactive toggle button styles */
.icon-button.toggle-rule[title="Deactivate Rule"] {
    color: var(--primary-color);
}

.icon-button.toggle-rule[title="Activate Rule"] {
    color: var(--text-secondary);
}

.icon-button.delete-rule:hover {
    color: var(--error-color);
}

/* Add Font Awesome icons */
.icon-button i {
    font-size: 14px;
    display: inline-block;
    width: 14px;
    height: 14px;
    line-height: 14px;
    text-align: center;
    vertical-align: middle;
}

/* Checkbox label styles */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 8px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    /* Layout adjustments */
    .socket-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .socket-filters,
    .cpu-models-filter-selects,
    .final-data-filter-selects {
        width: 100%;
    }

    .socket-filters select {
        flex: 1;
    }

    /* Table adjustments */
    .data-table,
    .cpu-models-table,
    .custom-rules-table,
    .cpu-details-table {
        font-size: 13px;
    }

    .data-table th,
    .data-table td,
    .cpu-models-table th,
    .cpu-models-table td,
    .custom-rules-table th,
    .custom-rules-table td,
    .cpu-details-table th,
    .cpu-details-table td {
        padding: 8px 5px;
    }

    /* Modal adjustments */
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    /* Container adjustments */
    .container {
        max-width: 100%;
        margin: 10px;
    }
}

/* CPU Details Styles */
.cpu-details-header {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.cpu-details-header h4 {
    margin-bottom: 0;
}

.cpu-details-header p {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--text-secondary);
}

.cpu-details-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cpu-details-buttons {
    display: flex;
    gap: 10px;
}

.cpu-details-list-container {
    /* Removed overflow-x to prevent horizontal scrolling */
    width: 100%;
}

/* CPU Details Table specific styles */
.cpu-details-table {
    margin-bottom: 20px;
    table-layout: fixed; /* Use fixed layout to control column widths better */
}

/* Column width definitions */
/* ID column */
.cpu-details-table th:nth-child(1),
.cpu-details-table td:nth-child(1) {
    width: 5%;
}

/* Original ID column */
.cpu-details-table th:nth-child(2),
.cpu-details-table td:nth-child(2) {
    width: 7%;
}

/* Name column - allow wrapping */
.cpu-details-table th:nth-child(3),
.cpu-details-table td:nth-child(3) {
    width: 30%;
    white-space: normal; /* Allow text wrapping */
    word-break: break-word; /* Break long words if necessary */
}

/* Brand, Series, Core Model columns */
.cpu-details-table th:nth-child(4),
.cpu-details-table td:nth-child(4),
.cpu-details-table th:nth-child(5),
.cpu-details-table td:nth-child(5),
.cpu-details-table th:nth-child(6),
.cpu-details-table td:nth-child(6) {
    width: 18%;
}

/* Actions column */
.cpu-details-table th:nth-child(7),
.cpu-details-table td:nth-child(7) {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

/* Default actions cell styling */
.cpu-details-table .actions-cell {
    text-align: center;
    width: 60px;
    min-width: 60px;
    max-width: 60px;
    white-space: nowrap;
    vertical-align: middle;
}


/* Sortable table headers */
th.sortable {
    cursor: pointer;
    position: relative;
    user-select: none;
}

th.sortable:hover {
    background-color: #e8e8e8;
}

th.sortable .sort-icon {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 5px;
    position: relative;
    top: -1px;
}

th.sortable.sort-asc .sort-icon::after {
    content: '▲';
    font-size: 10px;
}

th.sortable.sort-desc .sort-icon::after {
    content: '▼';
    font-size: 10px;
}

/* Editable Fields Styles */
.editable-field {
    position: relative;
    min-height: 20px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;
}

.edit-input {
    width: 100%;
    height: 100%;
    padding: 6px 8px;
    border: 1px solid var(--primary-color);
    border-radius: 3px;
    font-size: 13px;
    background-color: #fff;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: -6px;
    margin: 0;
    display: flex;
    align-items: center;
}

.edit-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(46, 138, 50, 0.2);
    z-index: 1; /* Ensure the focused input is above other elements */
}

/* Clickable field styles */
.clickable-field {
    cursor: pointer;
    transition: background-color 0.2s;
}

/* When clickable-field is a td */
td.clickable-field {
    position: relative;
    padding: 0; /* Remove default padding */
    vertical-align: middle;
    height: 39px; /* Consistent height */
    display: table-cell; /* Ensure proper table cell display */
}

td.clickable-field input {
    height: 31px; /* Consistent height */
    font-size: 14px;
    padding: 0 6px;
}

td.clickable-field:hover {
    background-color: rgba(46, 138, 50, 0.1);
}

/* Display value styles */
.display-value {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Allow wrapping for the Name column */
.cpu-details-table td:nth-child(3) .display-value {
    white-space: normal;
    word-break: break-word;
    display: flex;
    align-items: center;
}

/* Empty field styling - no placeholder text */
.display-value:empty {
    min-height: 18px; /* Ensure empty fields still have height */
}

/* Active edit row styling */
.cpu-details-table tr.active-edit {
    background-color: rgba(46, 138, 50, 0.05);
}

/* Saving state styling */
.cpu-details-table tr.saving {
    opacity: 0.7;
    pointer-events: none;
}

/* CPU details actions cell styling - overrides the default */
.cpu-details-table .actions-cell {
    width: 100px;
    text-align: right;
    min-width: 100px;
    max-width: 100px;
}

.edit-details-btn:hover {
    color: var(--primary-color);
}

.save-details-btn:hover {
    color: var(--success-color);
}

.cancel-details-btn:hover {
    color: var(--error-color);
}

.nowrap {
    white-space: nowrap;
}

/* CPU Identifiers Styles */

/* Identifiers popup */
.identifiers-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.identifiers-popup.active {
    opacity: 1;
    visibility: visible;
}

.identifiers-popup-content {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 0;
    position: relative;
    border: 1px solid #ddd;
}

.identifiers-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    position: relative;
    background-color: #f9f9f9;
}

h3.identifiers-popup-title {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    color: #333;
    margin-bottom: 0;
}

.identifiers-popup-close {
    background: none;
    border: none;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
    padding: 0;
    margin: 0;
    line-height: 1;
    position: absolute;
    top: 15px;
    right: 20px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.identifiers-popup-close:hover {
    color: red;
    background: none;
}

.identifiers-container {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
    background-color: #fff;
}

.identifiers-loading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

/* Icon button */
.cpu-details-table .icon-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #555;
    transition: background-color 0.2s, color 0.2s;
    vertical-align: middle;
    margin: 0 auto;
}

.cpu-details-table .icon-button:hover {
    background-color: #f0f0f0;
    color: #333;
}

/* CPU info section */
.cpu-info-section {
    margin-bottom: 15px;
    padding: 12px;
    border-radius: 4px;
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
}

.cpu-info-row {
    margin-bottom: 8px;
    line-height: 1.4;
    font-size: 14px;
    color: #333;
}

.cpu-info-row:last-child {
    margin-bottom: 0;
}

.identifiers-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.identifiers-section h4 {
    margin: 0;
    color: #444;
    padding-bottom: 8px;
    font-weight: 500;
}

.identifiers-section p {
    color: #666;
    font-style: italic;
    margin: 0 0 10px 0;
}

.identifiers-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.identifier-tag {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 13px;
    background-color: #e9ecef;
    color: #495057;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    border: 1px solid #dee2e6;
}

.simple-identifier {
    background-color: #e3f2fd;
    color: #0d47a1;
    border-color: #bbdefb;
}

.compound-identifier {
    background-color: #e8f5e9;
    color: #1b5e20;
    border-color: #c8e6c9;
}

.identifiers-actions {
    display: flex;
    margin-top: 15px;
}

.generate-identifiers-btn,
.regenerate-identifiers-btn {
    display: inline-block;
    padding: 10px 16px;
    font-size: 14px;
    background-color: #2e7d32;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    width: auto;
}

.generate-identifiers-btn:hover,
.regenerate-identifiers-btn:hover {
    background-color: #1b5e20;
}

.generate-identifiers-btn:disabled,
.regenerate-identifiers-btn:disabled {
    background-color: #c8c8c8;
    cursor: not-allowed;
}

/* Styled checkbox */
.styled-checkbox {
    position: relative;
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid #ccc;
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    background-color: white;
    vertical-align: middle;
}

.styled-checkbox:checked {
    background-color: #4a6ee0;
    border-color: #4a6ee0;
}

.styled-checkbox:checked::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 5px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.styled-checkbox + label {
    cursor: pointer;
    vertical-align: middle;
    margin-left: 5px;
    font-size: 14px;
}

/* Amazon Search */
.amazon-search-terms-container .actions-cell {
    text-align: right;
}

.amazon-search-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

/* Term type badges */
.term-type {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.term-type.auto {
    background-color: #e0f0ff;
    color: #0066cc;
}

.term-type.manual {
    background-color: #ffe0e0;
    color: #cc0000;
}

/* Action buttons */
.actions-cell {
    white-space: nowrap;
    text-align: center;
}

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    margin: 0 2px;
    border-radius: 3px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
}

.icon-button:hover {
    background-color: #f0f0f0;
}

.icon-button.edit-search-term {
    color: #0066cc;
}

.icon-button.toggle-search-term {
    color: #666;
}

.icon-button.toggle-search-term.active {
    color: #28a745;
}

.icon-button.toggle-search-term.inactive {
    color: #dc3545;
}

.icon-button.delete-search-term {
    color: #dc3545;
}

/* Danger button */
.danger-button {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

.danger-button:hover {
    background-color: #c82333;
}

/* Inactive row styling */
.inactive-row {
    opacity: 0.6;
}