<?php
/**
 * Configuration file for CPU Prices Backend
 * Loads settings from environment variables
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

// Load environment variables
require_once __DIR__ . '/../inc/env.php';

// Application environment
define('APP_ENV', env('APP_ENV', 'production'));

// CSRF Protection
define('ENABLE_CSRF_VALIDATION', env('ENABLE_CSRF_VALIDATION', true));

// Admin credentials
define('ADMIN_USERNAME', env('ADMIN_USERNAME', 'admin'));
define('ADMIN_PASSWORD', env('ADMIN_PASSWORD', 'admin_password'));

// Flag to indicate if the password is already hashed
// In production, you should store a hashed password in .env
define('PASSWORD_IS_HASHED', env('PASSWORD_IS_HASHED', false));

// Session settings
define('SESSION_NAME', 'cpuprices_admin');
define('SESSION_LIFETIME', env('SESSION_LIFETIME', 3600)); // 1 hour in seconds

// Security settings
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_TIMEOUT', 300); // 5 minutes in seconds

// Include database connection
require_once __DIR__ . '/../inc/database.php';

// Amazon settings
define('AMAZON_ACCESS_KEY', env('AMAZON_ACCESS_KEY', ''));
define('AMAZON_SECRET_KEY', env('AMAZON_SECRET_KEY', ''));
define('AMAZON_PARTNER_TAG', env('AMAZON_PARTNER_TAG', 'cpu-prices-20'));
define('AMAZON_HOST', env('AMAZON_HOST', 'webservices.amazon.com'));
define('AMAZON_REGION', env('AMAZON_REGION', 'us-east-1'));
