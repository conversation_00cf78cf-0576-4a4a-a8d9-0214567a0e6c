name: 🚀 Deploy to Production

on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: 🎉 Build and Deploy
    runs-on: ubuntu-latest
    env:
      REMOTE_HOST: *************
      REMOTE_USER: ubuntu
      SERVER_PATH: /var/www/html/
      SERVER_USER_AND_GROUP: ubuntu:www-data

      OCI_USER_OCID: ${{ secrets.OCI_USER_OCID }}
      OCI_TENANCY_OCID: ${{ secrets.OCI_TENANCY_OCID }}
      OCI_FINGERPRINT: ${{ secrets.OCI_FINGERPRINT }}
      OCI_REGION: ${{ secrets.OCI_REGION }}
      OCI_SECURITY_RESOURCE_TYPE: 'SL' # Or 'NSG' for Network Security Group
      OCI_SECURITY_RESOURCE_OCID: ${{ secrets.OCI_SECURITY_RESOURCE_OCID }}

    steps:
      - name: 🚚 Get latest codex
        uses: actions/checkout@v3

      - name: 🔧 Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'

      - name: 📦 Install Node.js dependencies
        run: npm install

      - name: 🔨 Build Project
        run: npm run build

            # --- OCI IP Whitelisting Steps ---

      - name: 🌍 Get Runner Public IP using action
        id: ip
        uses: haythem/public-ip@v1.3

      - name: ⚙️ Install OCI CLI and jq
        run: |
          sudo apt-get update && sudo apt-get install -y python3-pip jq
          pip3 install oci-cli

      - name: 🔑 Configure OCI CLI
        run: |
          mkdir -p ~/.oci
          echo "[DEFAULT]" > ~/.oci/config
          echo "user=${{ env.OCI_USER_OCID }}" >> ~/.oci/config
          echo "fingerprint=${{ env.OCI_FINGERPRINT }}" >> ~/.oci/config
          echo "tenancy=${{ env.OCI_TENANCY_OCID }}" >> ~/.oci/config
          echo "region=${{ env.OCI_REGION }}" >> ~/.oci/config
          echo "key_file=~/.oci/key.pem" >> ~/.oci/config
          chmod 600 ~/.oci/config # <-- Added fix for permissions warning
          # Store the private key from secret into the key file
          echo "${{ secrets.OCI_API_PRIVATE_KEY }}" > ~/.oci/key.pem
          chmod 600 ~/.oci/key.pem
          echo "OCI CLI Configured."

      - name: 🔥 Whitelist Runner IP in OCI Security Rules
        id: whitelist
        run: |
          if [ -z "${{ steps.ip.outputs.ipv4 }}" ]; then
            echo "::error::Failed to obtain runner public IP address."
            exit 1
          fi
          runner_ip_cidr="${{ steps.ip.outputs.ipv4 }}/32"
          echo "Whitelisting IP CIDR: $runner_ip_cidr for SSH (port 22)"
          RULE_DESCRIPTION="GitHub Actions Runner (${{ github.run_id }}) - $(date +%s)"
          echo "RULE_DESCRIPTION=${RULE_DESCRIPTION}" >> $GITHUB_ENV

          RULE_JSON=$(jq -n --arg ip "$runner_ip_cidr" --arg desc "$RULE_DESCRIPTION" '{
            "direction": "INGRESS",
            "protocol": "6",
            "source": $ip,
            "sourceType": "CIDR_BLOCK",
            "description": $desc,
            "isStateless": false,
            "tcpOptions": {
              "destinationPortRange": {
                "min": 22,
                "max": 22
              }
            }
          }')
          echo "Rule JSON: $RULE_JSON"

          if [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "NSG" ]]; then
            echo "Adding rule to Network Security Group (NSG)..."
            # NSG operations might still use SUCCEEDED for the work request
            oci network nsg add-security-rules --nsg-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --security-rules "[$RULE_JSON]" --wait-for-state SUCCEEDED --wait-interval-seconds 5
            echo "Fetching Rule OCID using description..."
            RULE_OCID=$(oci network nsg list-security-rules --nsg-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --all --query "data[?description=='$RULE_DESCRIPTION'].id | [0]" --raw-output)

          elif [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "SL" ]]; then
            echo "Adding rule to Security List (SL)..."
            EXISTING_RULES_JSON=$(oci network security-list get --security-list-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --query 'data."ingress-security-rules"' --raw-output)
            UPDATED_RULES_JSON=$(echo "$EXISTING_RULES_JSON" | jq --argjson newrule "$RULE_JSON" '. + [$newrule]')
            # --- FIX IS HERE ---
            oci network security-list update --security-list-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --ingress-security-rules "$UPDATED_RULES_JSON" --force --wait-for-state AVAILABLE --wait-interval-seconds 5
            # --- END FIX ---

          else
            echo "::error::Invalid OCI_SECURITY_RESOURCE_TYPE: Must be 'NSG' or 'SL'."
            exit 1
          fi

          if [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "NSG" && -n "$RULE_OCID" ]]; then
             echo "Found NSG Rule OCID: $RULE_OCID"
             echo "RULE_OCID=${RULE_OCID}" >> $GITHUB_ENV
          elif [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "NSG" && -z "$RULE_OCID" ]]; then
             echo "::warning::Could not reliably determine the OCID of the added NSG rule using description. Removal might fail or remove the wrong rule if descriptions collide."
          fi
          echo "IP Whitelisted successfully."
        env:
          RUNNER_IPV4: ${{ steps.ip.outputs.ipv4 }}

      # --- Deployment Step (Original) ---

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@main
        with:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_KEY }}
          ARGS: '-avzr --delete'
          SOURCE: 'dist/'
          REMOTE_HOST: ${{ env.REMOTE_HOST }}
          REMOTE_USER: ${{ env.REMOTE_USER }}
          TARGET: ${{ env.SERVER_PATH }}
          EXCLUDE: '.env, /data/'
          SCRIPT_AFTER: |
            sudo chown -R ${{ env.SERVER_USER_AND_GROUP }} ${{ env.SERVER_PATH }}

      # --- OCI IP Removal Step (Always Runs) ---

      - name: 💧 Remove Runner IP from OCI Security Rules
        if: always() && steps.whitelist.outcome == 'success'
        run: |
          if [ -z "${{ steps.ip.outputs.ipv4 }}" ]; then
            echo "::warning::Runner public IP was not available from previous step, cannot perform removal accurately, especially for Security Lists."
            exit 0
          fi
          runner_ip_cidr="${{ steps.ip.outputs.ipv4 }}/32"
          echo "Removing firewall rule for Runner IP CIDR: $runner_ip_cidr"
          RULE_DESCRIPTION="${{ env.RULE_DESCRIPTION }}"
          RULE_OCID="${{ env.RULE_OCID }}"

          if [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "NSG" ]]; then
            if [[ -z "$RULE_OCID" ]]; then
              echo "::warning::NSG Rule OCID was not captured during add. Attempting removal by description."
              RULE_OCID=$(oci network nsg list-security-rules --nsg-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --all --query "data[?description=='$RULE_DESCRIPTION'].id | [0]" --raw-output)
              if [[ -z "$RULE_OCID" ]]; then
                 echo "::error::Could not find NSG rule OCID using description '$RULE_DESCRIPTION'. Manual cleanup required."
                 exit 1
              fi
              echo "Found NSG rule OCID for removal: $RULE_OCID"
            fi
            echo "Removing NSG rule with OCID: $RULE_OCID"
            # NSG operations might still use SUCCEEDED for the work request
            oci network nsg remove-security-rules --nsg-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --security-rule-ids "[\"$RULE_OCID\"]" --force --wait-for-state SUCCEEDED --wait-interval-seconds 5
            echo "NSG Rule removed."

          elif [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "SL" ]]; then
            echo "Removing rule from Security List (SL) by filtering..."
            CURRENT_RULES_JSON=$(oci network security-list get --security-list-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --query 'data."ingress-security-rules"' --raw-output)
            FILTERED_RULES_JSON=$(echo "$CURRENT_RULES_JSON" | jq --arg desc "$RULE_DESCRIPTION" --arg ip "$runner_ip_cidr" '[.[] | select(.description != $desc and .source != $ip)]')
            if [[ $(echo "$CURRENT_RULES_JSON" | jq 'length') == $(echo "$FILTERED_RULES_JSON" | jq 'length') ]]; then
               echo "::warning::No rule matching description '$RULE_DESCRIPTION' or source IP '$runner_ip_cidr' found in Security List ${{ env.OCI_SECURITY_RESOURCE_OCID }}."
            else
               echo "Updating Security List with filtered rules..."
               # --- FIX IS HERE ---
               oci network security-list update --security-list-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --ingress-security-rules "$FILTERED_RULES_JSON" --force --wait-for-state AVAILABLE --wait-interval-seconds 5
               # --- END FIX ---
               echo "Security List Rule removed."
            fi

          else
             echo "::error::Invalid OCI_SECURITY_RESOURCE_TYPE encountered during removal."
             exit 1
          fi
          echo "IP rule removal process completed."
        env:
          RUNNER_IPV4: ${{ steps.ip.outputs.ipv4 }}