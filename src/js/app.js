// Get current locale from URL parameter, default to 'us'
var page_locale = 'us';
var urlParams = new URLSearchParams(window.location.search);
if (urlParams.has('locale')) {
    page_locale = urlParams.get('locale');
}
// Initialize default filters to show only desktop CPUs
var default_filters = {
    socket: {
        'intel-desktop': {},
        'amd-desktop': {},
        'intel-server': {},
        'amd-server': {}
    },
    condition: {},
    score_min: 0,
    score_max: null,
    cores_min: 0,
    cores_max: null,
    search: '',
    units: 'thousand'
};

// Set all sockets to unchecked by default
var allSockets = document.querySelectorAll('.socket-options input');
allSockets.forEach(function(socket) {
    default_filters.socket[socket.dataset.category][socket.dataset.socket] = false;
});

// Set all conditions to unchecked by default
var conditions = document.querySelectorAll('.condition input, .header-condition-filter input');
conditions.forEach(function(condition) {
    default_filters.condition[condition.dataset.condition] = false;
});

var f = document.getElementById('filters');
// Units no longer needed with Value calculation

function get_filters() {
    var filters = {
        socket: {},
        condition: {},
        score_min: 0,
        score_max: null,
        cores_min: 0,
        cores_max: null,
        search: '',
        units: 'thousand' // No longer used, kept for backward compatibility
    };

    // Check if filters element exists
    if (!f) {
        return filters;
    }

    // Get search query from filter search, header search, or mobile search
    var headerSearch = document.getElementById('header-search');
    var filterSearch = f.querySelector('#cpu_search');
    var mobileSearch = document.getElementById('mobile-search');

    // Check if any search input has a value
    var hasSearchValue = false;

    // Use the first available search input value
    if (headerSearch && headerSearch.value) {
        filters.search = headerSearch.value.trim().toLowerCase();
        hasSearchValue = true;
    } else if (mobileSearch && mobileSearch.value) {
        filters.search = mobileSearch.value.trim().toLowerCase();
        hasSearchValue = true;
    } else if (filterSearch && filterSearch.value) {
        filters.search = filterSearch.value.trim().toLowerCase();
        hasSearchValue = true;
    }

    // If no search value is found, explicitly set search to empty string
    if (!hasSearchValue) {
        filters.search = '';
    }

    // Get socket filters
    f.querySelectorAll('.socket-options input').forEach(function(checkbox) {
        if(!filters.socket[checkbox.dataset.category]) {
            filters.socket[checkbox.dataset.category] = {};
        }
        filters.socket[checkbox.dataset.category][checkbox.dataset.socket] = checkbox.checked;
    });

    // Get condition filters from header or sidebar
    var conditionCheckboxes = document.querySelectorAll('.header-condition-filter input, .condition input');
    conditionCheckboxes.forEach(function(checkbox) {
        if (checkbox.dataset.condition) {
            filters.condition[checkbox.dataset.condition] = checkbox.checked;
        }
    });

    // Get benchmark score range
    filters.score_min = 0;
    var score_min = f.querySelector('#score_min');
    if(score_min && score_min.value) {
        score_min = Number(score_min.value);
        if(Number.isFinite(score_min) && score_min >= 0) {
            filters.score_min = score_min;
        }
    }

    filters.score_max = null;
    var score_max = f.querySelector('#score_max');
    if(score_max && score_max.value) {
        score_max = Number(score_max.value);
        if(Number.isFinite(score_max) && score_max >= filters.score_min) {
            filters.score_max = score_max;
        }
    }

    // Get cores range
    filters.cores_min = 0;
    var cores_min = f.querySelector('#cores_min');
    if(cores_min && cores_min.value) {
        cores_min = Number(cores_min.value);
        if(Number.isFinite(cores_min) && cores_min >= 0) {
            filters.cores_min = cores_min;
        }
    }

    filters.cores_max = null;
    var cores_max = f.querySelector('#cores_max');
    if(cores_max && cores_max.value) {
        cores_max = Number(cores_max.value);
        if(Number.isFinite(cores_max) && cores_max >= filters.cores_min) {
            filters.cores_max = cores_max;
        }
    }

    return filters;
}

function set_filters(filters) {
    // Check if filters element exists
    if (!f) {
        return;
    }

    // Set socket filters
    for(var category in filters.socket) {
        for(var socket in filters.socket[category]) {
            var el = f.querySelector('.socket-options input[data-socket="' + socket + '"]');
            if(el) {
                el.checked = filters.socket[category][socket];
            }
        }
    }

    // Set condition filters (both in header and sidebar)
    for(var condition in filters.condition) {
        // Update all condition checkboxes with the same data-condition attribute
        document.querySelectorAll('.header-condition-filter input[data-condition="' + condition + '"], .condition input[data-condition="' + condition + '"]').forEach(function(checkbox) {
            checkbox.checked = filters.condition[condition];
        });
    }

    // Set benchmark score range
    var score_min = f.querySelector('#score_min');
    if(score_min && filters.score_min !== null && Number.isFinite(filters.score_min) && filters.score_min > 0) {
        score_min.value = filters.score_min;
    }

    var score_max = f.querySelector('#score_max');
    if(score_max && filters.score_max !== null && Number.isFinite(filters.score_max) && filters.score_max > 0) {
        score_max.value = filters.score_max;
    }

    // Set cores range
    var cores_min = f.querySelector('#cores_min');
    if(cores_min && filters.cores_min !== null && Number.isFinite(filters.cores_min) && filters.cores_min > 0) {
        cores_min.value = filters.cores_min;
    }

    var cores_max = f.querySelector('#cores_max');
    if(cores_max && filters.cores_max !== null && Number.isFinite(filters.cores_max) && filters.cores_max > 0) {
        cores_max.value = filters.cores_max;
    }

    // Set search query in search inputs
    var filterSearch = f.querySelector('#cpu_search');
    var headerSearch = document.getElementById('header-search');
    var mobileSearch = document.getElementById('mobile-search');

    if (filters.search) {
        // Update filter search input
        if (filterSearch) {
            filterSearch.value = filters.search;
        }

        // Update header search input
        if (headerSearch) {
            headerSearch.value = filters.search;
        }

        // Update mobile search input
        if (mobileSearch) {
            mobileSearch.value = filters.search;
        }
    }

    // Set units radio button
    if(filters.units) {
        var unitsRadio = f.querySelector('.units input[data-units="' + filters.units + '"]');
        if(unitsRadio) {
            unitsRadio.checked = true;
            current_units = filters.units;
        }
    }
}

function update_categories(filters) {
    // Check if filters element exists
    if (!f) {
        return;
    }

    // Update category checkboxes based on their child socket checkboxes
    f.querySelectorAll('.socket-group legend input').forEach(function(categoryCheckbox) {
        var category = categoryCheckbox.dataset.category;
        var socketCheckboxes = f.querySelectorAll('.socket-options input[data-category="' + category + '"]');
        var totalSockets = socketCheckboxes.length;
        var checkedSockets = 0;

        socketCheckboxes.forEach(function(socketCheckbox) {
            if(socketCheckbox.checked) {
                checkedSockets++;
            }
        });

        if(checkedSockets === totalSockets) {
            // All sockets in category are selected
            categoryCheckbox.checked = true;
            categoryCheckbox.indeterminate = false;
        } else if(checkedSockets > 0) {
            // Some sockets are selected
            categoryCheckbox.checked = false;
            categoryCheckbox.indeterminate = true;
        } else {
            // No sockets are selected
            categoryCheckbox.checked = false;
            categoryCheckbox.indeterminate = false;
        }
    });
}

function get_category_visibility(filters) {
    var table = document.getElementById('cpuprices-body');
    if (!table) {
        return {};
    }

    // Track which categories have visible CPUs
    var categoryVisibility = {
        'intel-desktop': false,
        'amd-desktop': false,
        'intel-server': false,
        'amd-server': false
    };

    // Flatten socket filters for easier checking
    var socket_filters = {};
    for(var category in filters.socket) {
        for(var socket in filters.socket[category]) {
            socket_filters[socket] = filters.socket[category][socket];
        }
    }

    // Check if any socket filters are active
    var anySocketChecked = false;
    for (var category in filters.socket) {
        for (var socket in filters.socket[category]) {
            if (filters.socket[category][socket]) {
                anySocketChecked = true;
                break;
            }
        }
        if (anySocketChecked) break;
    }

    // Check if any condition filters are active
    var anyConditionChecked = false;
    for (var condition in filters.condition) {
        if (filters.condition[condition]) {
            anyConditionChecked = true;
            break;
        }
    }

    // Loop through all table rows and check which categories have visible CPUs
    for(var i = 0; i < table.children.length; i++) {
        var row = table.children[i];

        // Apply the same filtering logic as update_table
        var socketMatch = !anySocketChecked || socket_filters[row.dataset.socket] || false;
        var conditionMatch = !anyConditionChecked || filters.condition[row.dataset.condition] || false;

        var mark = parseInt(row.querySelector('td:nth-child(3)').textContent.replace(/,/g, ''), 10);
        var cores = parseInt(row.dataset.cores, 10);
        var scoreMatch = in_range(mark, filters.score_min, filters.score_max);
        var coresMatch = in_range(cores, filters.cores_min, filters.cores_max);

        // Search filter
        var searchMatch = true;
        if (filters.search && filters.search.length > 0) {
            var cpuName = row.getAttribute('data-name').toLowerCase();
            searchMatch = cpuName.indexOf(filters.search) !== -1;
        }

        // If this row would be visible, mark its category as having visible CPUs
        if(socketMatch && conditionMatch && scoreMatch && coresMatch && searchMatch) {
            var rowSocket = row.dataset.socket;

            // Find the socket element to determine which category this CPU belongs to
            if (rowSocket) {
                var socketElement = document.querySelector('.socket-options input[data-socket="' + rowSocket + '"]');
                if (socketElement) {
                    var socketCategory = socketElement.dataset.category;
                    if (socketCategory && categoryVisibility.hasOwnProperty(socketCategory)) {
                        categoryVisibility[socketCategory] = true;
                    }
                }
            }
        }
    }

    return categoryVisibility;
}

function update_category_visibility(filters) {
    // Check if filters element exists
    if (!f) {
        return;
    }

    var categoryVisibility = get_category_visibility(filters);

    // Update visibility of each category group
    Object.keys(categoryVisibility).forEach(function(category) {
        var isVisible = categoryVisibility[category];

        // Find the corresponding socket group
        var socketGroup = f.querySelector('.socket-group legend input[data-category="' + category + '"]');
        if (socketGroup) {
            var fieldset = socketGroup.closest('.socket-group');
            if (fieldset) {
                if (isVisible) {
                    fieldset.style.display = '';
                } else {
                    fieldset.style.display = 'none';
                }
            }
        }
    });
}

function in_range(num, min, max) {
    if(max === null) {
        return (num >= min);
    } else {
        return (num >= min && num <= max);
    }
}

function update_table(filters) {
    var table = document.getElementById('cpuprices-body');
    var showResultsButton = document.getElementById('show-results-button');

    // Check if table exists
    if (!table) {
        return;
    }

    var socket_filters = {};

    // Flatten socket filters for easier checking
    for(var category in filters.socket) {
        for(var socket in filters.socket[category]) {
            socket_filters[socket] = filters.socket[category][socket];
        }
    }

    // Check if any socket filters are active
    var anySocketChecked = false;
    for (var category in filters.socket) {
        for (var socket in filters.socket[category]) {
            if (filters.socket[category][socket]) {
                anySocketChecked = true;
                break;
            }
        }
        if (anySocketChecked) break;
    }

    // Check if any condition filters are active
    var anyConditionChecked = false;
    for (var condition in filters.condition) {
        if (filters.condition[condition]) {
            anyConditionChecked = true;
            break;
        }
    }

    // Counter for visible rows
    var visibleRowCount = 0;

    // Loop through all table rows and apply filters
    for(var i = 0; i < table.children.length; i++) {
        var row = table.children[i];

        // Socket matching - if no sockets checked, all sockets match
        var socketMatch = !anySocketChecked || socket_filters[row.dataset.socket] || false;

        // Condition matching - if no conditions checked, all conditions match
        var conditionMatch = !anyConditionChecked || filters.condition[row.dataset.condition] || false;

        var mark = parseInt(row.querySelector('td:nth-child(3)').textContent.replace(/,/g, ''), 10);
        var cores = parseInt(row.dataset.cores, 10);
        var scoreMatch = in_range(mark, filters.score_min, filters.score_max);
        var coresMatch = in_range(cores, filters.cores_min, filters.cores_max);

        // Search filter
        var searchMatch = true;
        if (filters.search && filters.search.length > 0) {
            var cpuName = row.getAttribute('data-name').toLowerCase();
            searchMatch = cpuName.indexOf(filters.search) !== -1;
        } else {
            // If search is empty, all rows match the search criteria
            searchMatch = true;
        }

        if(socketMatch && conditionMatch && scoreMatch && coresMatch && searchMatch) {
            row.style.display = '';
            visibleRowCount++;
        } else {
            row.style.display = 'none';
        }
    }

    // Check if any filters are active (excluding conditions)
    var hasActiveFilters = anySocketChecked ||
                          filters.score_min > 0 ||
                          (filters.score_max !== null && filters.score_max < 100000) ||
                          filters.cores_min > 0 ||
                          (filters.cores_max !== null && filters.cores_max < 128);

    // Check if search is active in mobile view
    var mobileSearchActive = false;
    var headerSearchContainer = document.querySelector('.header-search-container');
    var mobileSearch = document.getElementById('mobile-search');

    // Consider mobile search as active when:
    // 1. Header search is hidden (we're in mobile view)
    // 2. Mobile search has a value
    // (We no longer check if filters are visible - indicator should stay visible)
    if (headerSearchContainer &&
        window.getComputedStyle(headerSearchContainer).display === 'none' &&
        mobileSearch &&
        mobileSearch.value &&
        mobileSearch.value.trim() !== '') {

        // Mobile search is active regardless of filter visibility
        mobileSearchActive = true;
    }

    // Update filter indicator - include mobile search in the condition
    const filtersToggle = document.getElementById('filters-toggle');
    if (filtersToggle) {
        filtersToggle.classList.toggle('has-active-filters', hasActiveFilters || mobileSearchActive);
    }

    // Check if search is active
    var searchActive = filters.search && filters.search.length > 0;

    // Update the show results button text and state
    if (showResultsButton) {
        if (!hasActiveFilters && !searchActive) {
            // No filters or search active, show "Close filters"
            showResultsButton.textContent = 'Close filters';
            showResultsButton.classList.add('close-filters');
            showResultsButton.disabled = false;
        } else if (visibleRowCount === 0) {
            // Has filters or search but no results
            showResultsButton.textContent = 'No results found';
            showResultsButton.classList.add('close-filters');
            showResultsButton.disabled = false;
        } else {
            // Has filters or search and results
            var resultText = visibleRowCount === 1 ? 'result' : 'results';
            showResultsButton.textContent = 'Show ' + visibleRowCount + ' ' + resultText;
            showResultsButton.classList.remove('close-filters');
            showResultsButton.disabled = false;
        }
    }

    // Update category visibility based on current filters
    update_category_visibility(filters);
}

function get_urlstate() {
    // Parse URL search params manually to avoid automatic decoding
    var search = window.location.search.substring(1); // Remove the leading '?'
    var params = {};

    if (search) {
        var pairs = search.split('&');
        for (var i = 0; i < pairs.length; i++) {
            var pair = pairs[i].split('=');
            params[pair[0]] = pair[1] || '';
        }
    }

    // Store debug parameter if present (for internal testing purposes)
    var debug = params['debug'];

    var sockets = params['sockets'];
    if(sockets === undefined) {
        sockets = [];
    } else {
        if(sockets.indexOf(',') != -1) {
            sockets = sockets.split(',');
        } else {
            sockets = [sockets];
        }
    }

    var conditions = params['condition'];
    if(conditions === undefined) {
        conditions = [];
    } else {
        if(conditions.indexOf(',') != -1) {
            conditions = conditions.split(',');
        } else {
            conditions = [conditions];
        }
    }

    var filters = get_filters();
    var count = 0;

    // Set socket filters from URL
    for(var category in filters.socket) {
        for(var socket in filters.socket[category]) {
            if(sockets.indexOf(socket) != -1) {
                filters.socket[category][socket] = true;
                count++;
            } else {
                filters.socket[category][socket] = false;
            }
        }
    }

    // Set condition filters from URL
    for(var condition in filters.condition) {
        if(conditions.indexOf(condition) != -1) {
            filters.condition[condition] = true;
            count++;
        } else {
            filters.condition[condition] = false;
        }
    }

    // Set benchmark score range from URL
    var score_range = params['score'];
    if(score_range !== undefined) {
        score_range = score_range.split('-');
        if(score_range.length == 2) {
            var min = Number(score_range[0]);
            var max = Number(score_range[1]);
            if(Number.isFinite(min) && min >= 0) {
                filters.score_min = min;
                count++;
            }
            if(Number.isFinite(max) && max > 0) {
                filters.score_max = max;
            }
        }
    }

    // Set cores range from URL
    var cores_range = params['cores'];
    if(cores_range !== undefined) {
        cores_range = cores_range.split('-');
        if(cores_range.length == 2) {
            var min = Number(cores_range[0]);
            var max = Number(cores_range[1]);
            if(Number.isFinite(min) && min >= 0) {
                filters.cores_min = min;
                count++;
            }
            if(Number.isFinite(max) && max > 0) {
                filters.cores_max = max;
            }
        }
    }

    // Set search query from URL
    var search = params['search'];
    if(search !== undefined && search !== '') {
        filters.search = decodeURIComponent(search);
        count++;
    }

    // Set units from URL
    var units = params['units'];
    if(units !== undefined && (units === 'thousand' || units === 'hundred')) {
        filters.units = units;
        count++;
    }

    // Store debug parameter if present (for internal testing purposes)
    if(debug !== undefined) {
        filters.debug = debug;
    }

    if(count > 0) {
        return filters;
    } else {
        return null;
    }
}

function set_urlstate(filters) {
    var sockets = [];
    var conditions = [];

    // Collect selected sockets
    for(var category in filters.socket) {
        for(var socket in filters.socket[category]) {
            if(filters.socket[category][socket]) {
                sockets.push(socket);
            }
        }
    }

    // Collect selected conditions
    for(var condition in filters.condition) {
        if(filters.condition[condition]) {
            conditions.push(condition);
        }
    }

    // Get existing URL parameters for sorting
    var url = new URL(window.location.href);
    var sortColumn = url.searchParams.get('sort');
    var sortDirection = url.searchParams.get('order');
    var debug = url.searchParams.get('debug');

    // Build new URL parameters
    var params = {};

    // Add locale only if it's not the default (us)
    if (page_locale && page_locale !== 'us') {
        params['locale'] = page_locale;
    }

    // Add conditions
    if(conditions.length > 0) {
        params['condition'] = conditions.join(',');
    }

    // Add score range
    var score = '-';
    if(filters.score_min > 0) {
        score = filters.score_min + score;
    }
    if(filters.score_max !== null) {
        score += filters.score_max;
    }
    if(score != '-') {
        params['score'] = score;
    }

    // Add cores range
    var cores = '-';
    if(filters.cores_min > 0) {
        cores = filters.cores_min + cores;
    }
    if(filters.cores_max !== null) {
        cores += filters.cores_max;
    }
    if(cores != '-') {
        params['cores'] = cores;
    }

    // Add sockets
    if(sockets.length > 0) {
        params['sockets'] = sockets.join(',');
    }

    // Add search query
    if(filters.search && filters.search.length > 0) {
        params['search'] = encodeURIComponent(filters.search);
    } else {
        // Explicitly remove search parameter if search is empty
        delete params['search'];
    }

    // Add units parameter
    if(filters.units && filters.units !== 'thousand') {
        params['units'] = filters.units;
    }

    // Preserve sort parameters
    if(sortColumn) {
        params['sort'] = sortColumn;
    }
    if(sortDirection) {
        params['order'] = sortDirection;
    }

    // Preserve debug parameter if present (for internal testing purposes)
    if(debug !== null) {
        params['debug'] = debug;
    }

    // Build the URL manually
    var newUrl = window.location.pathname;
    var paramPairs = [];

    for(var key in params) {
        if(params[key]) {
            paramPairs.push(key + '=' + params[key]);
        }
    }

    // Update URL
    if(paramPairs.length > 0) {
        window.history.pushState(null, '', newUrl + '?' + paramPairs.join('&'));
    } else {
        window.history.pushState(null, '', newUrl);
    }
}

function click_category(event) {
    var category = event.target.dataset.category;
    document.querySelectorAll('.socket-options input[data-category="' + category + '"]').forEach(function(checkbox) {
        checkbox.checked = event.target.checked;
    });

    var filters = get_filters();
    update_table(filters);
    set_urlstate(filters);
}

function click_socket(event) {
    update_categories(get_filters());

    var filters = get_filters();
    update_table(filters);
    set_urlstate(filters);
}

function click_condition(event) {
    var filters = get_filters();
    update_table(filters);
    set_urlstate(filters);
}

function change_score(event) {
    var filters = get_filters();
    update_table(filters);
    set_urlstate(filters);
}

function change_cores(event) {
    var filters = get_filters();
    update_table(filters);
    set_urlstate(filters);
}

function change_search(event) {
    var filters = get_filters();
    update_table(filters);

    // Check if search is empty and directly remove from URL if needed
    if (!filters.search || filters.search.trim() === '') {
        // Get current URL and remove search parameter
        var url = new URL(window.location.href);
        url.searchParams.delete('search');

        // Update URL without the search parameter
        window.history.replaceState(null, '', url);
    } else {
        // Normal URL update with all parameters
        set_urlstate(filters);
    }
}

// Global filter_table function that can be called from anywhere
window.filter_table = function() {
    var filters = get_filters();
    update_table(filters);

    // Check if search is empty and directly remove from URL if needed
    if (!filters.search || filters.search.trim() === '') {
        // Get current URL and remove search parameter
        var url = new URL(window.location.href);
        url.searchParams.delete('search');

        // Update URL without the search parameter
        window.history.replaceState(null, '', url);
    } else {
        // Normal URL update with all parameters
        set_urlstate(filters);
    }
}

function change_units(event) {
    // Units no longer affect calculation with the new Value metric
    // Function kept for backward compatibility
    var filters = get_filters();
    update_table(filters);
    set_urlstate(filters);
}

function updateValueDisplay() {
    var table = document.getElementById('cpuprices-body');

    // Check if table exists
    if (!table) {
        return;
    }

    // Loop through all table rows
    for(var i = 0; i < table.children.length; i++) {
        var row = table.children[i];
        var mark = parseFloat(row.dataset.mark);
        var price = parseFloat(row.dataset.price);

        if (mark && price) {
            // Calculate value (mark / price)
            var value = mark / price;

            // Update the data-value attribute
            row.dataset.value = value.toFixed(4);

            // Update the displayed value
            var valueCell = row.querySelector('td:first-child');
            if (valueCell) {
                // Format with 2 decimal places
                valueCell.textContent = value.toFixed(2);
            }
        }
    }

    // Always re-sort the table to ensure values are displayed correctly
    if (currentSortColumn === 'value') {
        sortTableWithoutUrlUpdate('value');
    }
}

// Initialize default filters
default_filters = get_filters();

// Add event listeners only if filters element exists
if (f) {
    f.querySelectorAll('.socket-group legend input').forEach(function(el) {
        el.addEventListener('click', click_category);
    });

    f.querySelectorAll('.socket-options input').forEach(function(el) {
        el.addEventListener('click', click_socket);
    });

    f.querySelectorAll('.condition input').forEach(function(el) {
        el.addEventListener('click', click_condition);
    });

    f.querySelectorAll('.benchmark input').forEach(function(el) {
        el.addEventListener('keyup', change_score);
        el.addEventListener('change', change_score);
    });

    f.querySelectorAll('.cores input').forEach(function(el) {
        el.addEventListener('keyup', change_cores);
        el.addEventListener('change', change_cores);
    });

    // Add search input event listeners
    var searchInput = f.querySelector('#cpu_search');
    if (searchInput) {
        searchInput.addEventListener('keyup', change_search);
        searchInput.addEventListener('change', function(event) {
            // Special handling for empty search
            if (searchInput.value === '') {
                // Clear all search inputs
                var headerSearch = document.getElementById('header-search');
                if (headerSearch) headerSearch.value = '';

                // Get filters and update table
                var filters = get_filters();
                update_table(filters);

                // Directly remove search parameter from URL
                var url = new URL(window.location.href);
                url.searchParams.delete('search');
                window.history.replaceState(null, '', url);
            } else {
                // Normal search handling
                change_search(event);
            }
        });
        searchInput.addEventListener('input', change_search); // Add input event to catch all changes
    }

    // Add event listeners for header search input
    var headerSearch = document.getElementById('header-search');
    if (headerSearch) {
        headerSearch.addEventListener('keyup', change_search);
        headerSearch.addEventListener('change', change_search);
        headerSearch.addEventListener('input', change_search);
    }



    // Add units radio button event listeners
    f.querySelectorAll('.units input').forEach(function(el) {
        el.addEventListener('change', change_units);
    });

    // Handle browser back/forward navigation
    window.addEventListener('popstate', function() {
        var filters = get_urlstate();
        if(filters === null) {
            filters = default_filters;
        }
        set_filters(filters);
        update_categories(filters);
        updateValueDisplay();
        update_table(filters);
    });
}

// Sorting functionality
var currentSortColumn = 'value';
var currentSortDirection = 'desc';

function sortTable(column) {
    var header = document.querySelector('th[data-sort="' + column + '"]');

    // Update sort direction
    if (column === currentSortColumn) {
        // Toggle direction if same column
        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        // Default to descending for new column (higher value is better)
        currentSortDirection = 'desc';
        // Remove sort classes from previous column
        document.querySelector('th[data-sort="' + currentSortColumn + '"]').classList.remove('sort-asc', 'sort-desc');
    }

    // Update sort indicators
    header.classList.remove('sort-asc', 'sort-desc');
    header.classList.add('sort-' + currentSortDirection);

    // Update current sort column
    currentSortColumn = column;

    // Sort the table
    sortTableWithoutUrlUpdate(column);

    // Update URL state with sort parameters
    updateUrlWithSort();
}

function updateUrlWithSort() {
    // Get existing URL parameters
    var url = new URL(window.location.href);
    var params = url.searchParams;

    // Update sort parameters
    params.set('sort', currentSortColumn);
    params.set('order', currentSortDirection);

    // Build the URL manually to avoid automatic encoding of commas
    var newUrl = window.location.pathname + '?';
    var paramPairs = [];

    params.forEach(function(value, key) {
        // For parameters that might contain commas (like 'sockets' or 'condition')
        // we want to preserve the commas without encoding
        if (key === 'sockets' || key === 'condition') {
            // Only add if the value is not empty
            if (value) {
                paramPairs.push(key + '=' + value);
            }
        } else {
            paramPairs.push(key + '=' + value);
        }
    });

    // Preserve debug parameter if present (for internal testing purposes)
    var debug = params.get('debug');
    if (debug !== null && !paramPairs.some(pair => pair.startsWith('debug='))) {
        paramPairs.push('debug=' + debug);
    }

    newUrl += paramPairs.join('&');
    window.history.replaceState(null, '', newUrl);
}

function getSortFromUrl() {
    try {
        // Check if we have a table to sort
        if (!document.getElementById('cpuprices-body')) {
            return;
        }

        // Parse URL search params manually to avoid automatic decoding
        var search = window.location.search.substring(1); // Remove the leading '?'
        var params = {};

        if (search) {
            var pairs = search.split('&');
            for (var i = 0; i < pairs.length; i++) {
                var pair = pairs[i].split('=');
                params[pair[0]] = pair[1] || '';
            }
        }

        var sortColumn = params['sort'];
        var sortDirection = params['order'];

        // Only update if we have valid parameters
        if (sortColumn && document.querySelector('th[data-sort="' + sortColumn + '"]')) {
            currentSortColumn = sortColumn;

            if (sortDirection === 'asc' || sortDirection === 'desc') {
                currentSortDirection = sortDirection;
            }

            // Update sort indicators
            document.querySelectorAll('th.sortable').forEach(function(th) {
                th.classList.remove('sort-asc', 'sort-desc');
            });

            var header = document.querySelector('th[data-sort="' + currentSortColumn + '"]');
            if (header) {
                header.classList.add('sort-' + currentSortDirection);
            }

            // Sort the table without updating URL (to avoid circular updates)
            sortTableWithoutUrlUpdate(currentSortColumn);
        } else {
            // If no valid sort parameters, set default sort indicator
            document.querySelectorAll('th.sortable').forEach(function(th) {
                th.classList.remove('sort-asc', 'sort-desc');
            });

            var defaultHeader = document.querySelector('th[data-sort="value"]');
            if (defaultHeader) {
                defaultHeader.classList.add('sort-desc');
            }
        }
    } catch (e) {
        console.error('Error parsing URL parameters:', e);
    }
}

// Sort table without updating URL (to avoid circular updates)
function sortTableWithoutUrlUpdate(column) {
    var table = document.getElementById('cpuprices-body');

    // Check if table exists
    if (!table) {
        return;
    }

    var rows = Array.from(table.getElementsByTagName('tr'));

    // Sort the rows
    rows.sort(function(a, b) {
        var aValue = a.getAttribute('data-' + column);
        var bValue = b.getAttribute('data-' + column);

        // Handle numeric values
        if (column === 'value' || column === 'price' || column === 'mark' || column === 'tdp' || column === 'cores') {
            aValue = parseFloat(aValue) || 0;
            bValue = parseFloat(bValue) || 0;
        }

        // Handle string values (case-insensitive comparison for text)
        if (column === 'name' || column === 'socket' || column === 'type' || column === 'condition') {
            aValue = (aValue || '').toLowerCase();
            bValue = (bValue || '').toLowerCase();
        }

        // Compare values
        if (aValue < bValue) {
            return currentSortDirection === 'asc' ? -1 : 1;
        } else if (aValue > bValue) {
            return currentSortDirection === 'asc' ? 1 : -1;
        } else {
            return 0;
        }
    });

    // Reorder the table
    rows.forEach(function(row) {
        table.appendChild(row);
    });
}

// Add click event listeners to sortable headers if they exist
var sortableHeaders = document.querySelectorAll('th.sortable');
if (sortableHeaders.length > 0) {
    sortableHeaders.forEach(function(th) {
        th.addEventListener('click', function() {
            sortTable(this.getAttribute('data-sort'));
        });
    });
}

// Function to hide the loading overlay and show the page content
function showContent() {
    // Remove page-loading class from body
    document.body.classList.remove('page-loading');

    // Hide the loading overlay
    var loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// Initialize filters and sorting before the page is rendered
// This script is placed at the end of the body, so we can run it immediately
(function initializeFiltersBeforeRender() {
    // Check if we're on a page with the filters and table
    var hasFiltersAndTable = document.getElementById('filters') && document.getElementById('cpuprices-body');

    if (hasFiltersAndTable) {
        // Get filters from URL
        var filters = get_urlstate();

        // Apply filters if they exist in URL
        if (filters !== null) {
            // Set filter UI elements
            set_filters(filters);

            // Update category checkboxes
            update_categories(filters);

            // Apply filters to table
            update_table(filters);

            // Apply sorting
            getSortFromUrl();
        } else {
            // No URL filters, use defaults
            filters = default_filters;

            // Set filter UI elements to match default filters
            set_filters(filters);

            // Update category checkboxes
            update_categories(filters);

            // Apply filters to table
            update_table(filters);
        }

        // Update Value display
        updateValueDisplay();
    }

    // Show the content after initialization is complete regardless of page type
    // Use a small timeout to ensure all DOM updates are complete
    setTimeout(function() {
        showContent();
    }, 100);
})();

// Backup initialization in case the above fails
document.addEventListener('DOMContentLoaded', function() {
    // Show the content if it's still hidden after 500ms
    setTimeout(function() {
        if (document.body.classList.contains('page-loading')) {
            showContent();
        }
    }, 500);
});

// Tablet-specific functionality for responsive layouts

// Handle window resize to maintain tablet layout
window.addEventListener('resize', function() {
    // Reset filters display when resizing between breakpoints
    const filters = document.getElementById('filters');
    if (filters) {
        // Check if filters are in fixed position mode
        if (filters.classList.contains('fixed-position')) {
            // Check if we're not in tablet range anymore
            if (window.innerWidth > 1024) {
                // Remove all inline styles by clearing the style attribute
                filters.removeAttribute('style');
                // Remove the fixed-position class
                filters.classList.remove('fixed-position');
                // Remove the filters-active class from body to hide overlay
                document.body.classList.remove('filters-active');
            } else {
                // Update styles for tablet screen size (768px-1024px)
                filters.style.display = 'flex';
                filters.style.flexWrap = 'wrap';
            }
        }
    }

    // Also remove the overlay if we're on desktop, regardless of filter state
    if (window.innerWidth > 1024) {
        document.body.classList.remove('filters-active');
    }

    // Update filter indicator when screen size changes
    // This ensures the indicator is updated when switching between mobile and desktop views
    if (typeof updateFilterIndicator === 'function') {
        // Use setTimeout to ensure the DOM has updated with new display properties
        setTimeout(updateFilterIndicator, 100);
    }
});

// Initialize tablet functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for condition checkboxes in the header
    var headerConditionCheckboxes = document.querySelectorAll('.header-condition-filter input');
    headerConditionCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('click', click_condition);
    });
    // Set up filters toggle button for tablet
    const filtersToggle = document.getElementById('filters-toggle');
    const filters = document.getElementById('filters');
    const showResultsButton = document.getElementById('show-results-button');

    // Function to hide filters panel
    function hideFiltersPanel() {
        if (filters) {
            filters.removeAttribute('style');
            filters.classList.remove('fixed-position');

            // Remove the filters-active class from body to hide overlay
            document.body.classList.remove('filters-active');
        }
    }

    if (filtersToggle && filters) {
        filtersToggle.addEventListener('click', function() {
            // Check if filters are already displayed
            const filtersDisplayed = filters.classList.contains('fixed-position');

            if (filtersDisplayed) {
                // Hide filters
                hideFiltersPanel();

                // Update filter indicator after hiding filters
                if (typeof updateFilterIndicator === 'function') {
                    // Use setTimeout to ensure the DOM has updated
                    setTimeout(updateFilterIndicator, 100);
                }
            } else {
                // Show filters
                filters.classList.add('fixed-position');
                filters.style.display = 'flex';
                filters.style.flexWrap = 'wrap';

                // Add the filters-active class to body to show overlay
                document.body.classList.add('filters-active');

                // Update the results count immediately
                var currentFilters = get_filters();
                update_table(currentFilters);

                // Make sure the filter indicator stays visible if mobile search is active
                if (typeof updateFilterIndicator === 'function') {
                    updateFilterIndicator();
                }
            }
        });
    }

    // Add event listener for the Show Results button
    if (showResultsButton) {
        showResultsButton.addEventListener('click', function() {
            hideFiltersPanel();
        });
    }

    // Set up source toggle
    const sourceToggle = document.getElementById('source-toggle');
    const sourcePanel = document.getElementById('source-panel');

    if (sourceToggle && sourcePanel) {
        sourceToggle.addEventListener('click', function() {
            // Toggle source panel
            if (sourcePanel.classList.contains('active')) {
                sourcePanel.classList.remove('active');
            } else {
                // Close menu panel if open
                const menuPanel = document.getElementById('menu-panel');
                if (menuPanel) {
                    menuPanel.classList.remove('active');
                }
                sourcePanel.classList.add('active');
            }
        });
    }

    // Set up menu toggle
    const menuToggle = document.getElementById('menu-toggle');
    const menuPanel = document.getElementById('menu-panel');

    if (menuToggle && menuPanel) {
        menuToggle.addEventListener('click', function() {
            // Toggle menu panel
            if (menuPanel.classList.contains('active')) {
                menuPanel.classList.remove('active');
            } else {
                // Close source panel if open
                const sourcePanel = document.getElementById('source-panel');
                if (sourcePanel) {
                    sourcePanel.classList.remove('active');
                }
                menuPanel.classList.add('active');
            }
        });
    }

    // Close panels when clicking outside
    document.addEventListener('click', function(event) {
        // Close source panel if click is outside
        if (sourcePanel && !sourcePanel.contains(event.target) && !sourceToggle.contains(event.target)) {
            sourcePanel.classList.remove('active');
        }

        // Close menu panel if click is outside
        if (menuPanel && !menuPanel.contains(event.target) && !menuToggle.contains(event.target)) {
            menuPanel.classList.remove('active');
        }

        // Close filters if click is outside and not on condition checkboxes or search in header
        if (filters && filters.classList.contains('fixed-position') &&
            !filters.contains(event.target) &&
            !filtersToggle.contains(event.target) &&
            !event.target.closest('.header-condition-filter') &&
            !event.target.closest('.header-search-container')) {
            hideFiltersPanel();
        }
    });

    // Make the overlay clickable to close filters
    const bodyOverlay = document.getElementById('body-overlay');
    if (bodyOverlay) {
        bodyOverlay.addEventListener('click', function() {
            hideFiltersPanel();
        });
    }

    // Update current source display
    const activeSource = document.querySelector('.source-list a.active');
    const currentSourceDisplay = document.querySelector('.current-source');

    if (activeSource && currentSourceDisplay) {
        currentSourceDisplay.textContent = activeSource.textContent;
    }
});

// Add event listeners for header search input and clear button
var headerSearch = document.getElementById('header-search');
var headerSearchClear = document.getElementById('header-search-clear');

if (headerSearch && headerSearchClear) {
    // Show/hide clear button based on input content
    function toggleClearButton() {
        headerSearchClear.style.display = headerSearch.value ? 'block' : 'none';
    }

    // Clear search function
    function clearSearch() {
        headerSearch.value = '';
        headerSearchClear.style.display = 'none';

        // Clear filter search input as well
        var filterSearch = document.getElementById('cpu_search');
        if (filterSearch) {
            filterSearch.value = '';
        }

        // Clear mobile search input as well
        var mobileSearch = document.getElementById('mobile-search');
        if (mobileSearch) {
            mobileSearch.value = '';

            // Also hide its clear button
            var mobileSearchClear = document.getElementById('mobile-search-clear');
            if (mobileSearchClear) {
                mobileSearchClear.style.display = 'none';
            }

            // Update filter indicator for mobile search
            if (typeof updateFilterIndicator === 'function') {
                updateFilterIndicator();
            }
        }

        // Trigger search change event
        var event = new Event('change');
        headerSearch.dispatchEvent(event);
        headerSearch.focus();
    }

    // Add event listeners
    headerSearch.addEventListener('input', toggleClearButton);
    headerSearch.addEventListener('change', toggleClearButton);

    // Prevent clicks on header search from closing filters
    headerSearch.addEventListener('click', function(event) {
        // Prevent event from bubbling up to document
        event.stopPropagation();
    });

    // Add click handler for clear button that also prevents propagation
    headerSearchClear.addEventListener('click', function(event) {
        // Clear the search
        clearSearch();

        // Prevent event from bubbling up to document
        event.stopPropagation();
    });

    // Initial state
    toggleClearButton();
}

// Add event listeners for mobile search input and clear button
var mobileSearch = document.getElementById('mobile-search');
var mobileSearchClear = document.getElementById('mobile-search-clear');

if (mobileSearch && mobileSearchClear) {
    // Show/hide clear button based on input content
    function toggleMobileClearButton() {
        mobileSearchClear.style.display = mobileSearch.value ? 'block' : 'none';
    }

    // Clear search function
    function clearMobileSearch() {
        mobileSearch.value = '';
        mobileSearchClear.style.display = 'none';

        // Clear header search input as well
        if (headerSearch) {
            headerSearch.value = '';

            // Also hide its clear button
            if (headerSearchClear) {
                headerSearchClear.style.display = 'none';
            }
        }

        // Clear filter search input as well
        var filterSearch = document.getElementById('cpu_search');
        if (filterSearch) {
            filterSearch.value = '';
        }

        // Trigger search change event
        var event = new Event('change');
        mobileSearch.dispatchEvent(event);
        mobileSearch.focus();
    }

    // Sync search with header search
    function syncMobileSearch() {
        // Update filters and table
        var filters = get_filters();
        update_table(filters);

        // Update URL
        if (!filters.search || filters.search.trim() === '') {
            // Get current URL and remove search parameter
            var url = new URL(window.location.href);
            url.searchParams.delete('search');
            window.history.replaceState(null, '', url);
        } else {
            // Normal URL update with all parameters
            set_urlstate(filters);
        }

        // Sync with header search if it exists
        if (headerSearch) {
            headerSearch.value = mobileSearch.value;

            // Update header search clear button
            if (headerSearchClear) {
                headerSearchClear.style.display = mobileSearch.value ? 'block' : 'none';
            }
        }

        // Update filter indicator based on mobile search value
        updateFilterIndicator();
    }

    // Function to update filter indicator based on mobile search
    function updateFilterIndicator() {
        const filtersToggle = document.getElementById('filters-toggle');
        if (!filtersToggle) return;

        // Check if any filters are active (excluding conditions)
        var filters = get_filters();
        var hasActiveFilters = false;

        // Check for active socket filters
        for (var category in filters.socket) {
            for (var socket in filters.socket[category]) {
                if (filters.socket[category][socket]) {
                    hasActiveFilters = true;
                    break;
                }
            }
            if (hasActiveFilters) break;
        }

        // Check for other active filters
        hasActiveFilters = hasActiveFilters ||
                          filters.score_min > 0 ||
                          (filters.score_max !== null && filters.score_max < 100000) ||
                          filters.cores_min > 0 ||
                          (filters.cores_max !== null && filters.cores_max < 128);

        // Check if mobile search is active
        var mobileSearchActive = false;
        var headerSearchContainer = document.querySelector('.header-search-container');

        // Consider mobile search as active when:
        // 1. Header search is hidden (we're in mobile view)
        // 2. Mobile search has a value
        // (We no longer check if filters are visible - indicator should stay visible)
        if (headerSearchContainer &&
            window.getComputedStyle(headerSearchContainer).display === 'none' &&
            mobileSearch &&
            mobileSearch.value &&
            mobileSearch.value.trim() !== '') {

            // Mobile search is active regardless of filter visibility
            mobileSearchActive = true;
        }

        // Update filter indicator
        filtersToggle.classList.toggle('has-active-filters', hasActiveFilters || mobileSearchActive);
    }

    // Add event listeners
    mobileSearch.addEventListener('input', function() {
        toggleMobileClearButton();
        updateFilterIndicator();
        // Update the table to refresh the results count
        var filters = get_filters();
        update_table(filters);
    });
    mobileSearch.addEventListener('change', function() {
        toggleMobileClearButton();
        syncMobileSearch();
    });
    mobileSearch.addEventListener('keyup', syncMobileSearch);

    // Prevent clicks on mobile search from closing filters
    mobileSearch.addEventListener('click', function(event) {
        // Prevent event from bubbling up to document
        event.stopPropagation();
    });

    // Add click handler for mobile clear button that also prevents propagation
    mobileSearchClear.addEventListener('click', function(event) {
        // Clear the search
        clearMobileSearch();
        updateFilterIndicator();

        // Update the table to refresh the results count
        var filters = get_filters();
        update_table(filters);

        // Prevent event from bubbling up to document
        event.stopPropagation();
    });

    // Initial state
    toggleMobileClearButton();
    updateFilterIndicator();
}
