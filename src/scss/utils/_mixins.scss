// Media query mixins

// Map of breakpoint names to their values
$breakpoints: (
  'xs': $breakpoint-xs,   // 320px
  'sm': $breakpoint-sm,   // 380px
  'md': $breakpoint-md,   // 480px
  'lg': $breakpoint-lg,   // 768px
  'xl': $breakpoint-xl    // 1024px
);

// Media up - min-width: breakpoint
// For styles that apply from this breakpoint and up
@mixin media-up($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}. Available breakpoints: #{map-keys($breakpoints)}";
  }
}

// Media down - max-width: breakpoint - 1px
// For styles that apply from this breakpoint and down
@mixin media-down($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: #{map-get($breakpoints, $breakpoint) - 1px}) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}. Available breakpoints: #{map-keys($breakpoints)}";
  }
}

// Media between - min-width: breakpoint1, max-width: breakpoint2 - 1px
// For styles that apply between two breakpoints
@mixin media-between($breakpoint1, $breakpoint2) {
  @if map-has-key($breakpoints, $breakpoint1) and map-has-key($breakpoints, $breakpoint2) {
    @media (min-width: #{map-get($breakpoints, $breakpoint1)}) and (max-width: #{map-get($breakpoints, $breakpoint2) - 1px}) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoints: #{$breakpoint1} or #{$breakpoint2}. Available breakpoints: #{map-keys($breakpoints)}";
  }
}

// Utility mixins
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}
