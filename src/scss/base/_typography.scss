body {
  font-family: $font-family-base;
  font-size: $font-size-base;
  margin: 0;
}

// Content typography
#content {
  h1, h2, h3, h4, h5, h6 {
    margin: $spacing-lg 0 $spacing-md;
  }

  h1:first-child {
    margin-top: 0;
  }

  p {
    font-size: $font-size-medium;
    line-height: $line-height-base;
  }

  ul {
    list-style-type: disc;
    margin-left: 1.5em;
    line-height: $line-height-base;

    li {
      font-size: $font-size-medium;
      margin-bottom: $spacing-md;
    }
  }
}

// Links
.active {
  text-decoration: none;
  color: $color-text;
  font-weight: bold;
}

// Year display
.year {
  color: $color-text-light;
  font-size: $font-size-small;
  margin-left: 4px;
}
