// Base layout
body {
  &.page-loading {
    overflow: hidden;
  }
}

// Container
#container {
  padding: $spacing-lg $spacing-lg $spacing-md $spacing-lg;
  display: flex;
  width: 900px;
  max-width: 100%;
  box-sizing: border-box;
  margin-top: 8px; /* Add space below the header */
}

// Content area
#content {
  max-width: 100%;
}

// Table cell specifics
td.name {
  white-space: normal;
}

// Form elements
td input {
  width: 3em;
}

td select {
  width: 10em;
}

// Beta notice
#beta {
  width: 100%;
  background-color: $color-primary;
  color: $color-background;
  font-weight: bold;
  padding: $spacing-md;
}

// Notices
#notices .new {
  color: $color-secondary;
  font-weight: bold;
}

// Disclosures
#disclosures {
  display: flex;
  margin: 0;
  padding: $spacing-xl 0 $spacing-lg;
}

label {
  cursor: pointer;
}