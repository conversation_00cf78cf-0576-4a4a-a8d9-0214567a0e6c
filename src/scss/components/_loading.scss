// Page loading overlay
.page-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: $color-background;
  z-index: 9999;
  @include flex-center;
  flex-direction: column;
}

// Spinner animation
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid $color-spinner;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Loading text
.loading-text {
  font-size: 16px;
  color: $color-text-light;
}
