// Button styles

// Show Results Button
.show-results-button {
  display: none;
  width: 100%;
  padding: 8px;
  background-color: $color-background-light;
  color: $color-text;
  border: 1px solid $color-border;
  font-weight: normal;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  margin: 0;
  margin-top: 3px;
  margin-bottom: 10px;

  &:hover {
    background-color: darken($color-background-light, 3%);
    border-color: $color-border;
  }

  &.close-filters {
    background-color: $color-background-light;
    color: $color-text;
    border-color: $color-border;

    &:hover {
      background-color: darken($color-background-light, 2%);
      border-color: $color-border;
    }
  }
}

// Mobile buttons are defined in the mobile-header component
