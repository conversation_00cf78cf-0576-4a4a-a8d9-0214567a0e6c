#filters {
  float: left;
  padding: $spacing-md;

  // Socket options grid layout (moved from _grid.scss)
  .socket-options label {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 4px;
  }

  // Mobile search container
  .mobile-search-container {
    display: none; // Hidden by default, shown only on mobile
    position: relative;
    width: 100%;
    margin-top: $spacing-md;

    input {
      width: 100%;
      padding: 4px 28px 4px 8px;
      border: 1px solid $color-border-dark;
      height: 32px;
      box-sizing: border-box;
      outline: none;

      &:focus {
        border-color: $color-text;
      }
    }

    .search-clear-button {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      padding: 0;
      width: 16px;
      height: 16px;
      cursor: pointer;
      display: none; /* Hidden by default */
      color: $color-text-muted;
      line-height: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: $color-text;
      }

      svg {
        display: block;
      }
    }
  }

  // Benchmark and cores filters
  .benchmark, .cores {
    width: 11em;

    &-container {
      display: flex;
      justify-content: space-between;
      gap: 5px;
    }

    .input-group {
      display: flex;
      flex-direction: column;
      width: calc(50% - 4px);
    }

    input {
      width: 100%;
      margin-bottom: 2px;
      padding: 2px 4px;
      box-sizing: border-box;
    }

    label {
      font-size: $font-size-small;
      text-align: center;
    }
  }

  // Search input
  .search input {
    width: 100%;
    padding: 5px;
    box-sizing: border-box;
    margin-bottom: 5px;
  }

  // Legend
  legend {
    font-weight: bold;
    padding-left: 0;
  }
}

/* Responsive styles for filters (moved from _responsive.scss) */
@include media-down('xl') {
  /* Hide filters by default on tablet */
  #filters {
    display: none;
  }

  /* Styles for fixed position filters on tablet */
  #filters.fixed-position {
    display: flex !important;
    flex-wrap: wrap;
    align-items: flex-start;
    position: fixed;
    top: 41px;
    left: 0;
    right: 0;
    z-index: 200;
    overflow: auto;
    background: $color-background;
    padding: 4px 8px 0;
    height: auto;
    border-bottom: 1px solid $color-border-light;
  }

  /* Mobile search container takes full width */
  #filters.fixed-position .mobile-search-container {
    width: 100%;
    flex-basis: 100%;
    margin-bottom: $spacing-md;
  }

  /* Show the results button inside the filters when they're visible */
  .show-results-button {
    display: none; /* Hide by default */
  }

  #filters.fixed-position .show-results-button {
    display: block;
  }

  /* Base styles for all fieldsets */
  #filters fieldset {
    display: flex;
    flex-direction: column;
    margin: $spacing-md 0;
    box-sizing: border-box;
  }

  /* Intel Desktop (1/4 width) */
  #filters .socket-group-desktop-intel {
    width: calc(25% - 0.375em);
    margin-right: $spacing-md;
    min-height: 250px;
  }

  /* AMD Desktop (1/4 width) */
  #filters .socket-group-desktop-amd {
    width: calc(25% - 0.375em);
    margin-right: $spacing-md;
    min-height: 250px;
  }

  /* Intel Server (1/4 width) */
  #filters .socket-group-server-intel {
    width: calc(25% - 0.375em);
    margin-right: $spacing-md;
    min-height: 250px;
  }

  /* AMD Server (1/4 width) */
  #filters .socket-group-server-amd {
    width: calc(25% - 0.375em);
    margin-right: 0;
    min-height: 250px;
  }
}

@include media-down('lg') {
  /* Intel Desktop (1/2 width) */
  #filters .socket-group-desktop-intel {
    width: calc(50% - 0.25em);
    margin-right: $spacing-md;
    min-height: 185px; /* Use min-height instead of height */
    display: flex;
    flex-direction: column;
  }

  /* AMD Desktop (1/2 width) */
  #filters .socket-group-desktop-amd {
    width: calc(50% - 0.25em);
    margin-right: 0;
    min-height: 185px; /* Use min-height instead of height */
    display: flex;
    flex-direction: column;
  }

  /* Intel Server (1/2 width) */
  #filters .socket-group-server-intel {
    width: calc(50% - 0.25em);
    margin-right: $spacing-md;
    min-height: 185px; /* Use min-height instead of height */
    margin-top: 0;
    display: flex;
    flex-direction: column;
  }

  /* AMD Server (1/2 width) */
  #filters .socket-group-server-amd {
    width: calc(50% - 0.25em);
    margin-right: 0;
    min-height: 185px; /* Use min-height instead of height */
    margin-top: 0;
    display: flex;
    flex-direction: column;
  }
}

@include media-down('xs') {
  #filters .socket-group .year {
    display: none;
  }
}
