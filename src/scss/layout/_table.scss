#cpuprices {
  display: flex;
  flex-direction: column;
  padding: $spacing-md;

  // Table header column widths
  &-head {
    th {
      &[data-sort="value"] {
        width: 40px;
      }

      &[data-sort="price"] {
        width: 38px;
      }

      &[data-sort="mark"] {
        width: 38px;
      }

      &[data-sort="cores"] {
        width: 40px;
      }

      &[data-sort="tdp"] {
        width: 35px;
      }

      &[data-sort="socket"] {
        width: 67px;
      }

      &[data-sort="type"] {
        width: 20px;
      }

      &[data-sort="condition"] {
        width: 38px;
      }

      &[data-sort="name"] {
        padding-right: 0;
      }
    }
  }

  // Sort indicators
  th.sortable {
    cursor: pointer;
    position: relative;
    padding-right: 20px; // Space for the sort icon

    &.sort-asc::after {
      content: '↑'; // Ascending sort icon
      position: absolute;
      right: 5px;
      color: $color-text;
      font-size: 14px;
    }

    &.sort-desc::after {
      content: '↓'; // Descending sort icon
      position: absolute;
      right: 5px;
      color: $color-text;
      font-size: 14px;
    }
  }
}

/* Card layout for mobile devices (moved from _cards.scss) */
@include media-down('lg') {
  /* Hide the table header on mobile */
  #cpuprices table thead {
    display: none;
  }

  /* Convert table rows to cards */
  #cpuprices table tbody tr {
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid $color-border-light;
    font-size: 13px;
    line-height: 1.3;
    padding: 6px 0;

    &:last-child {
      border-bottom: none;
    }

    &:first-child {
      padding-top: 0;
    }
  }

  /* Hide all cells by default */
  #cpuprices table tbody td {
    display: inline;
    border: none;
    white-space: normal;
    padding: 0;
    margin: 0;
    order: 2;
  }

  // #cpuprices table tbody td[data-label="Value"]::before {
  //   content: "🌟 ";
  // }

  #cpuprices table tbody td[data-label="Value"]::after {
    content: " • ";
    padding-right: 4px;
  }

  #cpuprices table tbody td[data-label="Price"]::before {
    content: "💰 ";
  }

  #cpuprices table tbody td[data-label="Price"]::after {
    content: " • ";
    padding-right: 4px;
  }

  #cpuprices table tbody td[data-label="Mark"]::before {
    content: "📊 ";
  }

  #cpuprices table tbody td[data-label="Mark"]::after {
    content: " • ";
    padding-right: 4px;
  }

  #cpuprices table tbody td[data-label="Cores"]::before {
    content: "🔲 ";
    font-size: 11px;
    position: relative;
    top: -.5px;
    padding-right: 2px;
  }

  #cpuprices table tbody td[data-label="Cores"]::after {
    content: " • ";
    padding-right: 4px;
  }

  #cpuprices table tbody td[data-label="TDP"]::after {
    content: " • ";
    padding-right: 4px;
  }

  #cpuprices table tbody td[data-label="Socket"]::after {
    content: " • ";
    padding-right: 4px;
  }

  #cpuprices table tbody td[data-label="Type"] {
    display: none;
  }

  #cpuprices table tbody td[data-label="Type"]::after {
    content: " • ";
    padding-right: 2px;
  }

  /* Style the affiliate link */
  #cpuprices table tbody td.name {
    display: block;
    line-height: 1.3;
    padding: 0;
    order: 1;
    flex-basis: 100%;
    margin-bottom: 2px;
  }

  /* Keep default link styling */
  #cpuprices table tbody td.name a {
    display: block;
    font-weight: 500;
  }

  /* Remove max-width constraints */
  #cpuprices td.name {
    max-width: none;
  }
}
