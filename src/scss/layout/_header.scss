/* Header styles - visible on all screen sizes */
#header {
  display: block;
  background-color: $color-background-light;
  border-bottom: 1px solid $color-border-light;
  padding: 4px 8px;
}

/* Main header row with buttons */
.header-main {
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 32px;
}

/* Site icon/home link */
.site-icon {
  display: inline-flex; /* Change to inline-flex */
  align-items: center;
  justify-content: center;
  width: 32px; /* Fixed width */
  height: 32px; /* Fixed height */
  flex-shrink: 0; /* Prevent shrinking */

  img, svg {
    width: 20px;
    height: 20px;
    padding: 5px;
    border: 1px solid $color-border;
    border-radius: 4px;
    background-color: $color-background-light;
  }
}

/* Hide tablet-only elements on desktop */
.tablet-only,
#filters-toggle {
  display: none;
}

/* Hide panels by default */
.header-panel {
  display: none;
}

/* Header search input */
.header-search-container {
  flex-grow: 1;
  display: flex;
  max-width: 220px;
  position: relative; /* Added for absolute positioning of clear button */
}

#header-search {
  width: 100%;
  padding: 4px 28px 4px 8px; /* Increased right padding to make room for clear button */
  border: 1px solid $color-border;
  border-radius: $border-radius;
  height: 32px;
  box-sizing: border-box;
  outline: none;

  &:focus {
    border-color: $color-text;
  }
}

/* Clear search button */
.search-clear-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0;
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: none; /* Hidden by default */
  color: $color-text-muted;
  line-height: 0; /* Add this to help with SVG centering */
  display: flex; /* Add this */
  align-items: center; /* Add this */
  justify-content: center; /* Add this */

  &:hover {
    color: $color-text;
  }

  svg {
    display: block; /* Add this */
  }
}

/* Source button */
.source-button {
  @include flex-center;
  background: $color-background;
  border: 1px solid $color-border;
  padding: 4px 8px;
  font-size: 13px;
  cursor: pointer;
  border-radius: $border-radius;
  height: 32px;
  box-sizing: border-box;
  user-select: none;
  font-weight: bold;
  flex-shrink: 0; /* Prevent shrinking */
  -webkit-appearance: none; /* Reset iOS styles */
  appearance: none;

  svg {
    margin-left: 4px;
    width: 12px;
    height: 12px;
    flex-shrink: 0; /* Prevent icon shrinking */
    fill: currentColor; /* Use current text color */
    stroke: currentColor; /* Use current text color */
    color: $color-text; /* Set explicit color */
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on iOS */
  }
}

/* Header buttons */
.header-button {
  position: relative; /* Add this to position the indicator */
  background: none;
  border: 1px solid $color-border;
  width: 32px;
  height: 32px;
  padding: 0; /* Remove padding */
  @include flex-center;
  cursor: pointer;
  border-radius: $border-radius;
  flex-shrink: 0; /* Prevent shrinking */
  box-sizing: border-box;
  -webkit-appearance: none; /* Reset iOS styles */
  appearance: none;

  svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0; /* Prevent icon shrinking */
    fill: currentColor; /* Use current text color */
    stroke: currentColor; /* Use current text color */
    color: $color-text; /* Set explicit color */
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on iOS */
  }

  /* Filter indicator */
  .filter-indicator {
    display: none; /* Hidden by default */
    position: absolute;
    bottom: -3px;
    right: -3px;
    width: 8px;  /* Slightly smaller for dot style */
    height: 8px;
    background-color: #007bff;
    border-radius: 50%;
    border: 1.5px solid $color-background-light;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  &.has-active-filters .filter-indicator {
    display: block;
  }
}

/* Header condition filter */
.header-condition-filter {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 32px; /* Match other elements height */
  box-sizing: border-box;

  /* Legend styling */
  .header-condition-legend {
    font-size: 12px;
    color: $color-text; // Changed from $color-text-muted to ensure sufficient contrast
    line-height: 1;
    font-weight: bold;
    margin: 0 0 2px 0;
  }

  /* Container for the checkboxes */
  .header-condition-options {
    display: flex;
    gap: 4px;
    line-height: 1;

    label {
      display: flex;
      align-items: center;
      font-size: 13px;
      white-space: nowrap;
      height: 16px; /* Fixed height */

      input {
        margin: 0 4px 0 0; /* Reset margins */
        height: 13px; /* Fixed height for checkboxes */
        width: 13px;
      }
    }
  }
}

/* Right-aligned buttons container */
.header-right-buttons {
  display: flex;
  margin-left: auto;
}

/* Panel styles */
.header-panel {
  background-color: $color-background;
  border: 1px solid $color-border;
  border-radius: $border-radius;
  margin-top: 4px;
  box-shadow: $box-shadow;
  max-width: 300px;
  position: absolute;
  z-index: 101;
  overflow-y: auto;
  max-height: calc(100vh - 40px);
}

/* Position panels */
#source-panel {
  left: 48px;
  min-width: 120px;
}

#search-panel {
  left: 50%;
  transform: translateX(-50%);
}

#menu-panel {
  right: 8px;
}

/* Source list */
.source-list {
  list-style: none;
  padding: 0;
  margin: 0;

  a {
    padding: 3px 8px;
    display: block;
    text-decoration: none;
    color: $color-text;
    font-weight: normal;

    &.active {
        background-color: $color-background-blue;
        color: $color-background;
    }
  }
}

/* Menu list */
.menu-list {
  list-style: none;
  padding: 0;
  min-width: 120px;

  a {
    display: block;
    padding: 4px 8px;
    text-decoration: none;
    color: $color-text;
    font-weight: normal;

    &.active {
      background-color: $color-background-blue;
      color: $color-background;
    }
  }
}

/* Show active panel */
.header-panel.active {
  display: block;
}

/* Overlay for filters - only on tablet and mobile */
.body-overlay {
  display: none; /* Hidden by default */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99; /* Just below the header but above everything else */
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Only show overlay on tablet and smaller screens */
@include media-down('xl') {
  /* Show overlay when filters are active */
  body.filters-active .body-overlay {
    display: block;
    opacity: 1;
  }
}

/* Tablet-specific adjustments */
@include media-down('xl') {
  /* Show tablet-only elements and filters toggle on tablet and smaller */
  .tablet-only,
  #filters-toggle {
    display: flex;
  }

  /* Hide desktop filters on tablet */
  #filters {
    display: none;
  }

  /* Hide specific filter sections on mobile/tablet */
  #filters #locales,
  #filters #links,
  #filters .last,
  #filters .search {
    display: none;
  }

  /* Tablet-specific container adjustments */
  #container {
    width: 100%;
    max-width: 100%;
    padding: $spacing-md;
    margin-top: 8px;
  }

  /* Ensure condition filter is visible on all device sizes */
  .header-condition-filter {
    display: flex;
  }

  /* Make header fixed (moved from _responsive.scss) */
  #header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }

  body {
    padding-top: 40px; /* Match the header height */
  }
}

/* Mobile-specific adjustments */
@include media-down('md') {
  /* Adjust header layout for small screens */
  .header-main {
    flex-wrap: wrap;
  }

  /* Hide header search */
  .header-search-container {
    display: none;
  }

  /* Show mobile search in filters when filters are visible */
  #filters.fixed-position .mobile-search-container {
    display: block;
  }
}

/* Small screen adjustments (moved from _responsive.scss) */
@include media-down('sm') {
  .header-right-buttons {
    display: none;
  }
}
