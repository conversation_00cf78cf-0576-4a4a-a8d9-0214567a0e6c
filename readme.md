# CPU Prices Backend

Backend API and admin interface for CPU Prices website. This application manages CPU data, including models, sockets, and custom rules for data processing.

## Project Overview

The CPU Prices Backend provides:

- Admin interface for managing CPU data
- API endpoints for accessing CPU information
- Database storage using SQLite
- CPU data processing and categorization
- Socket data management
- Custom rules for CPU data modification

## Project Structure

- `/api` - API endpoints for external access
- `/app` - Application code (Services, Repositories, Utils)
- `/assets` - CSS, JavaScript, and other static assets
- `/config` - Configuration files
- `/data` - Database and generated data files
- `/inc` - Core functionality and helper functions
- `/scripts` - CLI utility scripts
- `/vendor` - Composer dependencies

## Setup

1. Clone the repository
2. Copy `.env.example` to `.env` and update the values
3. Set up a web server (Apache, Nginx, etc.) pointing to the project directory
4. Run the database setup script: `php scripts/setup_database.php`
5. Generate socket data: `php scripts/generate_sockets.php`
6. Visit the admin interface in your browser

## Database Setup

The application uses SQLite for data storage. To set up the database:

```bash
php scripts/setup_database.php
```

To force recreation of tables:

```bash
php scripts/setup_database.php --force
```

## Socket Data Management

To seed socket data into the database and generate JSON for frontend use:

```bash
php scripts/generate_sockets.php
```

To force reseeding of socket data:

```bash
php scripts/generate_sockets.php --force
```

## Custom Rules Setup

Custom rules allow modifying CPU data based on specific patterns:

```bash
php scripts/setup_custom_rules.php
```

To force recreation of the custom rules table:

```bash
php scripts/setup_custom_rules.php --force
```

## Password Management

To generate a secure hash for the admin password:

```bash
php scripts/generate_hash.php
```

## Environment Variables

The application uses environment variables stored in a `.env` file for configuration. Here's what each variable does:

- `APP_ENV`: Application environment (development, production)
- `ENABLE_CSRF_VALIDATION`: Whether to enable CSRF token validation (true/false)
- `ADMIN_USERNAME`: Admin username for login
- `ADMIN_PASSWORD`: Admin password for login (can be hashed)
- `PASSWORD_IS_HASHED`: Whether the password is stored as a hash (true/false)
- `SESSION_LIFETIME`: Session lifetime in seconds
- `AMAZON_ACCESS_KEY`: Amazon access key for product data
- `AMAZON_SECRET_KEY`: Amazon secret key for product data
- `AMAZON_PARTNER_TAG`: Amazon partner tag for product data
- `AMAZON_HOST`: Amazon host for product data
- `AMAZON_REGION`: Amazon region for product data

## Development

For development, you can set `ENABLE_CSRF_VALIDATION=false` to disable CSRF validation, making it easier to test API endpoints.

In production, always set `ENABLE_CSRF_VALIDATION=true` for security.

## API Endpoints

### Socket Management

- `GET /api/sockets.php?action=list`: Get all sockets
- `GET /api/sockets.php?action=get&id={id}`: Get a specific socket
- `POST /api/sockets.php?action=add`: Add a new socket
- `PUT /api/sockets.php?action=update&id={id}`: Update a socket
- `DELETE /api/sockets.php?action=delete&id={id}`: Delete a socket
- `POST /api/sockets.php?action=add_variant`: Add a socket variant
- `PUT /api/sockets.php?action=update_variant&id={id}`: Update a socket variant
- `DELETE /api/sockets.php?action=delete_variant&id={id}`: Delete a socket variant

### CPU Data Management

- `GET /api/cpus.php?action=list`: Get all CPU models
- `GET /api/cpus.php?action=get&id={id}`: Get a specific CPU model
- `POST /api/cpus.php?action=update`: Update CPU model details
- `POST /api/cpus.php?action=whitelist`: Update whitelist status for CPU models
- `POST /api/cpus.php?action=import`: Import CPU data from external sources

### Custom Rules

- `GET /api/rules.php?action=list`: Get all custom rules
- `POST /api/rules.php?action=add`: Add a new custom rule
- `PUT /api/rules.php?action=update&id={id}`: Update a custom rule
- `DELETE /api/rules.php?action=delete&id={id}`: Delete a custom rule
- `POST /api/rules.php?action=apply`: Apply custom rules to CPU data

## Security

- All API endpoints require authentication
- CSRF protection is enabled for all non-GET requests in production
- Session timeout is configured in the `.env` file
- Password can be stored as a secure hash

## CPU Data Processing

The application processes CPU data with the following features:

- Categorization (desktop, server, laptop, mobile, other)
- Automatic whitelisting based on category (desktop and server by default)
- Tagging with brand, series, and model information
- Custom rules for modifying CPU data
- Socket matching for compatibility information
