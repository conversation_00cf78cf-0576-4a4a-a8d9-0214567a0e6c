
# CPU Prices

A web-based tool that compares CPU prices from various retailers on Amazon, sorted by CPU Mark per price. This helps users find the best value CPUs for their needs.

## Overview

This project scrapes CPU data from cpubenchmark.net and combines it with pricing information to calculate the best value processors. The site displays CPUs sorted by CPU Mark per price, making it easy to identify which processors offer the best performance for your money.

## Technical Details

- Frontend: HTML, CSS, JavaScript
- Backend: PHP for data fetching and processing
- Data sources: cpubenchmark.net for CPU Mark data, Amazon for pricing information, Amazon Product Advertising API for affiliate links
