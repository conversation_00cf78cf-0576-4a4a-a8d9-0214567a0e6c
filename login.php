<?php
/**
 * CPU Prices Backend - Login Page
 * Secure implementation with session management, CSRF protection, and rate limiting
 */

// Define secure access constant to protect included files
define('SECURE_ACCESS', true);

// Include configuration file
require_once 'config/config.php';

// Include authentication and session management files
require_once 'inc/auth.php';
require_once 'inc/session.php';
require_once 'inc/login-handler.php';

// Check for session expiration parameter
$session_expired = isset($_GET['expired']) && $_GET['expired'] === '1';

// If this is a session expiration, force logout
if ($session_expired) {
    // Clear session
    session_unset();
    session_destroy();

    // Start a new session
    session_start();
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    $csrf_token = $_SESSION['csrf_token'];

    // Set error message
    $error_message = 'Your session has expired. Please log in again.';

    // Remove the expired parameter from the URL using JavaScript
    echo "<script>
        if (window.history.replaceState) {
            // Remove the expired parameter
            var newUrl = window.location.href.split('?')[0];
            window.history.replaceState({}, document.title, newUrl);
        }
    </script>";
}

// If user is already logged in, redirect to index.php
if ($is_logged_in) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <title>CPU Prices - Login</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/png" href="/assets/favicon/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/assets/favicon/favicon.svg" />
    <link rel="shortcut icon" href="/assets/favicon/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="CPU Prices" />
    <link rel="manifest" href="/assets/favicon/site.webmanifest" />
    <meta name="csrf-token" content="<?php echo htmlspecialchars($csrf_token); ?>" />
    <link rel="stylesheet" href="/assets/css/admin.css?v=<?php echo filemtime($_SERVER['DOCUMENT_ROOT'] . '/assets/css/admin.css'); ?>" />
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <a href="/" class="logo-container">
                    <div class="logo">
                        <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="32px" height="32px" viewBox="0,0,256,256"><g transform="translate(-43.52,-43.52) scale(1.34,1.34)"><g fill="none" fill-rule="nonzero" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none" font-size="none" text-anchor="none" style="mix-blend-mode: normal"><g transform="scale(5.33333,5.33333)"><path d="M6,8v32c0,1.1 0.9,2 2,2h32c1.1,0 2,-0.9 2,-2v-32c0,-1.1 -0.9,-2 -2,-2h-32c-1.1,0 -2,0.9 -2,2z" fill="#43a047"></path><path d="M15,10c0,0.6 -0.4,1 -1,1c-0.6,0 -1,-0.4 -1,-1c0,-0.6 0.4,-1 1,-1c0.6,0 1,0.4 1,1zM18,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM18,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM18,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM18,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1z" fill="#ffeb3b"></path><path d="M29,30h-10c-0.5,0 -1,-0.5 -1,-1v-10c0,-0.5 0.5,-1 1,-1h10c0.5,0 1,0.5 1,1v10c0,0.5 -0.5,1 -1,1z" fill="#37474f"></path></g></g></g></svg>
                    </div>
                    <h1>CPU Prices - Backend</h1>
                </a>
            </div>
        </div>

        <div class="content">
            <div class="login-form">
                <h2 class="form-title">Admin Login</h2>

                <?php if ($error_message): ?>
                    <div class="error"><?php echo htmlspecialchars($error_message); ?></div>
                <?php endif; ?>

                <form method="post" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">

                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" required autocomplete="username">
                    </div>

                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" required autocomplete="current-password">
                    </div>

                    <button type="submit" name="login">Login</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
