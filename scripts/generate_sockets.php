<?php
/**
 * Socket Data Generation Script
 * 
 * This script:
 * 1. Seeds socket data into the database
 * 2. Generates a JSON file with socket data for use in the frontend
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', '1');

// Define secure access constant
define('SECURE_ACCESS', true);

// Include required files
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../inc/database.php';

// Configuration
$projectRoot = dirname(__DIR__);
$outputDir = $projectRoot . '/data/generated';
$outputFile = $outputDir . '/sockets.json';

echo "Socket Data Generation Script\n";
echo "----------------------------\n";
echo "Output file: {$outputFile}\n";

/**
 * Seed socket data into the database
 * 
 * @param PDO $pdo Database connection
 * @param bool $force Whether to force re-seeding even if data exists
 * @return array Result with success status and message
 */
function seedSocketData($pdo, $force = false) {
    try {
        // Check if sockets already exist
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM sockets");
        $count = $stmt->fetch()['count'];
        
        if ($count > 0 && !$force) {
            return [
                'success' => true,
                'message' => "Sockets already seeded ({$count} sockets found). Use force=true to reseed.",
                'count' => $count
            ];
        }
        
        // If force is true, clear existing data
        if ($force) {
            $pdo->exec("DELETE FROM socket_variants");
            $pdo->exec("DELETE FROM sockets");
            echo "Cleared existing socket data for reseeding\n";
        }
        
        // Socket data array
        $socketData = [
            // Intel Desktop
            ["name" => "LGA 1851", "slug" => "lga1851", "year" => 2024, "variants" => ["FCLGA1851", "FCLGA 1851"], "manufacturer" => "Intel", "type" => "desktop"],
            ["name" => "LGA 1700", "slug" => "lga1700", "year" => 2021, "variants" => ["LGA1700", "LGA 1700", "BGA1700", "BGA 1700", "LGA1700/BGA1700", "FCLGA1700", "FCLGA 1700"], "manufacturer" => "Intel", "type" => "desktop"],
            ["name" => "LGA 1200", "slug" => "lga1200", "year" => 2020, "variants" => ["LGA1200", "LGA 1200", "FCLGA1200", "FCLGA 1200"], "manufacturer" => "Intel", "type" => "desktop"],
            ["name" => "LGA 1151", "slug" => "lga1151", "year" => 2015, "variants" => ["LGA1151", "LGA 1151", "FCLGA1151", "FCLGA 1151"], "manufacturer" => "Intel", "type" => "desktop"],
            ["name" => "LGA 1150", "slug" => "lga1150", "year" => 2013, "variants" => ["LGA1150", "LGA 1150", "FCLGA1150", "FCLGA 1150"], "manufacturer" => "Intel", "type" => "desktop"],
            ["name" => "LGA 1155", "slug" => "lga1155", "year" => 2011, "variants" => ["LGA1155", "LGA 1155", "FCLGA1155", "FCLGA 1155"], "manufacturer" => "Intel", "type" => "desktop"],
            ["name" => "LGA 1156", "slug" => "lga1156", "year" => 2009, "variants" => ["LGA1156", "LGA 1156", "FCLGA1156", "FCLGA 1156"], "manufacturer" => "Intel", "type" => "desktop"],
            ["name" => "LGA 775", "slug" => "lga775", "year" => 2004, "variants" => ["LGA775", "LGA 775", "Socket T", "FCLGA775", "FCLGA 775"], "manufacturer" => "Intel", "type" => "desktop"],
            ["name" => "LGA 771", "slug" => "lga771", "year" => 2006, "variants" => ["LGA771", "LGA 771", "Socket J", "FCLGA771", "FCLGA 771"], "manufacturer" => "Intel", "type" => "desktop"],
            
            // Intel Server
            ["name" => "LGA 4677", "slug" => "lga4677", "year" => 2022, "variants" => ["LGA4677", "LGA 4677", "FCLGA4677", "FCLGA 4677"], "manufacturer" => "Intel", "type" => "server"],
            ["name" => "LGA 4189", "slug" => "lga4189", "year" => 2020, "variants" => ["LGA4189", "LGA 4189", "FCLGA4189", "FCLGA 4189"], "manufacturer" => "Intel", "type" => "server"],
            ["name" => "LGA 3647", "slug" => "lga3647", "year" => 2017, "variants" => ["LGA3647", "LGA 3647", "Socket P", "FCLGA3647", "FCLGA 3647"], "manufacturer" => "Intel", "type" => "server"],
            ["name" => "LGA 2066", "slug" => "lga2066", "year" => 2017, "variants" => ["LGA2066", "LGA 2066", "Socket R4", "FCLGA2066", "FCLGA 2066"], "manufacturer" => "Intel", "type" => "server"],
            ["name" => "LGA 2011-3", "slug" => "lga2011-3", "year" => 2014, "variants" => ["LGA2011-3", "LGA 2011-3", "Socket R3", "FCLGA2011-3", "FCLGA 2011-3"], "manufacturer" => "Intel", "type" => "server"],
            ["name" => "LGA 2011", "slug" => "lga2011", "year" => 2011, "variants" => ["LGA2011", "LGA 2011", "Socket R", "FCLGA2011", "FCLGA 2011"], "manufacturer" => "Intel", "type" => "server"],
            ["name" => "LGA 1366", "slug" => "lga1366", "year" => 2008, "variants" => ["LGA1366", "LGA 1366", "Socket B", "FCLGA1366", "FCLGA 1366"], "manufacturer" => "Intel", "type" => "server"],
            
            // AMD Desktop
            ["name" => "AM5", "slug" => "am5", "year" => 2022, "variants" => ["AM5", "Socket AM5"], "manufacturer" => "AMD", "type" => "desktop"],
            ["name" => "AM4", "slug" => "am4", "year" => 2016, "variants" => ["AM4", "Socket AM4"], "manufacturer" => "AMD", "type" => "desktop"],
            ["name" => "AM3+", "slug" => "am3plus", "year" => 2011, "variants" => ["AM3+", "Socket AM3+"], "manufacturer" => "AMD", "type" => "desktop"],
            ["name" => "AM3", "slug" => "am3", "year" => 2009, "variants" => ["AM3", "Socket AM3"], "manufacturer" => "AMD", "type" => "desktop"],
            ["name" => "AM2+", "slug" => "am2plus", "year" => 2007, "variants" => ["AM2+", "Socket AM2+"], "manufacturer" => "AMD", "type" => "desktop"],
            ["name" => "AM2", "slug" => "am2", "year" => 2006, "variants" => ["AM2", "Socket AM2"], "manufacturer" => "AMD", "type" => "desktop"],
            ["name" => "FM2+", "slug" => "fm2plus", "year" => 2014, "variants" => ["FM2+", "Socket FM2+"], "manufacturer" => "AMD", "type" => "desktop"],
            ["name" => "FM2", "slug" => "fm2", "year" => 2012, "variants" => ["FM2", "Socket FM2"], "manufacturer" => "AMD", "type" => "desktop"],
            ["name" => "FM1", "slug" => "fm1", "year" => 2011, "variants" => ["FM1", "Socket FM1"], "manufacturer" => "AMD", "type" => "desktop"],
            
            // AMD Server
            ["name" => "SP5", "slug" => "sp5", "year" => 2022, "variants" => ["SP5", "Socket SP5"], "manufacturer" => "AMD", "type" => "server"],
            ["name" => "SP3", "slug" => "sp3", "year" => 2017, "variants" => ["SP3", "Socket SP3"], "manufacturer" => "AMD", "type" => "server"],
            ["name" => "TR4", "slug" => "tr4", "year" => 2017, "variants" => ["TR4", "Socket TR4", "sTR4"], "manufacturer" => "AMD", "type" => "server"],
            ["name" => "sTRX4", "slug" => "strx4", "year" => 2019, "variants" => ["sTRX4", "Socket TRX4"], "manufacturer" => "AMD", "type" => "server"],
            ["name" => "sWRX8", "slug" => "swrx8", "year" => 2020, "variants" => ["sWRX8", "Socket WRX8"], "manufacturer" => "AMD", "type" => "server"]
        ];
        
        // Begin transaction
        $pdo->beginTransaction();
        
        // Prepare statements
        $socketStmt = $pdo->prepare("
            INSERT INTO sockets (name, slug, year, manufacturer, type)
            VALUES (:name, :slug, :year, :manufacturer, :type)
        ");
        
        $variantStmt = $pdo->prepare("
            INSERT INTO socket_variants (socket_id, variant_name)
            VALUES (:socket_id, :variant_name)
        ");
        
        $socketCount = 0;
        $variantCount = 0;
        
        // Insert socket data
        foreach ($socketData as $socket) {
            $socketStmt->execute([
                ':name' => $socket['name'],
                ':slug' => $socket['slug'],
                ':year' => $socket['year'],
                ':manufacturer' => $socket['manufacturer'],
                ':type' => $socket['type']
            ]);
            
            $socketId = $pdo->lastInsertId();
            $socketCount++;
            
            // Insert variants
            foreach ($socket['variants'] as $variant) {
                $variantStmt->execute([
                    ':socket_id' => $socketId,
                    ':variant_name' => $variant
                ]);
                $variantCount++;
            }
        }
        
        // Commit transaction
        $pdo->commit();
        
        return [
            'success' => true,
            'message' => "Successfully seeded {$socketCount} sockets with {$variantCount} variants",
            'socket_count' => $socketCount,
            'variant_count' => $variantCount
        ];
        
    } catch (PDOException $e) {
        // Roll back transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        $errorMsg = "Error seeding socket data: " . $e->getMessage();
        echo $errorMsg . "\n";
        
        return [
            'success' => false,
            'message' => $errorMsg
        ];
    }
}

/**
 * Generate socket data JSON file from database
 * 
 * @param PDO $db Database connection
 * @param string $outputFile Path to output file
 * @return array Result with success status and message
 */
function generateSocketsJson($db, $outputFile) {
    try {
        // Create output directory if it doesn't exist
        $outputDir = dirname($outputFile);
        if (!is_dir($outputDir)) {
            if (!mkdir($outputDir, 0755, true)) {
                return [
                    'success' => false,
                    'message' => "Failed to create output directory: {$outputDir}"
                ];
            }
        }
        
        // Initialize socket data structure
        $socketData = [
            'grouped' => [],
            'variants_map' => []
        ];
        
        // Get all sockets with their variants
        $query = "
            SELECT s.id, s.name, s.slug, s.year, s.manufacturer, s.type, sv.variant_name
            FROM sockets s
            LEFT JOIN socket_variants sv ON s.id = sv.socket_id
            ORDER BY s.manufacturer, s.year DESC, s.name
        ";
        
        $stmt = $db->query($query);
        $results = $stmt->fetchAll();
        
        // Process results
        $sockets = [];
        $socketVariants = [];
        
        foreach ($results as $row) {
            $socketId = $row['id'];
            
            // Add socket to array if not already added
            if (!isset($sockets[$socketId])) {
                $sockets[$socketId] = [
                    'id' => $socketId,
                    'name' => $row['name'],
                    'slug' => $row['slug'],
                    'year' => $row['year'],
                    'manufacturer' => $row['manufacturer'],
                    'type' => $row['type'],
                    'variants' => []
                ];
            }
            
            // Add variant to socket
            if (!empty($row['variant_name'])) {
                $sockets[$socketId]['variants'][] = $row['variant_name'];
                
                // Add to variants map
                $socketVariants[$row['variant_name']] = [
                    'name' => $row['name'],
                    'slug' => $row['slug']
                ];
            }
        }
        
        // Group sockets by type and manufacturer
        foreach ($sockets as $socket) {
            $type = $socket['type'];
            $manufacturer = $socket['manufacturer'];
            
            if (!isset($socketData['grouped'][$type])) {
                $socketData['grouped'][$type] = [];
            }
            
            if (!isset($socketData['grouped'][$type][$manufacturer])) {
                $socketData['grouped'][$type][$manufacturer] = [];
            }
            
            $socketData['grouped'][$type][$manufacturer][] = $socket;
        }
        
        // Add variants map
        $socketData['variants_map'] = $socketVariants;
        
        // Write to file
        $jsonData = json_encode($socketData, JSON_PRETTY_PRINT);
        if (file_put_contents($outputFile, $jsonData) === false) {
            return [
                'success' => false,
                'message' => "Failed to write to output file: {$outputFile}"
            ];
        }
        
        return [
            'success' => true,
            'message' => "Successfully generated socket data JSON file",
            'socket_count' => count($sockets),
            'variant_count' => count($socketVariants),
            'file' => $outputFile
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Error generating socket data: " . $e->getMessage()
        ];
    }
}

// Process command line arguments
$force = false;
if (isset($argv)) {
    foreach ($argv as $arg) {
        if ($arg === '--force') {
            $force = true;
            echo "Force mode enabled. Will recreate socket data if it exists.\n";
        }
    }
}

try {
    // Get database connection
    $db = getDatabase();
    echo "Database connection established successfully.\n";
    
    // Seed socket data
    $seedResult = seedSocketData($db, $force);
    if ($seedResult['success']) {
        echo "Socket data: " . $seedResult['message'] . "\n";
    } else {
        echo "Error seeding socket data: " . $seedResult['message'] . "\n";
        exit(1);
    }
    
    // Generate socket data JSON
    $result = generateSocketsJson($db, $outputFile);
    if ($result['success']) {
        echo "Socket data generation: " . $result['message'] . "\n";
        echo "Processed {$result['socket_count']} sockets with {$result['variant_count']} variants.\n";
        echo "Output saved to: {$result['file']}\n";
    } else {
        echo "Error generating socket data: " . $result['message'] . "\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

exit(0);
