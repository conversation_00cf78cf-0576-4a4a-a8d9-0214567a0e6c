<?php
/**
 * Database Setup Script
 * 
 * This script initializes the database and creates all necessary tables.
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', '1');

// Define secure access constant
define('SECURE_ACCESS', true);

// Include required files
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../inc/database.php';

// Calculate the project root directory
$projectRoot = dirname(__DIR__);
$dbPath = $projectRoot . '/data/database.sqlite';

echo "Database Setup Script\n";
echo "---------------------\n";
echo "Project Root: " . $projectRoot . "\n";
echo "Database Path: " . $dbPath . "\n";

// Process command line arguments
$force = false;
if (isset($argv)) {
    foreach ($argv as $arg) {
        if ($arg === '--force') {
            $force = true;
            echo "Force mode enabled. Will recreate tables if they exist.\n";
        }
    }
}

try {
    // Get database connection (this will create the database if it doesn't exist)
    $db = getDatabase();
    echo "Database connection established successfully.\n";
    
    // If force is true, drop and recreate tables
    if ($force) {
        echo "Dropping existing tables...\n";
        $db->exec("DROP TABLE IF EXISTS socket_variants");
        $db->exec("DROP TABLE IF EXISTS sockets");
        $db->exec("DROP TABLE IF EXISTS custom_rules");
        $db->exec("DROP TABLE IF EXISTS cpu_models");
        echo "Tables dropped successfully.\n";
    }
    
    // Initialize database schema
    echo "Initializing database schema...\n";
    $result = initializeDatabaseSchema($db);
    
    if ($result) {
        echo "Database schema initialized successfully.\n";
    } else {
        echo "Error initializing database schema.\n";
        exit(1);
    }
    
    // Log database statistics
    echo "\nDatabase Statistics:\n";
    echo "------------------\n";
    
    // Check if tables exist and log stats
    $tables = ['sockets', 'socket_variants', 'custom_rules', 'cpu_models'];
    foreach ($tables as $table) {
        $stmt = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='{$table}'");
        $tableExists = $stmt->fetch() !== false;
        echo "{$table} table exists: " . ($tableExists ? 'Yes' : 'No') . "\n";
        
        if ($tableExists) {
            $stmt = $db->query("SELECT COUNT(*) as count FROM {$table}");
            $count = $stmt->fetch()['count'];
            echo "Number of records in {$table}: {$count}\n";
        }
    }
    
    echo "\nDatabase setup completed successfully.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

exit(0);
