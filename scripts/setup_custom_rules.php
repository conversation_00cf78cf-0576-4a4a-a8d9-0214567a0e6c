<?php
/**
 * Custom Rules Setup Script
 * 
 * This script sets up the custom rules table for modifying CPU data.
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', '1');

// Define secure access constant
define('SECURE_ACCESS', true);

// Include required files
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../inc/database.php';

// Configuration
$projectRoot = dirname(__DIR__);
$dbPath = $projectRoot . '/data/database.sqlite';

echo "Custom Rules Setup Script\n";
echo "------------------------\n";
echo "Database Path: " . $dbPath . "\n";
echo "This script sets up the custom rules table for modifying CPU data.\n";
echo "Custom rules can modify the following fields: socket, cat (Category), tdp, cpuCount, cores, secondaryCores\n";

// Process command line arguments
$force = false;
if (isset($argv)) {
    foreach ($argv as $arg) {
        if ($arg === '--force') {
            $force = true;
            echo "Force mode enabled. Will recreate custom rules table if it exists.\n";
        }
    }
}

try {
    // Get database connection
    $db = getDatabase();
    echo "Database connection established successfully.\n";
    
    // If force is true, drop and recreate custom_rules table
    if ($force) {
        echo "Dropping existing custom_rules table...\n";
        $db->exec("DROP TABLE IF EXISTS custom_rules");
        echo "Custom rules table dropped successfully.\n";
        
        // Create custom_rules table
        echo "Creating custom_rules table...\n";
        $db->exec("
            CREATE TABLE custom_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name_pattern TEXT NOT NULL,
                field_to_change TEXT NOT NULL,
                new_value TEXT NOT NULL,
                is_active INTEGER DEFAULT 1,
                match_type TEXT DEFAULT 'name',
                cpu_count INTEGER DEFAULT NULL,
                original_id TEXT DEFAULT NULL,
                action_type TEXT DEFAULT 'update',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        // Create indexes for custom_rules table
        echo "Creating indexes for custom_rules table...\n";
        $db->exec("CREATE INDEX idx_custom_rules_field_to_change ON custom_rules (field_to_change)");
        $db->exec("CREATE INDEX idx_custom_rules_is_active ON custom_rules (is_active)");
        $db->exec("CREATE INDEX idx_custom_rules_match_type ON custom_rules (match_type)");
        $db->exec("CREATE INDEX idx_custom_rules_cpu_count ON custom_rules (cpu_count)");
        $db->exec("CREATE INDEX idx_custom_rules_original_id ON custom_rules (original_id)");
        $db->exec("CREATE INDEX idx_custom_rules_action_type ON custom_rules (action_type)");
        
        // Create trigger for custom_rules table
        echo "Creating trigger for custom_rules table...\n";
        $db->exec("
            CREATE TRIGGER trigger_custom_rules_update_timestamp
            AFTER UPDATE ON custom_rules
            FOR EACH ROW
            BEGIN
                UPDATE custom_rules SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
            END
        ");
        
        echo "Custom rules table created successfully.\n";
    } else {
        // Check if custom_rules table exists
        $stmt = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='custom_rules'");
        $tableExists = $stmt->fetch() !== false;
        
        if ($tableExists) {
            echo "Custom rules table already exists.\n";
            
            // Count the number of rules
            $stmt = $db->query("SELECT COUNT(*) as count FROM custom_rules");
            $count = $stmt->fetch()['count'];
            echo "Number of custom rules in database: {$count}\n";
            
            // List active rules
            if ($count > 0) {
                echo "\nActive custom rules:\n";
                echo "-------------------\n";
                
                $stmt = $db->query("
                    SELECT id, name_pattern, field_to_change, new_value, match_type, 
                           cpu_count, original_id, action_type
                    FROM custom_rules 
                    WHERE is_active = 1
                    ORDER BY id ASC
                ");
                
                $rules = $stmt->fetchAll();
                
                foreach ($rules as $rule) {
                    echo "ID: {$rule['id']}\n";
                    echo "  Pattern: {$rule['name_pattern']}\n";
                    echo "  Field: {$rule['field_to_change']}\n";
                    echo "  New Value: {$rule['new_value']}\n";
                    echo "  Match Type: {$rule['match_type']}\n";
                    
                    if ($rule['match_type'] === 'name+cpu_count' || $rule['match_type'] === 'original_id+cpu_count') {
                        echo "  CPU Count: " . ($rule['cpu_count'] ?? 'NULL') . "\n";
                    }
                    
                    if ($rule['match_type'] === 'original_id' || $rule['match_type'] === 'original_id+cpu_count') {
                        echo "  Original ID: " . ($rule['original_id'] ?? 'NULL') . "\n";
                    }
                    
                    echo "  Action: {$rule['action_type']}\n";
                    echo "\n";
                }
            }
        } else {
            echo "Custom rules table does not exist. Run with --force to create it.\n";
            exit(1);
        }
    }
    
    echo "\nCustom rules setup completed successfully.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

exit(0);
