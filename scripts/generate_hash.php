<?php
/**
 * Password Hash Generator
 * 
 * This utility script helps you generate a secure hash for your password
 * when you want to switch from plaintext to hashed password in config.php
 * 
 * Usage:
 * 1. Run this script from the command line: php scripts/generate_hash.php
 * 2. Enter your plaintext password when prompted
 * 3. Copy the generated hash to config.php
 * 4. Set PASSWORD_IS_HASHED to true in config.php
 */

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    echo "This script should be run from the command line.";
    exit(1);
}

echo "Password Hash Generator\n";
echo "----------------------\n";
echo "This utility will generate a secure hash for your admin password.\n\n";

// Prompt for password
echo "Enter your plaintext password: ";
$password = trim(fgets(STDIN));

if (empty($password)) {
    echo "Error: Password cannot be empty.\n";
    exit(1);
}

// Generate hash
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "\nGenerated hash: " . $hash . "\n\n";
echo "Instructions:\n";
echo "1. Copy this hash to your config.php file\n";
echo "2. Set PASSWORD_IS_HASHED to true in config.php\n";
echo "3. Your config.php should look like this:\n\n";
echo "define('ADMIN_PASSWORD', '" . $hash . "');\n";
echo "define('PASSWORD_IS_HASHED', true);\n";

exit(0);
