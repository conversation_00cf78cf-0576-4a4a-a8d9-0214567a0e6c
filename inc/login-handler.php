<?php
/**
 * Login and logout request handler
 * Processes login form submissions and logout requests
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

// Initialize error message
$error_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $error_message = processLogin();
}

// Handle logout
if (isset($_GET['logout'])) {
    // Check if this is a redirect from session expiration
    if ($_GET['logout'] === '1' && !isset($_GET['csrf_token'])) {
        // This is a redirect from session expiration, just clear the session
        session_unset();
        session_destroy();

        // Start a new session for the login page
        session_start();
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $csrf_token = $_SESSION['csrf_token'];

        // Set an error message to inform the user
        $error_message = 'Your session has expired. Please log in again.';
    } else {
        // This is a normal logout request
        processLogout();
    }
}

// This section has been moved to login.php
