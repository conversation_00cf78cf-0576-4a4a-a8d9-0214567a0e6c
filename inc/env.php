<?php
/**
 * Environment Variables Loader
 * Loads environment variables from .env file
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Load environment variables from .env file
 * 
 * @return array Array of environment variables
 */
function loadEnv() {
    $envFile = __DIR__ . '/../.env';
    $env = [];

    if (file_exists($envFile)) {
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // Skip comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            // Parse line
            if (strpos($line, '=') !== false) {
                list($name, $value) = explode('=', $line, 2);
                $name = trim($name);
                $value = trim($value);
                
                // Remove quotes if present
                if (strpos($value, '"') === 0 && strrpos($value, '"') === strlen($value) - 1) {
                    $value = substr($value, 1, -1);
                } elseif (strpos($value, "'") === 0 && strrpos($value, "'") === strlen($value) - 1) {
                    $value = substr($value, 1, -1);
                }
                
                // Remove comments from value
                if (strpos($value, '#') !== false) {
                    $value = trim(explode('#', $value, 2)[0]);
                }
                
                // Convert specific string values to their appropriate types
                if ($value === 'true' || $value === 'TRUE') {
                    $value = true;
                } elseif ($value === 'false' || $value === 'FALSE') {
                    $value = false;
                } elseif ($value === 'null' || $value === 'NULL') {
                    $value = null;
                } elseif (is_numeric($value)) {
                    $value = $value * 1; // Convert to int or float
                }
                
                $env[$name] = $value;
                
                // Also set as environment variable
                if (!isset($_ENV[$name])) {
                    $_ENV[$name] = $value;
                }
                
                // Also set as server variable for compatibility
                if (!isset($_SERVER[$name])) {
                    $_SERVER[$name] = $value;
                }
            }
        }
    }
    
    return $env;
}

// Load environment variables
$env = loadEnv();

/**
 * Get environment variable value
 * 
 * @param string $key Variable name
 * @param mixed $default Default value if variable is not set
 * @return mixed Variable value or default
 */
function env($key, $default = null) {
    global $env;
    
    return isset($env[$key]) ? $env[$key] : $default;
}
