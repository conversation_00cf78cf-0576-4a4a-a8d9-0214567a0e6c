<?php
/**
 * Session initialization and management
 * Handles session setup, CSRF token generation, and timeout checks
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

// Configure session settings before starting
ini_set('session.cookie_httponly', 1); // Prevent JavaScript access to session cookie
ini_set('session.use_only_cookies', 1); // Force the use of cookies for session
ini_set('session.cookie_secure', isset($_SERVER['HTTPS'])); // Use secure cookies if HTTPS is enabled
ini_set('session.use_strict_mode', 1); // Only use valid session IDs generated by the server

// Start session with custom name from config
session_name(SESSION_NAME);

// Set session cookie parameters
$secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on'; // Only set secure in HTTPS
session_set_cookie_params(SESSION_LIFETIME, '/', '', $secure, true);

// Start the session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Generate CSRF token if not exists
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Store token in local variable for use in the page
$csrf_token = $_SESSION['csrf_token'];

// Check if user is logged in
$is_logged_in = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;

// Check for session timeout
if ($is_logged_in && isset($_SESSION['last_activity'])) {
    $inactive_time = time() - $_SESSION['last_activity'];
    $session_lifetime = defined('SESSION_LIFETIME') ? SESSION_LIFETIME : 3600; // Default to 1 hour

    if ($inactive_time > $session_lifetime) {
        // Session has expired
        session_unset();
        session_destroy();
        session_start();
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $csrf_token = $_SESSION['csrf_token'];
        $is_logged_in = false;
        $error_message = 'Your session has expired. Please log in again.';
    } else {
        // Update last activity time
        $_SESSION['last_activity'] = time();
    }
}
