<?php
/**
 * Authentication functions
 * Handles login, logout, and password verification
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

// Function to verify password - handles both hashed and plaintext passwords
function verifyPassword($inputPassword, $storedPassword) {
    // If the password in config is already hashed
    if (defined('PASSWORD_IS_HASHED') && PASSWORD_IS_HASHED) {
        return password_verify($inputPassword, $storedPassword);
    }
    // If the password in config is plaintext
    else {
        // Simple string comparison for plaintext password
        return $inputPassword === $storedPassword;
    }
}

// Handle login attempts rate limiting
function checkLoginAttempts() {
    // Initialize login attempts tracking
    if (!isset($_SESSION['login_attempts'])) {
        $_SESSION['login_attempts'] = 0;
        $_SESSION['last_login_attempt'] = 0;
    }

    // Check if we need to reset the counter after timeout
    if ($_SESSION['login_attempts'] >= MAX_LOGIN_ATTEMPTS) {
        $time_passed = time() - $_SESSION['last_login_attempt'];
        if ($time_passed < LOGIN_TIMEOUT) {
            $time_left = LOGIN_TIMEOUT - $time_passed;
            return "Too many failed login attempts. Please try again in " . ceil($time_left / 60) . " minutes.";
        } else {
            // Reset counter after timeout
            $_SESSION['login_attempts'] = 0;
        }
    }

    return '';
}

// Process login form submission
function processLogin() {
    global $error_message, $csrf_token;

    // Check for rate limiting
    $rate_limit_message = checkLoginAttempts();
    if ($rate_limit_message) {
        return $rate_limit_message;
    }

    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        return 'Security validation failed. Please try again.';
    }

    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    // Verify credentials
    if ($username === ADMIN_USERNAME && verifyPassword($password, ADMIN_PASSWORD)) {
        // Reset login attempts on successful login
        $_SESSION['login_attempts'] = 0;

        // Regenerate session ID to prevent session fixation
        session_regenerate_id(true);

        // Set session variables
        $_SESSION['logged_in'] = true;
        $_SESSION['username'] = $username;
        $_SESSION['last_activity'] = time();

        // Session expiration handling has been moved to login.php

        // Redirect to index.php after successful login
        header('Location: index.php');
        exit;
    } else {
        // Increment failed login attempts
        $_SESSION['login_attempts']++;
        $_SESSION['last_login_attempt'] = time();

        return 'Invalid username or password';
    }
}

// Process logout request
function processLogout() {
    global $csrf_token;

    if (isset($_GET['csrf_token']) && $_GET['csrf_token'] === $_SESSION['csrf_token']) {
        // Clear session variables
        session_unset();
        session_destroy();

        // Start a new session for the login page
        session_start();
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $csrf_token = $_SESSION['csrf_token'];

        // Redirect to login page
        header('Location: login.php');
        exit;
    }

    return 'Invalid logout request';
}

/**
 * Check if user is authenticated
 *
 * @return bool True if user is authenticated, false otherwise
 */
function isAuthenticated() {
    // Check if user is logged in
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        return false;
    }

    // Check for session timeout
    if (isset($_SESSION['last_activity'])) {
        $inactive_time = time() - $_SESSION['last_activity'];
        $session_lifetime = defined('SESSION_LIFETIME') ? SESSION_LIFETIME : 3600; // Default to 1 hour

        if ($inactive_time > $session_lifetime) {
            // Session has expired
            return false;
        }

        // Update last activity time
        $_SESSION['last_activity'] = time();
    } else {
        // No last activity timestamp
        return false;
    }

    return true;
}
