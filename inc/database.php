<?php
/**
 * Database Connection Functions
 * Handles database connections and initialization
 *
 * This file contains all database-related functionality:
 * - Database connection
 * - Table creation and schema management
 * - Database initialization
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Initialize database schema
 * Creates all necessary tables and indexes if they don't exist
 *
 * @param PDO $pdo Database connection
 * @return bool True if successful
 */
function initializeDatabaseSchema($pdo) {
    try {
        error_log("Initializing database schema");

        // Create sockets table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS sockets (
                id           INTEGER PRIMARY KEY AUTOINCREMENT,
                name         TEXT NOT NULL UNIQUE,
                slug         TEXT NOT NULL UNIQUE,
                year         INTEGER NOT NULL,
                manufacturer TEXT NOT NULL CHECK(manufacturer IN ('Intel', 'AMD')),
                type         TEXT NOT NULL CHECK(type IN ('desktop', 'server')),
                created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // Create socket_variants table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS socket_variants (
                id            INTEGER PRIMARY KEY AUTOINCREMENT,
                socket_id     INTEGER NOT NULL,
                variant_name  TEXT NOT NULL,
                created_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (socket_id) REFERENCES sockets (id)
                    ON DELETE CASCADE,
                UNIQUE(socket_id, variant_name)
            )
        ");

        // Create indexes for socket tables
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_socket_variants_socket_id ON socket_variants (socket_id)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_socket_variants_variant_name ON socket_variants (variant_name)");

        // Create trigger for sockets table
        $pdo->exec("
            CREATE TRIGGER IF NOT EXISTS trigger_sockets_update_timestamp
            AFTER UPDATE ON sockets
            FOR EACH ROW
            BEGIN
                UPDATE sockets SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
            END
        ");

        // Create custom_rules table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS custom_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name_pattern TEXT NOT NULL,
                field_to_change TEXT NOT NULL,
                new_value TEXT NOT NULL,
                is_active INTEGER DEFAULT 1,
                match_type TEXT DEFAULT 'name',
                cpu_count INTEGER DEFAULT NULL,
                original_id TEXT DEFAULT NULL,
                action_type TEXT DEFAULT 'update',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // Create indexes for custom_rules table
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_custom_rules_field_to_change ON custom_rules (field_to_change)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_custom_rules_is_active ON custom_rules (is_active)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_custom_rules_match_type ON custom_rules (match_type)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_custom_rules_cpu_count ON custom_rules (cpu_count)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_custom_rules_original_id ON custom_rules (original_id)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_custom_rules_action_type ON custom_rules (action_type)");

        // Create trigger for custom_rules table
        $pdo->exec("
            CREATE TRIGGER IF NOT EXISTS trigger_custom_rules_update_timestamp
            AFTER UPDATE ON custom_rules
            FOR EACH ROW
            BEGIN
                UPDATE custom_rules SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
            END
        ");

        // Create cpu_models table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS cpu_models (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_id TEXT,
                name TEXT NOT NULL,
                mark INTEGER,
                speed INTEGER,
                cpu_count INTEGER,
                cores INTEGER,
                p_cores INTEGER,
                e_cores INTEGER,
                core_display TEXT,
                tdp INTEGER,
                socket TEXT,
                socket_slug TEXT,
                category TEXT,
                whitelist INTEGER DEFAULT 0,
                rule_applied INTEGER DEFAULT 0,
                brand_tag TEXT,
                series_tag TEXT,
                core_model_tag TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // Create indexes for cpu_models table
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_name ON cpu_models (name)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_whitelist ON cpu_models (whitelist)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_socket ON cpu_models (socket)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_category ON cpu_models (category)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_original_id ON cpu_models (original_id)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_cpu_models_cpu_count ON cpu_models (cpu_count)");

        // Create trigger for cpu_models table
        $pdo->exec("
            CREATE TRIGGER IF NOT EXISTS trigger_cpu_models_update_timestamp
            AFTER UPDATE ON cpu_models
            FOR EACH ROW
            BEGIN
                UPDATE cpu_models SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
            END
        ");

        // Create amazon_search_terms table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS amazon_search_terms (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                brand_tag TEXT,
                keywords TEXT,
                generated_search_term TEXT NOT NULL,
                product_count INTEGER DEFAULT 0,
                type TEXT DEFAULT 'auto',
                active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // Check if type column exists in amazon_search_terms table, add if not
        $stmt = $pdo->query("PRAGMA table_info(amazon_search_terms)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $typeColumnExists = false;
        $activeColumnExists = false;

        foreach ($columns as $column) {
            if ($column['name'] === 'type') {
                $typeColumnExists = true;
            }
            if ($column['name'] === 'active') {
                $activeColumnExists = true;
            }
        }

        if (!$typeColumnExists) {
            $pdo->exec("ALTER TABLE amazon_search_terms ADD COLUMN type TEXT DEFAULT 'auto'");
            error_log("Added 'type' column to amazon_search_terms table");
        }

        if (!$activeColumnExists) {
            $pdo->exec("ALTER TABLE amazon_search_terms ADD COLUMN active INTEGER DEFAULT 1");
            error_log("Added 'active' column to amazon_search_terms table");
        }

        // Create indexes for amazon_search_terms table
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_amazon_search_terms_brand_tag ON amazon_search_terms (brand_tag)");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_amazon_search_terms_keywords ON amazon_search_terms (keywords)");

        error_log("Database schema initialized successfully");
        return true;
    } catch (PDOException $e) {
        error_log("Error initializing database schema: " . $e->getMessage());
        return false;
    }
}

/**
 * Log database statistics for monitoring
 *
 * @param PDO $pdo Database connection
 * @return void
 */
function logDatabaseStats($pdo) {
    try {
        // Check if custom_rules table exists and log stats
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='custom_rules'");
        $tableExists = $stmt->fetch() !== false;
        error_log("Custom rules table exists: " . ($tableExists ? 'Yes' : 'No'));

        if ($tableExists) {
            // Count the number of rules
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM custom_rules");
            $count = $stmt->fetch()['count'];
            error_log("Number of custom rules in database: {$count}");
        }

        // Check if cpu_models table exists and log stats
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='cpu_models'");
        $tableExists = $stmt->fetch() !== false;
        error_log("CPU models table exists: " . ($tableExists ? 'Yes' : 'No'));

        if ($tableExists) {
            // Count the total number of CPU models
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM cpu_models");
            $count = $stmt->fetch()['count'];
            error_log("Total CPU models in database: {$count}");

            // Count whitelisted CPU models
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM cpu_models WHERE whitelist = 1");
            $count = $stmt->fetch()['count'];
            error_log("Whitelisted CPU models in database: {$count}");
        }

        // Check if sockets table exists and log stats
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sockets'");
        $tableExists = $stmt->fetch() !== false;
        error_log("Sockets table exists: " . ($tableExists ? 'Yes' : 'No'));

        if ($tableExists) {
            // Count the number of sockets
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM sockets");
            $count = $stmt->fetch()['count'];
            error_log("Number of sockets in database: {$count}");

            // Count the number of socket variants
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM socket_variants");
            $count = $stmt->fetch()['count'];
            error_log("Number of socket variants in database: {$count}");
        }

        // Check if amazon_search_terms table exists and log stats
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='amazon_search_terms'");
        $tableExists = $stmt->fetch() !== false;
        error_log("Amazon search terms table exists: " . ($tableExists ? 'Yes' : 'No'));

        if ($tableExists) {
            // Count the number of amazon search terms
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM amazon_search_terms");
            $count = $stmt->fetch()['count'];
            error_log("Number of amazon search terms in database: {$count}");
        }
    } catch (PDOException $e) {
        error_log("Error logging database stats: " . $e->getMessage());
    }
}

/**
 * Get database connection
 * Creates the database and initializes schema if needed
 *
 * @return PDO Database connection
 */
function getDatabase() {
    $dbPath = __DIR__ . '/../data/database.sqlite';
    $dbDir = dirname($dbPath);

    error_log("Database path: {$dbPath}");

    // Create data directory if it doesn't exist
    if (!is_dir($dbDir)) {
        error_log("Creating data directory: {$dbDir}");
        if (!mkdir($dbDir, 0755, true)) {
            $errorMsg = "Failed to create data directory: {$dbDir}";
            error_log($errorMsg);
            throw new \Exception($errorMsg);
        }
    }

    $createTables = false;
    if (!file_exists($dbPath)) {
        $createTables = true;
        error_log("Database file not found, will create it at: {$dbPath}");
    } else {
        error_log("Database file exists at: {$dbPath}");
    }

    try {
        error_log("Connecting to SQLite database at: {$dbPath}");
        $pdo = new PDO('sqlite:' . $dbPath);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        error_log("Connected to SQLite database successfully");

        // Initialize database schema if needed
        if ($createTables) {
            error_log("New database detected, initializing schema");
            initializeDatabaseSchema($pdo);
        } else {
            // Verify tables exist and are up to date
            error_log("Verifying database schema");
            initializeDatabaseSchema($pdo);
        }

        // Test the connection
        $pdo->query('SELECT 1');
        error_log("Database connection test successful");

        // Log database statistics
        logDatabaseStats($pdo);

        return $pdo;
    } catch (PDOException $e) {
        $errorMsg = "Database connection error: " . $e->getMessage();
        error_log($errorMsg);
        throw new \Exception($errorMsg);
    }
}
