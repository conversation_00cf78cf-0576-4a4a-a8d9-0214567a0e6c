<?php
/**
 * Socket Management Functions
 * Handles database operations for sockets and socket variants
 */

// Prevent PHP errors from being output
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.0 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Get database connection
 *
 * @return PDO Database connection
 */
function getDbConnection() {
    $dbPath = __DIR__ . '/../data/database.sqlite';
    $dbDir = dirname($dbPath);

    // Create data directory if it doesn't exist
    if (!is_dir($dbDir)) {
        if (!mkdir($dbDir, 0755, true)) {
            $errorMsg = "Failed to create data directory: {$dbDir}";
            error_log($errorMsg);
            throw new \Exception($errorMsg);
        }
    }

    $createTables = false;
    if (!file_exists($dbPath)) {
        $createTables = true;
        error_log("Database file not found, will create it at: {$dbPath}");
    }

    try {
        $pdo = new PDO('sqlite:' . $dbPath);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

        // Create tables if needed
        if ($createTables) {
            // Create sockets table
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS sockets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    slug TEXT NOT NULL UNIQUE,
                    year INTEGER NOT NULL,
                    manufacturer TEXT NOT NULL,
                    type TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");

            // Create socket_variants table
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS socket_variants (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    socket_id INTEGER NOT NULL,
                    variant_name TEXT NOT NULL,
                    FOREIGN KEY (socket_id) REFERENCES sockets(id) ON DELETE CASCADE,
                    UNIQUE(socket_id, variant_name)
                )
            ");

            error_log("Database tables created successfully");
        }

        // Test the connection
        $pdo->query('SELECT 1');

        return $pdo;
    } catch (PDOException $e) {
        $errorMsg = "Database connection error: " . $e->getMessage();
        error_log($errorMsg);
        throw new \Exception($errorMsg);
    }
}

/**
 * Get all sockets with their variants
 *
 * @return array Array of sockets with their variants
 */
function getAllSockets() {
    try {
        $pdo = getDbConnection();

        // First, get all sockets
        $sql = "
            SELECT
                id, name, slug, year, manufacturer, type, created_at, updated_at
            FROM
                sockets
            ORDER BY
                year DESC, manufacturer, name
        ";

        $stmt = $pdo->query($sql);
        $sockets = $stmt->fetchAll();

        // Then get all variants for each socket
        if (!empty($sockets)) {
            $socketIds = array_column($sockets, 'id');

            // Initialize variants array for each socket
            foreach ($sockets as &$socket) {
                $socket['variants'] = [];
            }
            unset($socket); // Unset reference to avoid issues

            // Get all variants in a single query
            $variantsSql = "
                SELECT
                    socket_id, id, variant_name
                FROM
                    socket_variants
                WHERE
                    socket_id IN (" . implode(',', $socketIds) . ")
                ORDER BY
                    socket_id, id
            ";

            $variantsStmt = $pdo->query($variantsSql);
            $variants = $variantsStmt->fetchAll();

            // Group variants by socket_id
            $variantsBySocket = [];
            foreach ($variants as $variant) {
                $socketId = $variant['socket_id'];
                if (!isset($variantsBySocket[$socketId])) {
                    $variantsBySocket[$socketId] = [];
                }
                $variantsBySocket[$socketId][] = [
                    'id' => $variant['id'],
                    'name' => $variant['variant_name']
                ];
            }

            // Add variants to their respective sockets
            foreach ($sockets as &$socket) {
                $socketId = $socket['id'];
                if (isset($variantsBySocket[$socketId])) {
                    $socket['variants'] = $variantsBySocket[$socketId];
                }
            }
            unset($socket); // Unset reference
        }

        return [
            'success' => true,
            'data' => $sockets
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'message' => 'Error retrieving sockets: ' . $e->getMessage()
        ];
    }
}

/**
 * Get a single socket by ID with its variants
 *
 * @param int $socketId Socket ID
 * @return array Socket data with variants
 */
function getSocketById($socketId) {
    try {
        $pdo = getDbConnection();

        // Get socket data
        $sql = "SELECT id, name, slug, year, manufacturer, type, created_at, updated_at FROM sockets WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
        $stmt->execute();

        $socket = $stmt->fetch();

        if (!$socket) {
            return [
                'success' => false,
                'message' => 'Socket not found'
            ];
        }

        // Get socket variants
        $sql = "SELECT id, variant_name FROM socket_variants WHERE socket_id = :socket_id ORDER BY id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
        $stmt->execute();

        $socket['variants'] = $stmt->fetchAll();

        return [
            'success' => true,
            'data' => $socket
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'message' => 'Error retrieving socket: ' . $e->getMessage()
        ];
    }
}

/**
 * Add a new socket with variants
 *
 * @param array $socketData Socket data
 * @return array Result of the operation
 */
function addSocket($socketData) {
    try {
        $pdo = getDbConnection();

        // Validate required fields
        $requiredFields = ['name', 'slug', 'year', 'manufacturer', 'type'];
        foreach ($requiredFields as $field) {
            if (empty($socketData[$field])) {
                return [
                    'success' => false,
                    'message' => "Field '{$field}' is required"
                ];
            }
        }

        // Validate manufacturer
        if (!in_array($socketData['manufacturer'], ['Intel', 'AMD'])) {
            return [
                'success' => false,
                'message' => "Manufacturer must be 'Intel' or 'AMD'"
            ];
        }

        // Validate type
        if (!in_array($socketData['type'], ['desktop', 'server'])) {
            return [
                'success' => false,
                'message' => "Type must be 'desktop' or 'server'"
            ];
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Insert socket
        $sql = "
            INSERT INTO sockets (name, slug, year, manufacturer, type)
            VALUES (:name, :slug, :year, :manufacturer, :type)
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':name', $socketData['name']);
        $stmt->bindParam(':slug', $socketData['slug']);
        $stmt->bindParam(':year', $socketData['year'], PDO::PARAM_INT);
        $stmt->bindParam(':manufacturer', $socketData['manufacturer']);
        $stmt->bindParam(':type', $socketData['type']);

        $stmt->execute();

        $socketId = $pdo->lastInsertId();

        // Insert variants if provided
        if (!empty($socketData['variants']) && is_array($socketData['variants'])) {
            $sql = "INSERT INTO socket_variants (socket_id, variant_name) VALUES (:socket_id, :variant_name)";
            $stmt = $pdo->prepare($sql);

            foreach ($socketData['variants'] as $variant) {
                if (!empty($variant)) {
                    $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
                    $stmt->bindParam(':variant_name', $variant);
                    $stmt->execute();
                }
            }
        }

        // Commit transaction
        $pdo->commit();

        // Regenerate socket data file
        regenerateSocketDataFile();

        return [
            'success' => true,
            'message' => 'Socket added successfully',
            'socketId' => $socketId
        ];
    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        // Check for UNIQUE constraint violation
        if ($e->getCode() == 23000 && (strpos($e->getMessage(), 'UNIQUE constraint failed: sockets.name') !== false ||
                                      strpos($e->getMessage(), 'UNIQUE constraint failed: sockets.slug') !== false)) {
            return [
                'success' => false,
                'message' => 'A socket with this name or slug already exists'
            ];
        }

        return [
            'success' => false,
            'message' => 'Error adding socket: ' . $e->getMessage()
        ];
    } catch (\Exception $e) {
        // Rollback transaction on error
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }

        return [
            'success' => false,
            'message' => 'Error adding socket: ' . $e->getMessage()
        ];
    }
}

/**
 * Update an existing socket and its variants
 *
 * @param int $socketId Socket ID
 * @param array $socketData Socket data
 * @return array Result of the operation
 */
function updateSocket($socketId, $socketData) {
    try {
        $pdo = getDbConnection();

        // Validate required fields
        $requiredFields = ['name', 'slug', 'year', 'manufacturer', 'type'];
        foreach ($requiredFields as $field) {
            if (empty($socketData[$field])) {
                return [
                    'success' => false,
                    'message' => "Field '{$field}' is required"
                ];
            }
        }

        // Validate manufacturer
        if (!in_array($socketData['manufacturer'], ['Intel', 'AMD'])) {
            return [
                'success' => false,
                'message' => "Manufacturer must be 'Intel' or 'AMD'"
            ];
        }

        // Validate type
        if (!in_array($socketData['type'], ['desktop', 'server'])) {
            return [
                'success' => false,
                'message' => "Type must be 'desktop' or 'server'"
            ];
        }

        // Check if socket exists
        $sql = "SELECT id FROM sockets WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
        $stmt->execute();

        if (!$stmt->fetch()) {
            return [
                'success' => false,
                'message' => 'Socket not found'
            ];
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Update socket
        $sql = "
            UPDATE sockets
            SET name = :name, slug = :slug, year = :year, manufacturer = :manufacturer, type = :type, updated_at = CURRENT_TIMESTAMP
            WHERE id = :id
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':name', $socketData['name']);
        $stmt->bindParam(':slug', $socketData['slug']);
        $stmt->bindParam(':year', $socketData['year'], PDO::PARAM_INT);
        $stmt->bindParam(':manufacturer', $socketData['manufacturer']);
        $stmt->bindParam(':type', $socketData['type']);
        $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);

        $stmt->execute();

        // Handle variants
        if (isset($socketData['variants'])) {
            // Delete existing variants
            $sql = "DELETE FROM socket_variants WHERE socket_id = :socket_id";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
            $stmt->execute();

            // Insert new variants
            if (!empty($socketData['variants']) && is_array($socketData['variants'])) {
                $sql = "INSERT INTO socket_variants (socket_id, variant_name) VALUES (:socket_id, :variant_name)";
                $stmt = $pdo->prepare($sql);

                foreach ($socketData['variants'] as $variant) {
                    if (!empty($variant)) {
                        $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
                        $stmt->bindParam(':variant_name', $variant);
                        $stmt->execute();
                    }
                }
            }
        }

        // Commit transaction
        $pdo->commit();

        // Regenerate socket data file
        regenerateSocketDataFile();

        return [
            'success' => true,
            'message' => 'Socket updated successfully'
        ];
    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        // Check for UNIQUE constraint violation
        if ($e->getCode() == 23000 && (strpos($e->getMessage(), 'UNIQUE constraint failed: sockets.name') !== false ||
                                      strpos($e->getMessage(), 'UNIQUE constraint failed: sockets.slug') !== false)) {
            return [
                'success' => false,
                'message' => 'A socket with this name or slug already exists'
            ];
        }

        return [
            'success' => false,
            'message' => 'Error updating socket: ' . $e->getMessage()
        ];
    } catch (\Exception $e) {
        // Rollback transaction on error
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }

        return [
            'success' => false,
            'message' => 'Error updating socket: ' . $e->getMessage()
        ];
    }
}

/**
 * Delete a socket and its variants
 *
 * @param int $socketId Socket ID
 * @return array Result of the operation
 */
function deleteSocket($socketId) {
    try {
        $pdo = getDbConnection();

        // Check if socket exists
        $sql = "SELECT id FROM sockets WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
        $stmt->execute();

        if (!$stmt->fetch()) {
            return [
                'success' => false,
                'message' => 'Socket not found'
            ];
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Delete socket (variants will be deleted automatically due to ON DELETE CASCADE)
        $sql = "DELETE FROM sockets WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
        $stmt->execute();

        // Commit transaction
        $pdo->commit();

        // Regenerate socket data file
        regenerateSocketDataFile();

        return [
            'success' => true,
            'message' => 'Socket deleted successfully'
        ];
    } catch (\Exception $e) {
        // Rollback transaction on error
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }

        return [
            'success' => false,
            'message' => 'Error deleting socket: ' . $e->getMessage()
        ];
    }
}

/**
 * Add a new socket variant
 *
 * @param int $socketId Socket ID
 * @param string $variantName Variant name
 * @return array Result of the operation
 */
function addSocketVariant($socketId, $variantName) {
    try {
        $pdo = getDbConnection();

        // Check if socket exists
        $sql = "SELECT id FROM sockets WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $socketId, PDO::PARAM_INT);
        $stmt->execute();

        if (!$stmt->fetch()) {
            return [
                'success' => false,
                'message' => 'Socket not found'
            ];
        }

        // Check if variant already exists
        $sql = "SELECT id FROM socket_variants WHERE socket_id = :socket_id AND variant_name = :variant_name";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
        $stmt->bindParam(':variant_name', $variantName);
        $stmt->execute();

        if ($stmt->fetch()) {
            return [
                'success' => false,
                'message' => 'Variant already exists for this socket'
            ];
        }

        // Insert variant
        $sql = "INSERT INTO socket_variants (socket_id, variant_name) VALUES (:socket_id, :variant_name)";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':socket_id', $socketId, PDO::PARAM_INT);
        $stmt->bindParam(':variant_name', $variantName);
        $stmt->execute();

        $variantId = $pdo->lastInsertId();

        // Regenerate socket data file
        regenerateSocketDataFile();

        return [
            'success' => true,
            'message' => 'Variant added successfully',
            'variantId' => $variantId
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'message' => 'Error adding variant: ' . $e->getMessage()
        ];
    }
}

/**
 * Update a socket variant
 *
 * @param int $variantId Variant ID
 * @param string $variantName New variant name
 * @return array Result of the operation
 */
function updateSocketVariant($variantId, $variantName) {
    try {
        $pdo = getDbConnection();

        // Check if variant exists
        $sql = "SELECT id, socket_id FROM socket_variants WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
        $stmt->execute();

        $variant = $stmt->fetch();

        if (!$variant) {
            return [
                'success' => false,
                'message' => 'Variant not found'
            ];
        }

        // Check if variant name already exists for this socket
        $sql = "SELECT id FROM socket_variants WHERE socket_id = :socket_id AND variant_name = :variant_name AND id != :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':socket_id', $variant['socket_id'], PDO::PARAM_INT);
        $stmt->bindParam(':variant_name', $variantName);
        $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->fetch()) {
            return [
                'success' => false,
                'message' => 'Variant name already exists for this socket'
            ];
        }

        // Update variant
        $sql = "UPDATE socket_variants SET variant_name = :variant_name WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':variant_name', $variantName);
        $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
        $stmt->execute();

        // Regenerate socket data file
        regenerateSocketDataFile();

        return [
            'success' => true,
            'message' => 'Variant updated successfully'
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'message' => 'Error updating variant: ' . $e->getMessage()
        ];
    }
}

/**
 * Delete a socket variant
 *
 * @param int $variantId Variant ID
 * @return array Result of the operation
 */
function deleteSocketVariant($variantId) {
    try {
        $pdo = getDbConnection();

        // Check if variant exists
        $sql = "SELECT id FROM socket_variants WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
        $stmt->execute();

        if (!$stmt->fetch()) {
            return [
                'success' => false,
                'message' => 'Variant not found'
            ];
        }

        // Delete variant
        $sql = "DELETE FROM socket_variants WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $variantId, PDO::PARAM_INT);
        $stmt->execute();

        // Regenerate socket data file
        regenerateSocketDataFile();

        return [
            'success' => true,
            'message' => 'Variant deleted successfully'
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'message' => 'Error deleting variant: ' . $e->getMessage()
        ];
    }
}

/**
 * Regenerate the socket data JSON file
 *
 * @return bool True on success, false on failure
 */
function regenerateSocketDataFile() {
    try {
        // First try to use the SocketDataService if available
        if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
            require_once __DIR__ . '/../vendor/autoload.php';

            if (class_exists('App\Services\SocketDataService')) {
                $pdo = getDbConnection();
                $socketService = new App\Services\SocketDataService($pdo);

                $outputDir = __DIR__ . '/../data/generated';
                if (!is_dir($outputDir)) {
                    mkdir($outputDir, 0755, true);
                }

                $result = $socketService->generateSocketDataFile($outputDir);
            }
        }

        // Fallback: Generate the JSON file directly
        $pdo = getDbConnection();

        // Get all sockets with their variants
        $sql = "
            SELECT
                s.id, s.name, s.slug, s.year, s.manufacturer, s.type,
                sv.id as variant_id, sv.variant_name
            FROM
                sockets s
            LEFT JOIN
                socket_variants sv ON s.id = sv.socket_id
            ORDER BY
                s.year DESC, s.manufacturer, s.name, sv.id
        ";

        $stmt = $pdo->query($sql);
        $rows = $stmt->fetchAll();

        // Process the data into a structured format
        $sockets = [];
        foreach ($rows as $row) {
            $socketId = $row['id'];

            if (!isset($sockets[$socketId])) {
                $sockets[$socketId] = [
                    'id' => $socketId,
                    'name' => $row['name'],
                    'slug' => $row['slug'],
                    'year' => $row['year'],
                    'manufacturer' => $row['manufacturer'],
                    'type' => $row['type'],
                    'variants' => []
                ];
            }

            if (!empty($row['variant_id'])) {
                $sockets[$socketId]['variants'][] = [
                    'id' => $row['variant_id'],
                    'name' => $row['variant_name']
                ];
            }
        }

        // Convert to indexed array
        $socketsArray = array_values($sockets);

        // Save to JSON file
        $jsonData = json_encode($socketsArray, JSON_PRETTY_PRINT);
        $targetFile = __DIR__ . '/../data/sockets.json';

        if (file_put_contents($targetFile, $jsonData)) {
            return true;
        }

        return false;
    } catch (\Exception $e) {
        error_log('Error regenerating socket data file: ' . $e->getMessage());
        return false;
    }
}
