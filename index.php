<?php
/**
 * CPU Prices Backend - Admin Panel
 * Secure implementation with session management, CSRF protection, and rate limiting
 */

// Define secure access constant to protect included files
define('SECURE_ACCESS', true);

// Include configuration file
require_once 'config/config.php';

// Include authentication and session management files
require_once 'inc/auth.php';
require_once 'inc/session.php';
require_once 'inc/login-handler.php';

// If user is not logged in, redirect to login page
if (!$is_logged_in) {
    header('Location: login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <title>CPU Prices - Backend</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/png" href="/assets/favicon/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/assets/favicon/favicon.svg" />
    <link rel="shortcut icon" href="/assets/favicon/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="CPU Prices" />
    <link rel="manifest" href="/assets/favicon/site.webmanifest" />
    <meta name="csrf-token" content="<?php echo htmlspecialchars($csrf_token); ?>" />
    <link rel="stylesheet" href="/assets/css/admin.css?v=<?php echo filemtime($_SERVER['DOCUMENT_ROOT'] . '/assets/css/admin.css'); ?>" />
    <script src="/assets/js/admin.js?v=<?php echo filemtime($_SERVER['DOCUMENT_ROOT'] . '/assets/js/admin.js'); ?>"></script>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <a href="/" class="logo-container">
                    <div class="logo">
                        <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="32px" height="32px" viewBox="0,0,256,256"><g transform="translate(-43.52,-43.52) scale(1.34,1.34)"><g fill="none" fill-rule="nonzero" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none" font-size="none" text-anchor="none" style="mix-blend-mode: normal"><g transform="scale(5.33333,5.33333)"><path d="M6,8v32c0,1.1 0.9,2 2,2h32c1.1,0 2,-0.9 2,-2v-32c0,-1.1 -0.9,-2 -2,-2h-32c-1.1,0 -2,0.9 -2,2z" fill="#43a047"></path><path d="M15,10c0,0.6 -0.4,1 -1,1c-0.6,0 -1,-0.4 -1,-1c0,-0.6 0.4,-1 1,-1c0.6,0 1,0.4 1,1zM18,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,9c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM18,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,13c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,17c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,21c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,25c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,29c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM10,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM18,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM14,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM18,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM22,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM26,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM30,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM34,37c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1zM38,33c-0.6,0 -1,0.4 -1,1c0,0.6 0.4,1 1,1c0.6,0 1,-0.4 1,-1c0,-0.6 -0.4,-1 -1,-1z" fill="#ffeb3b"></path><path d="M29,30h-10c-0.5,0 -1,-0.5 -1,-1v-10c0,-0.5 0.5,-1 1,-1h10c0.5,0 1,0.5 1,1v10c0,0.5 -0.5,1 -1,1z" fill="#37474f"></path></g></g></g></svg>
                    </div>
                    <h1>CPU Prices - Backend</h1>
                </a>
            </div>
            <div class="logout">
                <?php if ($is_logged_in): ?>
                <a href="?logout=1&csrf_token=<?php echo urlencode($csrf_token); ?>">Logout</a>
                <?php endif; ?>
            </div>
        </div>

        <div class="content">
            <?php if ($is_logged_in): ?>
                <div class="tab-navigation">
                    <ul>
                        <li><a href="#socket-data" class="active" data-tab="socket-data">Socket Data</a></li>
                        <li><a href="#cpu-data" data-tab="cpu-data">CPU Data</a></li>
                        <li><a href="#amazon-search" data-tab="amazon-search">Amazon Search</a></li>
                        <li><a href="#settings" data-tab="settings">Settings</a></li>
                    </ul>
                </div>

                <div id="socket-data" class="tab-content active">
                    <h3>Socket Data Management</h3>
                    <p>Manage CPU sockets and their variants. Changes will be automatically applied to the socket data file.</p>

                    <!-- Hidden CSRF token for AJAX requests -->
                    <input type="hidden" id="socket-csrf-token" value="<?php echo htmlspecialchars($csrf_token); ?>" />

                    <div class="socket-management">
                        <div class="socket-overview">
                            <h4>Socket Values in CPU Data (0)</h4>
                            <div id="cpu-socket-tags" class="socket-tags-container">
                                <div class="loading-message">Loading socket values...</div>
                            </div>
                        </div>
                        <div class="socket-actions">
                            <button id="add-socket-btn" class="primary-button">Add New Socket</button>
                        </div>

                        <div class="socket-list-container">
                            <table id="socket-list" class="data-table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Slug</th>
                                        <th>Year</th>
                                        <th>Brand</th>
                                        <th>Type</th>
                                        <th>Variants</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Socket data will be loaded here via JavaScript -->
                                    <tr class="loading-row">
                                        <td colspan="7">Loading socket data...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Socket Form Modal -->
                    <div id="socket-modal" class="modal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3 id="socket-modal-title">Add New Socket</h3>
                                <span class="modal-close">&times;</span>
                            </div>
                            <div class="modal-body">
                                <form id="socket-form">
                                    <input type="hidden" id="socket-id" name="socket-id" value="">

                                    <div class="form-group">
                                        <label for="socket-name">Socket Name:</label>
                                        <input type="text" id="socket-name" name="socket-name" required>
                                        <div class="form-hint">Human-readable name (e.g., "LGA 1700")</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="socket-slug">Slug:</label>
                                        <input type="text" id="socket-slug" name="socket-slug" required>
                                        <div class="form-hint">URL-friendly identifier (e.g., "lga1700")</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="socket-year">Year:</label>
                                        <input type="number" id="socket-year" name="socket-year" min="1990" max="2100" required>
                                        <div class="form-hint">Year of introduction</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="socket-manufacturer">Manufacturer:</label>
                                        <select id="socket-manufacturer" name="socket-manufacturer" required>
                                            <option value="">Select Manufacturer</option>
                                            <option value="Intel">Intel</option>
                                            <option value="AMD">AMD</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="socket-type">Type:</label>
                                        <select id="socket-type" name="socket-type" required>
                                            <option value="">Select Type</option>
                                            <option value="desktop">Desktop</option>
                                            <option value="server">Server</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Variants:</label>
                                        <div id="socket-variants-container">
                                            <div class="variant-row">
                                                <input type="text" name="socket-variants[]" placeholder="Variant name">
                                                <button type="button" class="remove-variant-btn">Remove</button>
                                            </div>
                                        </div>
                                        <button type="button" id="add-variant-btn">Add Variant</button>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" id="socket-form-submit" class="primary-button">Save Socket</button>
                                        <button type="button" id="socket-form-cancel" class="secondary-button">Cancel</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Confirmation Modal -->
                    <div id="delete-modal" class="modal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>Confirm Deletion</h3>
                                <span class="modal-close">&times;</span>
                            </div>
                            <div class="modal-body">
                                <p id="delete-message">Are you sure you want to delete this item?</p>
                                <input type="hidden" id="delete-id" value="">
                                <input type="hidden" id="delete-type" value="">
                                <div class="form-actions">
                                    <button id="confirm-delete-btn" class="danger-button">Delete</button>
                                    <button id="cancel-delete-btn" class="secondary-button">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications are now created dynamically via JavaScript -->
                </div>

                <div id="cpu-data" class="tab-content">
                    <h3>CPU Data Management</h3>
                    <p>Update CPU models data (taken manually from <a href="https://www.cpubenchmark.net/CPU_mega_page.html" target="_blank">cpubenchmark.net</a>).</p>

                    <!-- CPU Data Inner Tabs -->
                    <div class="inner-tab-navigation">
                        <ul>
                            <li><a href="#" class="inner-tab-link active" data-inner-tab="import-tab">Import</a></li>
                            <li><a href="#" class="inner-tab-link" data-inner-tab="review-tab">Review Data</a></li>
                            <li><a href="#" class="inner-tab-link" data-inner-tab="custom-rules-tab">Custom Rules</a></li>
                            <li><a href="#" class="inner-tab-link" data-inner-tab="final-data-tab">Whitelisted Data</a></li>
                            <li><a href="#" class="inner-tab-link" data-inner-tab="cpu-details-tab">CPU Details</a></li>
                        </ul>
                    </div>

                    <!-- CPU Data Statistics -->
                    <div class="cpu-data-stats">
                        <span id="cpu-stats-container" class="stats-text">Loading statistics...</span>
                    </div>

                    <!-- Import Tab Content -->
                    <div id="import-tab" class="inner-tab-content active">
                        <div class="cpu-data-import">
                            <h4>Import CPU Data</h4>
                            <p>Paste JSON data below to update the CPU models database.</p>

                            <form id="cpu-data-form">
                                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">

                                <!-- Error container for displaying validation errors -->
                                <div id="cpu-data-error" class="error-message" style="display: none;"></div>

                                <div class="form-group">
                                    <textarea id="cpu-data-json" name="cpu-data-json" rows="15" placeholder="Paste JSON data here..."></textarea>
                                    <div class="form-hint">
                                        JSON should be in the format: {"data": [{"id": "123", "name": "CPU Name", "cpumark": "1234", ...}]}
                                        <br>Numeric values can include commas (e.g., "2,409") and "NA" is accepted for missing values.
                                        <a href="#" id="show-sample-json">Show sample format</a>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" id="cpu-data-submit" class="primary-button">Update CPU Data</button>
                                    <button type="button" id="cpu-data-clear" class="secondary-button">Clear</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Review Data Tab Content -->
                    <div id="review-tab" class="inner-tab-content">
                        <!-- CPU Models Viewer -->
                        <div class="cpu-models-viewer">
                            <h4>CPU Models</h4>
                            <div class="cpu-models-filters">
                                <div class="cpu-models-search">
                                    <input type="text" id="cpu-models-search" placeholder="Search CPU models..." />
                                    <button type="button" id="cpu-models-search-clear" class="search-clear-button">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <line x1="18" y1="6" x2="6" y2="18"></line>
                                            <line x1="6" y1="6" x2="18" y2="18"></line>
                                        </svg>
                                    </button>
                                </div>
                                <div class="cpu-models-filter-selects">
                                    <div class="filter-select-group">
                                        <label for="cpu-models-socket-filter">Socket:</label>
                                        <select id="cpu-models-socket-filter">
                                            <option value="">All Sockets</option>
                                        </select>
                                    </div>
                                    <div class="filter-select-group">
                                        <label for="cpu-models-category-filter">Category:</label>
                                        <select id="cpu-models-category-filter">
                                            <option value="">All Categories</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="cpu-models-container">
                                <table class="data-table cpu-models-table">
                                    <thead>
                                        <tr>
                                            <th class="nowrap">ID</th>
                                            <th class="nowrap">Name</th>
                                            <th class="nowrap">CPU Mark</th>
                                            <th class="nowrap">TDP</th>
                                            <th class="nowrap">Socket</th>
                                            <th class="nowrap">Category</th>
                                            <th class="nowrap">CPUs</th>
                                            <th class="nowrap">Cores</th>
                                            <th class="nowrap">Secondary</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cpu-models-list">
                                        <tr class="loading-row">
                                            <td colspan="9">Loading CPU models...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Custom Rules Tab Content -->
                    <div id="custom-rules-tab" class="inner-tab-content">
                        <div class="custom-rules-container">
                            <div class="custom-rules-header">
                                <h4>Custom Rules</h4>
                                <p>Define rules to modify CPU data based on name patterns. These rules will be applied when processing the data.</p>
                                <button id="add-rule-btn" class="primary-button">Add New Rule</button>
                            </div>

                            <div class="custom-rules-list-container">
                                <table class="data-table custom-rules-table">
                                    <thead>
                                        <tr>
                                            <th class="nowrap">Match Type</th>
                                            <th class="nowrap">Pattern/ID</th>
                                            <th class="nowrap">CPUs</th>
                                            <th class="nowrap">Action</th>
                                            <th class="nowrap">Field</th>
                                            <th class="nowrap">New Value</th>
                                            <th class="nowrap">Status</th>
                                            <th class="nowrap">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="custom-rules-list">
                                        <tr class="loading-row">
                                            <td colspan="8">Loading custom rules...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Custom Rule Modal -->
                        <div id="rule-modal" class="modal">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3 id="rule-modal-title">Add New Rule</h3>
                                    <span class="modal-close">&times;</span>
                                </div>
                                <div class="modal-body">
                                    <form id="rule-form">
                                        <input type="hidden" id="rule-id" name="rule-id" value="">
                                        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">

                                        <div class="form-group">
                                            <label for="match-type">Match Type:</label>
                                            <select id="match-type" name="match-type" required>
                                                <option value="name">Match by Name</option>
                                                <option value="name_cpu_count">Match by Name + CPU Count</option>
                                                <option value="original_id_cpu_count">Match by Original ID + CPU Count</option>
                                            </select>
                                            <div class="form-hint">
                                                Select how to match CPU models.
                                            </div>
                                        </div>

                                        <div class="form-group" id="name-pattern-group">
                                            <label for="name-pattern">Name Pattern:</label>
                                            <input type="text" id="name-pattern" name="name-pattern" required>
                                            <div class="form-hint">
                                                Enter a text pattern to match in CPU names (case-insensitive).
                                            </div>
                                        </div>

                                        <div class="form-group" id="original-id-group" style="display: none;">
                                            <label for="original-id">Original ID:</label>
                                            <input type="text" id="original-id" name="original-id">
                                            <div class="form-hint">
                                                Enter the original ID to match.
                                            </div>
                                        </div>

                                        <div class="form-group" id="cpu-count-group" style="display: none;">
                                            <label for="cpu-count">CPU Count:</label>
                                            <input type="number" id="cpu-count" name="cpu-count" min="1">
                                            <div class="form-hint">
                                                Enter the CPU count to match.
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="action-type">Action Type:</label>
                                            <select id="action-type" name="action-type" required>
                                                <option value="update">Update Field</option>
                                                <option value="delete">Delete Record</option>
                                            </select>
                                            <div class="form-hint">
                                                Select whether to update a field or delete the matched record.
                                            </div>
                                        </div>

                                        <div class="form-group" id="field-to-change-group">
                                            <label for="field-to-change">Field to Change:</label>
                                            <select id="field-to-change" name="field-to-change" required>
                                                <option value="">Select Field</option>
                                                <option value="socket">Socket</option>
                                                <option value="cat">Category</option>
                                                <option value="tdp">TDP</option>
                                                <option value="cpuCount">CPU Count</option>
                                                <option value="cores">Cores</option>
                                                <option value="secondaryCores">Secondary Cores</option>
                                            </select>
                                        </div>

                                        <div class="form-group" id="new-value-group">
                                            <label for="new-value">New Value:</label>
                                            <input type="text" id="new-value" name="new-value" required>
                                        </div>

                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="is-active" name="is-active" value="1" checked>
                                                Active
                                            </label>
                                        </div>

                                        <div class="form-actions">
                                            <button type="submit" id="rule-form-submit" class="primary-button">Save Rule</button>
                                            <button type="button" id="rule-form-cancel" class="secondary-button">Cancel</button>
                                            <input type="hidden" id="rule-id" name="rule-id" value="">
                                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Delete Confirmation Modal -->
                        <div id="rule-delete-modal" class="modal">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3>Confirm Deletion</h3>
                                    <span class="modal-close">&times;</span>
                                </div>
                                <div class="modal-body">
                                    <p id="delete-rule-message">Are you sure you want to delete this rule? This action cannot be undone.</p>
                                    <div class="form-actions">
                                        <button type="button" id="confirm-rule-delete" class="danger-button" onclick="handleConfirmRuleDelete()">Delete</button>
                                        <button type="button" id="cancel-rule-delete" class="secondary-button">Cancel</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Final Data Tab Content -->
                    <div id="final-data-tab" class="inner-tab-content">
                        <div class="final-data-container">
                            <div class="final-data-header">
                                <h4>Whitelisted Data</h4>
                                <p>This tab shows the whitelisted CPU data after applying custom rules, processing categories, and matching sockets.</p>
                                <div class="final-data-actions">
                                    <div class="final-data-buttons">
                                        <button id="generate-final-data-btn" class="primary-button">Generate Whitelisted Data</button>
                                    </div>
                                    <div class="final-data-stats">
                                        <span id="final-data-stats-container" class="stats-text">Loading statistics...</span>
                                    </div>
                                </div>
                            </div>

                            <div class="final-data-filters">
                                <div class="final-data-search">
                                    <input type="text" id="final-data-search" placeholder="Search CPU models..." />
                                    <button type="button" id="final-data-search-clear" class="search-clear-button">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <line x1="18" y1="6" x2="6" y2="18"></line>
                                            <line x1="6" y1="6" x2="18" y2="18"></line>
                                        </svg>
                                    </button>
                                </div>
                                <div class="final-data-filter-selects">
                                    <div class="filter-select-group">
                                        <label for="final-data-socket-filter">Socket:</label>
                                        <select id="final-data-socket-filter">
                                            <option value="">All Sockets</option>
                                        </select>
                                    </div>
                                    <div class="filter-select-group">
                                        <label for="final-data-category-filter">Category:</label>
                                        <select id="final-data-category-filter">
                                            <option value="">All Categories</option>
                                        </select>
                                    </div>
                                    <div class="filter-select-group">
                                        <label for="final-data-whitelist-filter">Whitelist:</label>
                                        <select id="final-data-whitelist-filter">
                                            <option value="">All</option>
                                            <option value="true">Yes</option>
                                            <option value="false">No</option>
                                        </select>
                                    </div>

                                    <div class="filter-select-group">
                                        <label for="final-data-rule-applied-filter">Custom Rule:</label>
                                        <select id="final-data-rule-applied-filter">
                                            <option value="">All</option>
                                            <option value="true">Applied</option>
                                            <option value="false">Not Applied</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="final-data-list-container">
                                <table class="data-table final-data-table">
                                    <thead>
                                        <tr>
                                            <th class="nowrap">ID</th>
                                            <th class="nowrap">Name</th>
                                            <th class="nowrap">CPU Mark</th>
                                            <th class="nowrap">TDP</th>
                                            <th class="nowrap">Socket</th>
                                            <th class="nowrap">Category</th>
                                            <th class="nowrap">CPUs</th>
                                            <th class="nowrap">Cores</th>
                                            <th class="nowrap">Whitelist</th>
                                            <th class="nowrap">Rule</th>
                                        </tr>
                                    </thead>
                                    <tbody id="final-data-list">
                                        <tr class="loading-row">
                                            <td colspan="10">Loading whitelisted data...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- CPU Details Tab Content -->
                    <div id="cpu-details-tab" class="inner-tab-content">
                        <div class="cpu-details-container">
                            <div class="cpu-details-header">
                                <h4>CPU Details</h4>
                                <p>Manage brand, series, and core model tags for CPU models. Click the edit button to modify tags for a specific CPU.</p>
                                <div class="cpu-details-actions">
                                    <button id="generate-all-identifiers-btn" class="primary-button">Generate All Identifiers</button>
                                </div>
                            </div>

                            <div class="cpu-details-filters">
                                <div class="cpu-details-search">
                                    <input type="text" id="cpu-details-search" placeholder="Search CPU models..." />
                                    <button type="button" id="cpu-details-search-clear" class="search-clear-button">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <line x1="18" y1="6" x2="6" y2="18"></line>
                                            <line x1="6" y1="6" x2="18" y2="18"></line>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="cpu-details-list-container">
                                <table class="data-table cpu-details-table">
                                    <thead>
                                        <tr>
                                            <th class="nowrap sortable" data-sort="id">ID <span class="sort-icon"></span></th>
                                            <th class="nowrap sortable" data-sort="original_id">Orig. <span class="sort-icon"></span></th>
                                            <th class="nowrap sortable" data-sort="name">Name <span class="sort-icon"></span></th>
                                            <th class="nowrap">Brand</th>
                                            <th class="nowrap">Series</th>
                                            <th class="nowrap">Core Model</th>
                                            <th class="nowrap">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cpu-details-list">
                                        <tr class="loading-row">
                                            <td colspan="7">Generate CPU details to view data...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- CPU Identifiers Popup -->
                        <div id="identifiers-popup" class="identifiers-popup">
                            <div class="identifiers-popup-content">
                                <div class="identifiers-popup-header">
                                    <h3 class="identifiers-popup-title">CPU Identifiers</h3>
                                    <button class="identifiers-popup-close">×</button>
                                </div>
                                <div id="identifiers-popup-container" class="identifiers-container">
                                    <div class="identifiers-loading">
                                        <p>Loading identifiers...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="settings" class="tab-content">
                    <h3>System Settings</h3>
                    <p>Configure system settings and preferences.</p>
                    <div class="action-buttons">
                        <button>Update Settings</button>
                        <button>Reset to Defaults</button>
                    </div>
                </div>

                <div id="amazon-search" class="tab-content">
                    <h3>Amazon Search Term Management <span id="amazon-search-term-count" class="count-indicator">(0)</span></h3>
                    <p>Manage and generate search terms for Amazon PA-API.</p>
                    <!-- Content for Amazon Search tab will go here -->
                    <div class="amazon-search-actions">
                        <button id="generate-amazon-search-terms-btn" class="primary-button">Generate Search Terms</button>
                        <button id="add-manual-search-term-btn" class="secondary-button">Add Manual Search Term</button>
                    </div>
                    <div class="amazon-search-terms-container">
                        <table id="amazon-search-terms-list" class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Brand</th>
                                    <th>Keywords</th>
                                    <th>Generated Search Term</th>
                                    <th>Type</th>
                                    <th>Products</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="loading-row">
                                    <td colspan="8">Loading search terms...</td>
                                </tr>
                                <!-- Search terms will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Modal for adding/editing manual search terms -->
                    <div id="search-term-modal" class="modal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3 id="search-term-modal-title">Add Manual Search Term</h3>
                                <span class="modal-close">&times;</span>
                            </div>
                            <div class="modal-body">
                                <form id="search-term-form">
                                    <input type="hidden" id="search-term-id" name="id" value="">
                                    <input type="hidden" id="search-term-action" name="action" value="add_manual">
                                    <input type="hidden" id="search-term-csrf-token" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                                    <div class="form-group">
                                        <label for="search-term-brand">Brand (optional):</label>
                                        <input type="text" id="search-term-brand" name="brand_tag" placeholder="e.g., Intel, AMD">
                                        <div class="form-hint">
                                            Enter a brand name to associate with this search term.
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="search-term-keywords">Keywords (optional):</label>
                                        <input type="text" id="search-term-keywords" name="keywords" placeholder="e.g., Core i7, Ryzen 9">
                                        <div class="form-hint">
                                            Enter additional keywords to associate with this search term.
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="search-term-generated">Search Term (required):</label>
                                        <input type="text" id="search-term-generated" name="generated_search_term" placeholder="e.g., Intel Core i7" required>
                                        <div class="form-hint">
                                            Enter the search term that will be used for Amazon product searches.
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" id="search-term-submit" class="primary-button">Save</button>
                                        <button type="button" id="search-term-cancel" class="secondary-button">Cancel</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Delete confirmation modal for search terms -->
                    <div id="search-term-delete-modal" class="modal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>Confirm Delete</h3>
                                <span class="modal-close">&times;</span>
                            </div>
                            <div class="modal-body">
                                <p>Are you sure you want to delete this search term? This action cannot be undone.</p>
                                <div class="form-actions">
                                    <button id="confirm-delete-search-term" class="danger-button">Delete</button>
                                    <button id="cancel-delete-search-term" class="secondary-button">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
