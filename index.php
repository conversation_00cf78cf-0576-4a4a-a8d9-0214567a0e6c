<?php
// Define ABSPATH to allow inclusion of header.php
define('ABSPATH', __DIR__);
require_once 'header.php';
?>

<?php
// Check if debug mode is enabled
$test = $_GET['debug'] ?? false;

// Affiliate params (US)
$affiliate_us = [
    'tag' => 'cpu-prices-20',
    'ref_' => 'as_li_tl'
];
?>

<div id="filters">

    <!-- Units fieldset removed - Value is now calculated as CPU Mark / Price -->
    <!-- <fieldset class="units">
        <legend>Price per</legend>
        <label><input type="radio" name="units" data-units="thousand" checked />1000 Mark pts.</label>
        <label><input type="radio" name="units" data-units="hundred" />100 Mark pts.</label>
    </fieldset> -->

    <!-- <fieldset class="benchmark">
        <legend>Benchmark</legend>
        <div class="benchmark-container">
            <div class="input-group">
                <input type="number" id="score_min" size="2" min="0" step="100" placeholder="Min" />
                <label class="sr-only" for="score_min">Min</label>
            </div>
            <div class="input-group">
                <input type="number" id="score_max" size="2" min="0" step="100" placeholder="Max" />
                <label class="sr-only" for="score_max">Max</label>
            </div>
        </div>
    </fieldset>

    <fieldset class="cores">
        <legend>Cores</legend>
        <div class="cores-container">
            <div class="input-group">
                <input type="number" id="cores_min" size="2" min="0" step="1" placeholder="Min" />
                <label class="sr-only" for="cores_min">Min</label>
            </div>
            <div class="input-group">
                <input type="number" id="cores_max" size="2" min="0" step="1" placeholder="Max" />
                <label class="sr-only" for="cores_max">Max</label>
            </div>
        </div>
    </fieldset> -->

    <!-- Mobile search - only visible on mobile devices -->
    <div class="mobile-search-container">
        <input type="text" id="mobile-search" placeholder="Search CPUs..." />
        <button type="button" class="search-clear-button" id="mobile-search-clear">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
        </button>
    </div>

    <?php
    $sockets_json = file_get_contents(__DIR__ . '/data/sockets.json');
    $sockets_data = json_decode($sockets_json, true);

    // Desktop Sockets
    foreach (['Intel', 'AMD'] as $manufacturer) {
        $manufacturer_lower = strtolower($manufacturer);
        echo '<fieldset class="socket-group socket-group-desktop-' . $manufacturer_lower . '">';
        echo '<legend>';
        echo '<label><input type="checkbox" data-category="' . $manufacturer_lower . '-desktop" />' . $manufacturer . ' Desktop</label>';
        echo '</legend>';
        echo '<div class="socket-options">';

        // Get desktop sockets for current manufacturer from the grouped structure
        $sockets = $sockets_data['grouped']['desktop'][$manufacturer];

        foreach ($sockets as $socket) {
            echo '<label><input type="checkbox" data-category="' . $manufacturer_lower . '-desktop" ' .
                    'data-socket="' . $socket['slug'] . '" />' . $socket['name'] .
                    ' <span class="year">' . $socket['year'] . '</span></label>';
        }

        echo '</div>';
        echo '</fieldset>';
    }

    // Server Sockets
    foreach (['Intel', 'AMD'] as $manufacturer) {
        $manufacturer_lower = strtolower($manufacturer);
        echo '<fieldset class="socket-group socket-group-server-' . $manufacturer_lower . '">';
        echo '<legend>';
        echo '<label><input type="checkbox" data-category="' . $manufacturer_lower . '-server" />' . $manufacturer . ' Server</label>';
        echo '</legend>';
        echo '<div class="socket-options">';

        // Get server sockets for current manufacturer from the grouped structure
        $sockets = $sockets_data['grouped']['server'][$manufacturer];

        foreach ($sockets as $socket) {
            echo '<label><input type="checkbox" data-category="' . $manufacturer_lower . '-server" ' .
                    'data-socket="' . $socket['slug'] . '" />' . $socket['name'] .
                    ' <span class="year">' . $socket['year'] . '</span></label>';
        }

        echo '</div>';
        echo '</fieldset>';
    }
    ?>

    <!-- Show Results Button - Only visible when filters are toggled -->
    <button id="show-results-button" class="show-results-button">Show results</button>

    <fieldset class="last">This site is supported by paid affiliate links. <br><a href="/faq.php#affiliate">Learn more</a></fieldset>
</div>

<div id="cpuprices">
    <table>
        <thead>
            <tr id="cpuprices-head">
                <th class="sortable sort-desc" data-sort="value">Value</th>
                <th class="sortable" data-sort="price">Price</th>
                <th class="sortable" data-sort="mark">Mark</th>
                <th class="sortable" data-sort="cores">Cores</th>
                <th class="sortable" data-sort="tdp">TDP</th>
                <!-- <th>Warranty</th> -->
                <th class="sortable" data-sort="socket">Socket</th>
                <th class="sortable" data-sort="type">Category</th>
                <th class="sortable" data-sort="condition">Cond</th>
                <?php if ($test): ?>
                    <th class="sortable" data-sort="cpu">Matched CPU</th>
                <?php endif; ?>
                <th class="sortable" data-sort="name">Affiliate Link</th>
            </tr>
        </thead>
        <tbody id="cpuprices-body" lang="en">
            <?php
            $json_data = file_get_contents(__DIR__ . '/data/cpu_data.json');
            $cpu_data = json_decode($json_data, true);

            // Sort CPUs by value (CPU Mark / Price) - higher is better
            usort($cpu_data, function($a, $b) {
                // Handle empty values
                if (empty($a['price']) || empty($a['mark']) || empty($b['price']) || empty($b['mark'])) {
                    // If either CPU is missing price or mark, put it at the end
                    if (empty($a['price']) || empty($a['mark'])) return 1;
                    if (empty($b['price']) || empty($b['mark'])) return -1;
                    return 0;
                }

                // Calculate value (mark / price) for both CPUs
                $valueA = $a['mark'] / $a['price'];
                $valueB = $b['mark'] / $b['price'];

                // Sort descending (higher value is better)
                return $valueB <=> $valueA;
            });

            foreach ($cpu_data as $cpu) {
                // Skip CPUs without price or mark
                if (empty($cpu['price']) || $cpu['price'] === '' || $cpu['price'] === 0 || empty($cpu['mark'])) {
                    continue;
                }

                // Calculate value (mark / price)
                $value = $cpu['mark'] / $cpu['price'];

                // Format socket for data attribute (use socket_slug)
                $socketAttr = $cpu['socket_slug'];

                // Format condition (default to 'new' if not specified)
                $condition = ($cpu['condition'] !== '-') ? strtolower($cpu['condition']) : 'new';

                // Format CPU type/category for display
                $type = '';

                if (isset($cpu['type']) && !empty($cpu['type'])) {
                    // Convert to lowercase for consistent processing
                    $cpuType = strtolower($cpu['type']);

                    // Handle multiple types separated by commas
                    if (strpos($cpuType, ',') !== false) {
                        // Extract the primary type (first one in the list)
                        $types = explode(',', $cpuType);
                        $primaryType = trim($types[0]);
                        $type = ucfirst($primaryType); // Capitalize first letter
                    } else {
                        // Single type
                        $type = ucfirst($cpuType); // Capitalize first letter
                    }
                } else {
                    // Default type if not specified
                    $type = 'Unknown';
                }

                // Get core count and display format
                // Use core_display if available (for hybrid CPUs with P+E cores)
                // Otherwise use the cores field directly
                // The cores field already represents the total number of cores
                $totalCores = isset($cpu['cores']) ? $cpu['cores'] : 0;
                $coreDisplay = isset($cpu['core_display']) ? $cpu['core_display'] : $totalCores;

                echo '<tr class="cpu" data-socket="' . $socketAttr . '" data-condition="' . $condition . '" ' .
                        'data-value="' . number_format($value, 4, '.', '') . '" ' .
                        'data-price="' . $cpu['price'] . '" ' .
                        'data-mark="' . $cpu['mark'] . '" ' .
                        'data-cores="' . $totalCores . '" ' .
                        'data-tdp="' . $cpu['tdp'] . '" ' .
                        'data-type="' . $type . '" ' .
                        'data-cpu="' . htmlspecialchars($cpu['cpu'], ENT_QUOTES) . '" ' .
                        'data-name="' . htmlspecialchars($cpu['name'], ENT_QUOTES) . '">';
                // Format Value: 2 decimal places
                echo '<td data-label="Value">' . number_format($value, 2, '.', '') . '</td>';
                echo '<td data-label="Price">$' . number_format($cpu['price'], 0, '.', '') . '</td>';
                echo '<td data-label="Mark">' . number_format($cpu['mark'], 0, '', '') . '</td>';
                echo '<td data-label="Cores">' . $coreDisplay . '</td>';
                echo '<td data-label="TDP">' . $cpu['tdp'] . 'W</td>';
                // echo '<td data-label="Warranty">' . ($cpu['warranty'] !== '-' ? $cpu['warranty'] : 'N/A') . '</td>';
                echo '<td data-label="Socket">' . $cpu['socket'] . '</td>';
                echo '<td data-label="Type">' . $type . '</td>';
                echo '<td data-label="Condition">' . ucfirst($condition) . '</td>';
                if ($test) {
                    echo '<td data-label="Matched CPU">' . $cpu['cpu'] . '</td>';
                }
                // Build affiliate link with proper attributes
                $affiliateLink = $cpu['link'];

                // Add affiliate tag if not already present
                if (strpos($affiliateLink, 'tag=') === false && !empty($affiliate_us['tag'])) {
                    $affiliateLink .= (strpos($affiliateLink, '?') !== false ? '&' : '?') . 'tag=' . $affiliate_us['tag'];
                }

                // Add ref parameter if not already present
                if (strpos($affiliateLink, 'ref_=') === false && !empty($affiliate_us['ref_'])) {
                    $affiliateLink .= (strpos($affiliateLink, '?') !== false ? '&' : '?') . 'ref_=' . $affiliate_us['ref_'];
                }

                echo '<td class="name" data-label="Affiliate Link"><a href="' . $affiliateLink . '" target="_blank" rel="nofollow sponsored">' . $cpu['name'] . '</a></td>';
                echo '</tr>';
            }
            ?>
        </tbody>
    </table>

    <p id="disclosures">Last updated: 2025-04-10 00:30:00 UTC. <br><br>This site participates in the Amazon Associates Program. Prices and product availability are accurate as of the update time shown and are subject to change. The final price and availability will be displayed on Amazon at the time of purchase. Product information is provided by Amazon Services LLC and may be updated or modified at their discretion. All other content © CPU Prices. All rights reserved.</p>
</div>

<?php
// Footer already has ABSPATH check
require_once 'footer.php';
?>
